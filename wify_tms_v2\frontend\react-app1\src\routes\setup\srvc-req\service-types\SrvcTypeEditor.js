import React, { Component } from 'react';
import {
    Modal,
    Form,
    Radio,
    Input,
    Button,
    Popover,
    Steps,
    message,
    Collapse,
    Tabs,
    Checkbox,
    Select,
    Badge,
    Tag,
    Alert,
    Spin,
    Tooltip,
} from 'antd';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../../../util/http_utils';
import CircularProgress from '../../../../components/CircularProgress';
import IconSelectorWidget from '../../../../components/wify-utils/IconSelectorWidget';
import StatusesCreator, {
    defaultStatuses,
} from '../../../../components/wify-utils/StatusesCreator';
import FormPreviewMeta from '../../../../components/wify-utils/FieldCreator/FormPreviewMeta';
import {
    ZoomInOutlined,
    WhatsAppOutlined,
    MessageOutlined,
} from '@ant-design/icons';
import { Link } from 'react-router-dom';
import {
    parseSMSTemplateWithValues,
    status_type_templates_,
} from './consumer-sms-notification-template-manager';
import VisualJSONRulesEditor from '../../../devl-playground/VisualJSONRulesEditor';
import starterProactiveRulesSetMeta, {
    getFactsForServiceType,
} from './proactiveRulesHelper';
import {
    decodeAttachmentSectionsFrmJson,
    decodeFieldsMetaFrmJson,
    decodeFileSectionsFrmJson,
} from '../../../../components/wify-utils/FieldCreator/helpers';
import {
    getOptionsFrNatureOfServiceType,
    handleClearSelect,
    PROJECT_BASED_SERVICE_TYPE,
    BILLING_TYPE,
    validateLambdaArn,
} from '../../../../util/helpers';
import LineItemManagementModule from './project-components/LineItemManagementModule';
import PricingMaster from './PricingMaster';
import {
    status_type_templates_fr_whatsapp,
    parseWhatsappTemplateWithValues,
} from './consumer-whatsapp-notification-template-manager';
import TimePickerWidget from '../../../../components/wify-utils/TimePickerWidget';
import SpecificFieldsWiseNotificationModule from './project-components/SpecificFieldsWiseNotificationModule';
import BillingDiscountingConfigModule from './project-components/BillingDiscountingConfigModule';
import {
    addDefaultKeysForTabularView,
    getDefaultColumnsForTabularView,
    getRatingFormMeta,
    getSrvcReqStaticAndCustomPossibleFields,
    getStaticAndCustomFieldsFilter,
    getAuthorityLabelValuePairsWithPrefix,
} from '../../../services/helpers';
import ConfigHelpers from '../../../../util/ConfigHelpers';
import UserSelectorWidget from '../../../../components/wify-utils/UserSelectorWidget';
import {
    consumer_feedback_templates_fr_whatsapp,
    parseWhatsappFeedbackTemplateWithValues,
} from './consumer-whatsapp-feedback-template';
import { unitsFrCurrency } from '../../../../util/constants';
import GaiWrapper from '../../../../components/wify-utils/Gai/GaiWrapper';
import TimeBasedNotificationModule from './TimeBasedNotificationModule';
import PeriodicAutoModule from './periodic-automation/PeriodicAutoModule';
import InputTable from '../../../../components/WIFY/subtasks/InputTable';
import {
    consumer_feedback_templates_fr_sms,
    parseSmsFeedbackTemplateWithValues,
} from './consumer-sms-feedback-template';
import checkFeatureAccess from '../../../../util/FeatureAccess';
import InlineCheckbox from '../../../../components/WIFY/InlineCheckbox.tsx';

const protoUrl = '/setup/srvc-req/proto';
const submitUrl = '/setup/srvc-req';
const startOfDay = '12:00AM';
const endOfDay = '11:45PM';
const autoAssignAuthorityApiUrl = '/services/auto_authority_assignment';
const proceedWithCaution =
    process.env.REACT_APP_CAUTION_AHEAD_GIF_LINK ||
    'https://static.wixstatic.com/media/9e5e4a_d59e230c320e403394030acaddfe410f~mv2.gif/v1/fit/w_300%2Ch_200%2Cal_c%2Cq_80/file.gif';

const { Step } = Steps;
const { TabPane } = Tabs;
const { TextArea } = Input;

const isValidServiceKey = (value) => {
    // console.log('testing ',value);
    var serviceKeyComparator = /^[a-zA-Z_]+$/g;
    // console.log('testing result - ',serviceKeyComparator.test(value));
    return serviceKeyComparator.test(value);
};

const DailyUpdateModes = [
    {
        value: 'lenient_mode',
        label: 'Lenient mode(Progress is autofilled till the previous date and no restriction in progress slider movement)',
    },
    {
        value: 'strict_mode',
        label: 'Strict mode(Progress is autofilled till the previous date and cannot be modified out of permissible range)',
    },
];

class SrvcTypeEditor extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
    }
    initState = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
        currentStep: 0,
        templates: '',
        isPrefillRefreshDoneFrNotificationsTab: 0,
        srvcTypeSpecificFieldsNotification: undefined,
        applyingAutoAssignAuthorityFrRole: undefined,
        applyingAutoAssignAuthorityFrAllActiveReq: undefined,
        activeTabKey: 'help'
    };

    state = this.initState;

    componentDidMount() {
        this.initViewData();
        this.verifyFeatureAccess();
        this.verifyFeatureAccessTMS250312325161();
    }

    verifyFeatureAccess = async () => {
        try {
            let hasAccess = await checkFeatureAccess('TMS250414073641');
            this.setState({ TMS250414073641: hasAccess });
        } catch (error) {
            this.setState({ TMS250414073641: false });
            console.error(
                'SrvcTypeEditor :: verifyFeatureAccess :: error : ',
                error
            );
        }
    };

    verifyFeatureAccessTMS250312325161 = async () => {
        try {
            let hasAccess = await checkFeatureAccess('TMS250312325161');
            this.setState({ TMS250312325161: hasAccess });
        } catch (error) {
            this.setState({ TMS250312325161: false });
            console.error(
                'SrvcTypeEditor :: verifyFeatureAccessTMS250312325161 :: error : ',
                error
            );
        }
    };

    initViewData() {
        console.log('Trying to init view Data', this.state.visible);
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({
                isLoadingViewData: true,
            });
            var params = {};
            const onComplete = (resp) => {
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };
            var url = !this.state.editMode
                ? protoUrl
                : protoUrl + '/' + this.props.editorItem.id;
            // console.log(url);
            http_utils.performGetCall(url, params, onComplete, onError);
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                    }
                }
            );
        } else {
            if (this.state.refreshOnUpdate) {
                this.setState(
                    {
                        refreshOnUpdate: false,
                    },
                    this.initViewData()
                );
            }
        }
    }
    handleApplyToExistingRequestsClick = (singleSrvcAuthority) => {
        this.setState({
            applyingAutoAssignAuthorityFrRole: singleSrvcAuthority,
        });
        setTimeout(() => {
            this.setState({ applyingAutoAssignAuthorityFrRole: undefined });
        }, 1000);

        var params = {
            role_id: singleSrvcAuthority,
            srvc_type_id: this.props.editorItem.id,
        };
        const onComplete = (resp) => {
            this.setState({
                // isAutoAssignButtonClick : false,
                error: '',
            });
        };
        const onError = (error) => {
            this.setState({
                applyingAutoAssignAuthorityFrRole: undefined,
                error: http_utils.decodeErrorToMessage(error),
            });
            message.error(http_utils.decodeErrorToMessage(error));
        };

        http_utils.performPostCall(
            autoAssignAuthorityApiUrl,
            params,
            onComplete,
            onError
        );
    };

    handleApplyToAllActiveRequestsClick = (singleSrvcAuthority) => {
        this.setState({
            applyingAutoAssignAuthorityFrAllActiveReq: singleSrvcAuthority,
        });
        setTimeout(() => {
            this.setState({
                applyingAutoAssignAuthorityFrAllActiveReq: undefined,
            });
        }, 1000);

        var params = {
            role_id: singleSrvcAuthority,
            srvc_type_id: this.props.editorItem.id,
            is_apply_to_all_active_req: true,
        };
        const onComplete = (resp) => {
            this.setState({
                error: '',
            });
            if (resp.data == 'success') {
                message.success(
                    'Auto assignment will be done for all active requests'
                );
            }
        };
        const onError = (error) => {
            this.setState({
                applyingAutoAssignAuthorityFrAllActiveReq: undefined,
                error: http_utils.decodeErrorToMessage(error),
            });
            message.error(http_utils.decodeErrorToMessage(error));
        };

        http_utils.performPostCall(
            autoAssignAuthorityApiUrl,
            params,
            onComplete,
            onError
        );
    };

    handleOk = () => {
        this.setState({
            visible: false,
            isFormSubmitting: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({
            refreshOnUpdate: true,
            ...this.initState,
        });
    }

    tellParentToRefreshList(entry_id) {
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    submitForm = (data) => {
        this.setState({
            isFormSubmitting: true,
        });
        var params = data;
        const isSrvcStatusSelectedFrAutomation =
            this.formRef.current?.getFieldValue(
                'srvc_type_statuses_fr_automation'
            );
        if (isSrvcStatusSelectedFrAutomation) {
            const statusMovementRules =
                this.getRowDataForSubtaskStatusMovementRules();
            let decodedObj = {};
            if (statusMovementRules.length > 0) {
                // we need to modify
                statusMovementRules.map((singleRule) => {
                    decodedObj[singleRule.row_id] = singleRule;
                });
            }
            params['auto_move_sbtsk_rules'] = decodedObj;
        }
        // console.log("params",params)
        const isAutomationSrvcStatusEnable =
            this.formRef.current?.getFieldValue(
                'automation_srvc_status_enable'
            );
        if (isAutomationSrvcStatusEnable) {
            const rulesFrStatusMovement =
                this.getRowDataFrRuleTableFrSbtskStatusMovementBasedOnSrvcStatus();
            let decodedObj = {};
            if (rulesFrStatusMovement.length > 0) {
                // we need to modify
                rulesFrStatusMovement.map((singleRule) => {
                    decodedObj[singleRule.row_id] = singleRule;
                });
            }
            params['auto_sbtsk_status_movement_based_on_srvc_update_rules'] =
                decodedObj;
        }

        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
                error: '',
                visible: false,
            });
            this.tellParentToRefreshList(resp.entry_id);
            this.updateClosureToParent();
        };
        const onError = (error) => {
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };
        if (this.state.editMode) {
            http_utils.performPutCall(
                submitUrl + '/' + this.props.editorItem.id,
                params,
                onComplete,
                onError
            );
        } else {
            http_utils.performPostCall(submitUrl, params, onComplete, onError);
        }
    };

    refreshPageForNotificationMeta() {
        if (this.state.isPrefillRefreshDoneFrNotificationsTab < 2) {
            this.setState(
                {
                    isPrefillRefreshDoneFrNotificationsTab:
                        this.state.isPrefillRefreshDoneFrNotificationsTab + 1,
                },
                () => this.refreshPageForNotificationMeta()
            );
        }
    }

    getConsumerNotificationFieldsStepMeta() {
        let statusWiseFields = [];
        let statuses = this.getStatuses();
        let initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};
        if (statuses != undefined) {
            try {
                for (const status_type in statuses) {
                    if (Object.hasOwnProperty.call(statuses, status_type)) {
                        const statusesFrType = statuses[status_type];
                        statusesFrType.map((singleStatus) => {
                            statusWiseFields.push({
                                key: `srvc_type_status_${singleStatus.key}_fields`,
                                render: () => {
                                    return (
                                        <div className="gx-mb-4">
                                            {
                                                <div>
                                                    <Collapse
                                                        onChange={() => {
                                                            // alert('Hello')
                                                            this.refreshPageForNotificationMeta();
                                                        }}
                                                    >
                                                        <Collapse.Panel
                                                            key={
                                                                singleStatus.key
                                                            }
                                                            forceRender={true}
                                                            header={
                                                                <span>
                                                                    <i
                                                                        className="icon icon-circle gx-mr-3 gx-vertical-align-middle"
                                                                        style={{
                                                                            color: singleStatus.color,
                                                                        }}
                                                                    ></i>
                                                                    {
                                                                        singleStatus.title
                                                                    }
                                                                </span>
                                                            }
                                                        >
                                                            <>
                                                                <Tabs defaultActiveKey="sms">
                                                                    <TabPane
                                                                        tab={
                                                                            <span>
                                                                                SMS
                                                                                <MessageOutlined className="gx-ml-2" />
                                                                            </span>
                                                                        }
                                                                        key="sms"
                                                                        forceRender={
                                                                            true
                                                                        }
                                                                    >
                                                                        <FormBuilder
                                                                            meta={{
                                                                                ...this.getSmsNotificationMeta(
                                                                                    singleStatus.key
                                                                                ),
                                                                                initialValues:
                                                                                    initialValues,
                                                                            }}
                                                                            form={
                                                                                this
                                                                                    .formRef
                                                                            }
                                                                        />
                                                                    </TabPane>

                                                                    <TabPane
                                                                        tab={
                                                                            <span>
                                                                                Whatsapp
                                                                                <WhatsAppOutlined className="gx-ml-2 gx-text-green " />
                                                                            </span>
                                                                        }
                                                                        key="whatsapp_message"
                                                                        forceRender={
                                                                            true
                                                                        }
                                                                    >
                                                                        {/* <TabPane tab="Whatsapp" key="whatsapp_message" forceRender={true}> */}
                                                                        <FormBuilder
                                                                            meta={{
                                                                                ...this.getWhatsAppMessageNotificationMeta(
                                                                                    singleStatus.key
                                                                                ),
                                                                                initialValues:
                                                                                    initialValues,
                                                                            }}
                                                                            form={
                                                                                this
                                                                                    .formRef
                                                                            }
                                                                        />
                                                                    </TabPane>

                                                                    <div className="gx-d-none">
                                                                        <TabPane
                                                                            tab="EMAIL"
                                                                            key="email"
                                                                        >
                                                                            <FormBuilder
                                                                                meta={{
                                                                                    ...this.getEmailNotificationMeta(
                                                                                        singleStatus.key
                                                                                    ),
                                                                                    initialValues:
                                                                                        initialValues,
                                                                                }}
                                                                                form={
                                                                                    this
                                                                                        .formRef
                                                                                }
                                                                            />
                                                                        </TabPane>
                                                                    </div>
                                                                </Tabs>
                                                            </>
                                                        </Collapse.Panel>
                                                    </Collapse>
                                                </div>
                                            }
                                        </div>
                                    );
                                },
                            });
                        });
                    }
                }
            } catch (error) {
                console.log(
                    'Error while createing dynamic stage wise fields',
                    error
                );
            }
        }
        const meta = {
            // formItemLayout: null,
            // initialValues : this.state.editMode ? this.state.viewData?.form_data : {},
            fields: [...statusWiseFields],
        };
        return meta;
    }

    getAuthoritiesNotificationFieldsStepMeta() {
        let srvcTypeStatusWiseFields = [];
        let statuses = this.getStatuses();
        if (statuses != undefined) {
            try {
                for (const status_type in statuses) {
                    if (Object.hasOwnProperty.call(statuses, status_type)) {
                        const statusesFrType = statuses[status_type];
                        statusesFrType.map((singleStatus) => {
                            srvcTypeStatusWiseFields.push({
                                label: (
                                    <div>
                                        <span
                                            style={{
                                                height: '11px',
                                                width: '11px',
                                                display: 'inline-block',
                                                background: `${singleStatus.color}`,
                                            }}
                                            className="gx-mr-2 gx-rounded-circle"
                                        ></span>
                                        When updated as{' '}
                                        <b>{singleStatus.title}</b> notify
                                    </div>
                                ),
                                key: `srvc_type_status_${singleStatus.key}_notify_authorties`,
                                widget: 'select',
                                options: this.state.viewData?.roles_list,
                                widgetProps: {
                                    mode: 'multiple',
                                    allowClear: true,
                                    showSearch: true,
                                    optionFilterProp: 'children',
                                },
                            });
                        });
                    }
                }
            } catch (error) {
                console.log(
                    'Error while createing dynamic stage for notification fields',
                    error
                );
            }
        }
        const meta = {
            // formItemLayout: null,
            initialValues: this.state.editMode
                ? this.state.viewData?.form_data
                : {},
            fields: [...srvcTypeStatusWiseFields],
        };
        return meta;
    }
    getConfigFieldsFrBrand() {
        let fields = [];
        if (!ConfigHelpers.isServiceProvider()) {
            fields = [
                {
                    key: 'rename_specific_details_label',
                    label: 'Rename Specific Details Label',
                    widget: 'input',
                },
            ];
        }
        return fields;
    }

    getGaiConfigMeta() {
        // let fields = [];

        return {
            fields: [
                {
                    key: 'gai_rating_alert',
                    render: () => {
                        return (
                            <div className="gx-p-2">
                                <GaiWrapper
                                    gaiStyle={`gx-text-left gx-mb-3`}
                                    gaiWrapperStyle={`wy-flex-clock wy-gap-10`}
                                >
                                    <img
                                        src="https://static.wify.co.in/images/website/tms/ai_3d.png"
                                        alt="AI Icon for TMS"
                                        className="wy-icon-ai"
                                    />
                                    Explore G-AI configurations here for a
                                    tailored and intelligent experience in every
                                    output!
                                </GaiWrapper>
                                <p>
                                    Enabling this configuration generates
                                    AI-powered task ratings to optimize your
                                    field service operations. When enabled for a
                                    subtask type, a rating will be generated
                                    based on various factors, including:
                                </p>
                                <ul>
                                    <li>
                                        <strong>Task complexity: </strong> The
                                        system analyzes the steps involved and
                                        technical skills required.
                                    </li>
                                    <li>
                                        <strong>Estimated time: </strong>Planned
                                        vs actual time of subtask completion
                                    </li>
                                    <li>
                                        <strong>Task updates: </strong>
                                        Photos/videos uploaded on the subtask
                                    </li>
                                </ul>
                            </div>
                        );
                    },
                },
                {
                    label: 'Enable GAI ratings for subtasks',
                    key: 'gai_rating_subtasks',
                    widget: 'checkbox',
                    onChange: () =>
                        this.setState({
                            render_helper: !this.state.render_helper,
                        }),
                },
            ],
        };
    }

    getConfigurationStepMeta() {
        let initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};

        let FieldsInitialValues = initialValues;
        // if(initialValues?.srvc_cust_fields_json == undefined
        //     || initialValues?.srvc_cust_fields_json == ""){
        //     FieldsInitialValues = {};
        // }
        var customFields = this.formRef?.current?.getFieldValue(
            'srvc_cust_fields_json'
        );
        // console.log('Customer fields',customFields);
        if (customFields == undefined) {
            customFields = initialValues?.srvc_cust_fields_json;
        }
        if (
            initialValues &&
            initialValues['deployment_time_slot_lower_limit'] == undefined
        ) {
            initialValues['deployment_time_slot_lower_limit'] = startOfDay;
            initialValues['deployment_time_slot_upper_limit'] = endOfDay;
        }
        //Set default table columns for srvc_type_tabular_view_columns
        if (
            initialValues?.hasOwnProperty('srvc_type_tabular_view_columns') ==
            false
        ) {
            initialValues['srvc_type_tabular_view_columns'] =
                addDefaultKeysForTabularView();
        }
        const startTimeFrEndTime = this.formRef.current?.getFieldValue(
            'sbtsk_time_slot_lower_limit'
        );
        let areCustomFieldsDynamicFieldKey = 'srvc_is_cust_fields_dynamic';
        let dynamicFormEnabled =
            this.formRef?.current?.getFieldValue(
                areCustomFieldsDynamicFieldKey
            ) == undefined
                ? initialValues?.[areCustomFieldsDynamicFieldKey]
                : this.formRef?.current?.getFieldValue(
                      areCustomFieldsDynamicFieldKey
                  );
        let sbtskTimeFieldsMeta = [];
        if (!this.isProjectNature()) {
            sbtskTimeFieldsMeta.push(
                {
                    key: 'sbtsk_time_slot_lower_limit',
                    label: (
                        <p>
                            <i className="icon icon-timepicker gx-mr-2"></i>
                            Start Time Slot(Lower Limit)
                        </p>
                    ),
                    widget: TimePickerWidget,
                    widgetProps: {
                        beginLimit: startOfDay,
                        endLimit: endOfDay,
                        step: 15,
                        onChange: (e) => {
                            this.refresh();
                        },
                    },
                    colSpan: 2,
                },
                {
                    key: 'sbtsk_time_slot_upper_limit',
                    label: (
                        <p>
                            <i className="icon icon-timepicker gx-mr-2"></i>End
                            Time Slot(Upper Limit)
                        </p>
                    ),
                    widget: TimePickerWidget,
                    widgetProps: {
                        beginLimit: startTimeFrEndTime || startOfDay,
                        endLimit: endOfDay,
                        step: 15,
                    },
                    colSpan: 2,
                }
            );
        }
        const tabItems = [
            {
                tab: 'Help',
                key: 'help',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    fields: [
                        {
                            key: 'help_tab_text',
                            render: () => (
                                <div>
                                    <img
                                        className="gx-ml-3"
                                        src={proceedWithCaution}
                                    />
                                    <ul className="gx-mt-2">
                                        <li>
                                            <b>
                                                Please make sure you know what
                                                you are doing!
                                            </b>
                                        </li>
                                        <li>
                                            You are editing a service type's
                                            configuration
                                        </li>
                                        <li>
                                            Doing any changes here will largely
                                            impact how the service requests are
                                            created and updated
                                        </li>
                                    </ul>
                                </div>
                            ),
                        },
                    ],
                },
            },
            {
                tab: 'Statuses',
                key: 'statuses',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    fields: [
                        {
                            label: 'Status',
                            key: 'srvc_statuses',
                            formItemProps: {
                                style: {
                                    display: 'none',
                                },
                            },
                        },
                        {
                            key: 'service_statuses_editor',
                            render: () => {
                                return (
                                    <div className="gx-jumbotron gx-border-amber gx-border-2 gx-p-3">
                                        <StatusesCreator
                                            initStatuses={this.getStatuses()}
                                            onJSONChanged={(newObject) => {
                                                this.formRef.current.setFieldsValue(
                                                    {
                                                        srvc_statuses:
                                                            JSON.stringify(
                                                                newObject
                                                            ),
                                                    }
                                                );
                                                // this.forceUpdate() // BUG fix not able to edit text continously
                                            }}
                                        />
                                        <p>
                                            ** If a status is deleted, then any
                                            request lying in the status will be
                                            moved to previous status
                                        </p>
                                    </div>
                                );
                            },
                        },
                    ],
                },
            },
            {
                tab: 'Fields',
                key: 'fields',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: FieldsInitialValues,
                    fields: [
                        {
                            render: () => {
                                return <hr />;
                            },
                        },
                        {
                            label: 'Enter custom fields json',
                            key: 'srvc_cust_fields_json',
                            widget: 'textarea',
                            placeholder: 'Please paste the JSON here',
                            onChange: (event) => {
                                this.setState({
                                    render_helper: !this.state.render_helper,
                                });
                                this.forceUpdate();
                            },
                        },
                        {
                            key: 'srvc_cust_fields_json_colspan',
                            widget: 'number',
                            widgetProps: {
                                min: 1,
                                defaultValue: 2,
                            },
                            label: 'Column span (Default is 2 for rendering a grid)',
                            tooltip:
                                'If you want your fields to be rendered in a grid',
                        },
                        {
                            key: 'link_to_field_creator',
                            render: () => {
                                return (
                                    <div className="gx-mb-3">
                                        {customFields != '' && (
                                            <div>
                                                <Button
                                                    onClick={(e) =>
                                                        this.handleShowFieldsPreviewClick(
                                                            customFields
                                                        )
                                                    }
                                                    icon={<ZoomInOutlined />}
                                                >
                                                    Form preview
                                                </Button>
                                            </div>
                                        )}
                                        <Link
                                            to={
                                                '/fields-creator?edit=' +
                                                encodeURIComponent(customFields)
                                            }
                                            target="_blank"
                                        >
                                            Open field creator {'-->'}
                                        </Link>
                                    </div>
                                );
                            },
                        },
                        ...this.getConfigFieldsFrBrand(),
                        {
                            label: 'Service category field',
                            key: `srvc_category_field`,
                            widget: 'select',
                            options: this.getPossibleSpecificFields(
                                false,
                                [],
                                false,
                                [
                                    {
                                        widget: 'select',
                                    },
                                ]
                            ),
                            widgetProps: {
                                allowClear: true,
                                onChange: (value) => {
                                    handleClearSelect(
                                        value,
                                        this.formRef,
                                        'srvc_category_field'
                                    );
                                },
                            },
                        },
                        {
                            key: 'alternate_number_fields',
                            label: `Select alternate number fields`,
                            widget: 'select',
                            widgetProps: {
                                mode: 'multiple',
                            },
                            options: this.getPossibleNumberFields(),
                            onChange: () => this.refresh(),
                        },
                        ...sbtskTimeFieldsMeta,
                        {
                            key: areCustomFieldsDynamicFieldKey,
                            label: `Are custom fields dynamic ?`,
                            widget: 'checkbox',
                            tooltip:
                                'If you are planning to render the fields in a dynamic way, eg: If field 1 needs to be hidden based on value of field 2 and so on... You can code the entire logic in a lambda function and refer it here',
                            onChange: () => this.refresh(),
                        },
                        ...(dynamicFormEnabled
                            ? [
                                  {
                                      key: `srvc_cust_fields_dynamic_form_lambda_arn`,
                                      label: `Enter custom fields lambda arn`,
                                      placeholder: 'Lambda ARN',
                                      required: true,
                                      rules: [
                                          {
                                              validator: validateLambdaArn,
                                              message:
                                                  'Please enter a valid Lambda ARN',
                                          },
                                      ],
                                  },
                              ]
                            : []),
                        ...this.srvcReqStatusUpdateLambdaMeta(),
                        {
                            render: () => {
                                return <hr />;
                            },
                        },
                        ...this.getFieldsCongigDetails(initialValues),
                    ],
                },
            },
            {
                tab: 'Service Provider',
                key: 'assignment',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getDynamicStep3Meta(),
                },
            },
            {
                tab: 'Proactive',
                key: 'proactive',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getProActiveMeta(),
                },
            },
            {
                tab: 'Reporting',
                key: 'reporting',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getDynamicStep4Meta(),
                },
            },
            {
                tab: 'Entities',
                key: 'entities',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getDynamicStep2Meta(),
                },
            },
            {
                tab: 'Ratings',
                key: 'rating',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getRatingConfigMeta(initialValues),
                },
            },
            ...(this.isProjectNature()
                ? []
                : [
                      {
                          tab: 'GAI',
                          key: 'gai',
                          forceRender: true,
                          formMeta: {
                              formItemLayout: null,
                              initialValues,
                              ...this.getGaiConfigMeta(),
                          },
                      },
                  ]),
                {
                    tab: 'Line Items',
                    key: 'line_items',
                    forceRender: true,
                    formMeta: {
                        formItemLayout: null,
                        initialValues: initialValues,
                        ...this.getLineItemsConfigMeta(),
                    },
                },
            ...(this.isProjectNature()
                ? [
                      {
                          tab: 'Deployment',
                          key: 'deployment',
                          formMeta: {
                              formItemLayout: null,
                              initialValues: initialValues,
                              ...this.getDeploymentConfigMeta(),
                          },
                      },
                      {
                          tab: 'Daily updates',
                          key: 'daily_updates',
                          formMeta: {
                              formItemLayout: null,
                              initialValues: initialValues,
                              ...this.getDailyUpdatesMeta(),
                          },
                      },
                  ]
                : []),
            {
                tab: 'Pricing Master',
                key: 'pricing_master',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getPricingMasterConfigMeta(initialValues),
                },
            },
            {
                tab: 'Billing',
                key: 'billing',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getBillingMasterConfigMeta(initialValues),
                },
            },
            {
                tab: 'Automations',
                key: 'automation_movement_fields',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getAutoMovementFieldsConfigMeta(initialValues),
                },
            },
            {
                tab: 'Tabular view',
                key: 'tabular_view',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getTabularViewFieldsConfigMeta(),
                },
            },
            {
                tab: 'Views',
                key: 'filter_views',
                forceRender: true,
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getFilterViewFieldsConfigMeta(),
                },
            },
        ];
        const meta = {
            fields: [
                {
                    key: 'configuration_tabs_wrapper',
                    render: () => (
                        <Tabs
                            type="card"
                            onChange={(activeKey) => {
                                this.setState({ activeTabKey: activeKey });
                                this.forceUpdate();
                            }}
                        >
                            {tabItems.map((tabItem) => (
                                <Tabs.TabPane
                                    {...tabItem}
                                    // forceRender={true}
                                >
                                    <FormBuilder
                                        meta={tabItem.formMeta}
                                        form={this.formRef}
                                    />
                                </Tabs.TabPane>
                            ))}
                        </Tabs>
                    ),
                },
            ],
        };

        return meta;
    }

    getFieldsCongigDetails(initialValues) {
        let fieldsConfigDetailds = [
            {
                render: () => {
                    return (
                        <div className="gx-mt-4">
                            <Tabs
                                defaultActiveKey="collapsable_specific_fields"
                                className="gx-mb-2"
                            >
                                <Tabs.TabPane
                                    tab="Collapsable specific fields"
                                    key="collapsable_specific_fields"
                                    forceRender={true}
                                >
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getCollapsableSpecificFieldsMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>

                                <Tabs.TabPane
                                    tab="Configure status wise mandatory fields"
                                    key="configure_status_wise_mandatory_fields"
                                    forceRender={true}
                                >
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getMandatoryStatusFieldsMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        </div>
                    );
                },
            },
        ];
        return fieldsConfigDetailds;
    }

    getStatuses() {
        // console.log("Tried calling get statuses");
        // console.log("FormRef",this.formRef?.current?.getFieldValue('srvc_statuses'));
        var statusesFrmFormField =
            this.formRef?.current?.getFieldValue('srvc_statuses');
        // console.log("statusesFrmFormField",statusesFrmFormField);
        if (statusesFrmFormField != undefined) {
            try {
                statusesFrmFormField = JSON.parse(statusesFrmFormField);
                // console.log("Successfully got statuses from");
            } catch (e) {}
        }
        return statusesFrmFormField;
    }

    getProactiveRulesetMeta() {
        let prefix = 'pro_active';
        let sourceTypeSelected = this.formRef?.current?.getFieldValue(
            `${prefix}_source_srvc_type`
        );
        // console.log("Tried calling get statuses");
        // console.log("FormRef",this.formRef?.current?.getFieldValue('pro_active_form_data'));
        var proactiveRulesetMeta = this.formRef?.current?.getFieldValue(
            `pro_active_form_data_fr_${this.props.editorItem.id}_${sourceTypeSelected}`
        ); //
        //var proactiveRulesetMeta = this.formRef?.current?.getFieldValue('pro_active_form_data');
        // console.log("proactiveRulesetMeta",proactiveRulesetMeta);
        if (proactiveRulesetMeta != undefined) {
            try {
                let rulesetMetaInDb = JSON.parse(proactiveRulesetMeta);
                proactiveRulesetMeta = starterProactiveRulesSetMeta;
                proactiveRulesetMeta[0].outcomes = rulesetMetaInDb[0].outcomes;
                // console.log("Successfully got statuses from");
            } catch (e) {}
        } else {
            proactiveRulesetMeta = starterProactiveRulesSetMeta;
            proactiveRulesetMeta[0].outcomes = [];
        }
        let selectedSrcSrvcTypeValue = this.formRef?.current?.getFieldValue(
            'pro_active_source_srvc_type'
        );
        if (selectedSrcSrvcTypeValue) {
            let possibleSrcSrvcTypes = this.getPossibleSrcSrvcTypes();
            let selectedSrcSrvcType = possibleSrcSrvcTypes.filter(
                (srvcType) => selectedSrcSrvcTypeValue === srvcType.value
            )[0];
            proactiveRulesetMeta[0].attributes = [
                ...getFactsForServiceType(
                    selectedSrcSrvcType?.config_data,
                    proactiveRulesetMeta[0].attributes
                ),
            ];
        }
        return proactiveRulesetMeta;
    }

    getStepsMeta() {
        const initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};
        const steps = [
            {
                title: 'Basic',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getDynamicStep1Meta(),
                },
            },
            {
                title: 'Configuration',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getConfigurationStepMeta(),
                },
            },
            {
                title: 'Notification',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getNotificationFieldsStepMeta(),
                },
            },
            // {
            //     title: 'Notification',
            //     formMeta: {
            //         formItemLayout: null,
            //         initialValues: initialValues,
            //         ...this.getNotificationFieldsStepMeta(),
            //     }
            // }
        ];
        return steps;
    }

    getNotificationFieldsStepMeta() {
        let initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};
        const tabItems = [
            {
                tab: 'Status wise',
                key: 'status_wise',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getStatusWiseConsumerNotification(),
                },
            },
            {
                tab: 'Field wise',
                key: 'field_wise',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getFieldWiseAuthorityNotification(),
                },
            },
            {
                tab: 'Event based',
                key: 'event_based',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getEventBasedMeta(initialValues),
                },
            },
            {
                tab: 'Time based',
                key: 'Time_based',
                formMeta: {
                    formItemLayout: null,
                    initialValues: initialValues,
                    ...this.getTimeBasedNotification(initialValues),
                },
            },
        ];
        const meta = {
            fields: [
                {
                    key: 'configuration_notification_tabs_wrapper',
                    render: () => (
                        <Tabs
                            type="card"
                            onChange={(activeKey) => {
                                this.forceUpdate();
                            }}
                        >
                            {tabItems.map((tabItem) => (
                                <Tabs.TabPane
                                    {...tabItem}
                                    // forceRender={true}
                                >
                                    <FormBuilder
                                        meta={tabItem.formMeta}
                                        form={this.formRef}
                                    />
                                </Tabs.TabPane>
                            ))}
                        </Tabs>
                    ),
                },
            ],
        };
        return meta;
    }

    getStatusWiseConsumerNotification(initialValues) {
        return {
            fields: [
                {
                    key: `srvc_type_status_wise_consumer_notification_config`,
                    render: () => {
                        return (
                            <Tabs
                                defaultActiveKey="consumer"
                                className="gx-mb-2"
                            >
                                <Tabs.TabPane tab="Consumer" key="consumer">
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getConsumerNotificationFieldsStepMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    tab="Authorities"
                                    key="authorities"
                                >
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getAuthoritiesNotificationFieldsStepMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }

    getFieldWiseAuthorityNotification(initialValues) {
        return {
            fields: [
                {
                    key: `srvc_type_field_wise_authority_notification_config`,
                    render: () => {
                        return (
                            <Tabs
                                defaultActiveKey="authority"
                                className="gx-mb-2"
                            >
                                <Tabs.TabPane tab="Authority" key="authority">
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getSpecificFieldsWiseNotificationFieldsMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }
    getTimeBasedNotification(initialValues) {
        return {
            fields: [
                {
                    key: `srvc_type_time_based_notification_config`,
                    render: () => {
                        return (
                            <FormBuilder
                                form={this.formRef}
                                meta={this.getTimeBasedNotificationsFieldsMeta(
                                    initialValues
                                )}
                            />
                        );
                    },
                },
            ],
        };
    }
    getEventBasedMeta(initialValues) {
        return {
            fields: [
                {
                    key: `srvc_type_field_wise_authority_notification_config`,
                    render: () => {
                        return (
                            <div className="gx-mb-2 gx-ml-2">
                                <div>
                                    <Collapse>
                                        <Collapse.Panel
                                            forceRender={true}
                                            header={
                                                <span>
                                                    Service Request deletion
                                                </span>
                                            }
                                        >
                                            <>
                                                <FormBuilder
                                                    meta={{
                                                        ...this.getMetaFrSingleStatusWise(
                                                            'srvc_req_deletion'
                                                        ),
                                                        initialValues:
                                                            initialValues,
                                                    }}
                                                    form={this.formRef}
                                                />
                                            </>
                                        </Collapse.Panel>
                                        <Collapse.Panel
                                            forceRender={true}
                                            header={
                                                <span>
                                                    Service Request closure
                                                </span>
                                            }
                                        >
                                            <>
                                                <FormBuilder
                                                    meta={{
                                                        ...this.getMetaFrSingleStatusWise(
                                                            'srvc_req_closure'
                                                        ),
                                                        initialValues:
                                                            initialValues,
                                                    }}
                                                    form={this.formRef}
                                                />
                                            </>
                                        </Collapse.Panel>
                                    </Collapse>
                                </div>
                            </div>
                        );
                    },
                },
            ],
        };
    }
    getMetaFrSingleStatusWise = (singleStatus) => {
        let meta = [];
        let lambdaHookEnabledFrStatus =
            this.formRef?.current?.getFieldValue(
                `enable_lambda_hook_fr_${singleStatus}`
            ) ||
            this.state.viewData?.form_data[
                `enable_lambda_hook_fr_${singleStatus}`
            ];
        if (lambdaHookEnabledFrStatus) {
            meta.push({
                key: `lambda_arn_fr_${singleStatus}`,
                label: ' Lambda ARN ',
                widget: 'input',
                required: true,
                rules: [
                    {
                        validator: validateLambdaArn,
                        message: 'Please enter a valid Lambda ARN',
                    },
                ],
            });
        }
        return {
            fields: [
                {
                    key: `enable_lambda_hook_fr_${singleStatus}`,
                    label: 'Enable lambda hook',
                    widget: 'checkbox',
                    onChange: () => {
                        this.refresh();
                    },
                },
                ...meta,
            ],
        };
    };

    getSpecificFieldsWiseNotificationFieldsMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Service type specific fields wise notification',
                    key: 'srvc_type_specific_fields_wise_notification',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `srvc_type_fields_wise_notification`,
                    render: () => {
                        return (
                            <SpecificFieldsWiseNotificationModule
                                srvc_type_id={this.props.editorItem.id}
                                authorities_list={
                                    this.state.viewData?.authorities_list
                                }
                                selected_authorities_list={this.formRef?.current?.getFieldValue(
                                    'srvc_authorities'
                                )}
                                specific_fields={this.formRef?.current?.getFieldValue(
                                    'srvc_cust_fields_json'
                                )}
                                initialValue={this.getSpecificFieldsNotificationJson()}
                                onChange={(newObj) => {
                                    // console.log("newObj",newObj);
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_specific_fields_wise_notification:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                            />
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getSpecificFieldsNotificationJson() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_specific_fields_wise_notification'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            let initialFieldJson =
                this.state.viewData?.form_data
                    ?.srvc_type_specific_fields_wise_notification;
            if (initialFieldJson) {
                return JSON.parse(initialFieldJson);
            }
        }
        return {};
    }

    getTimeBasedNotificationsFieldsMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Service type time based wise notification',
                    key: 'srvc_type_time_based_wise_notification',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `srvc_type_time_based_notification`,
                    render: () => {
                        return (
                            <TimeBasedNotificationModule
                                srvc_type_id={this.props.editorItem.id}
                                roles_ids={this.state.viewData?.roles_list}
                                initialValue={this.getTimeBasedNotificationJson()}
                                onChange={(newObj) => {
                                    // console.log("newObj",newObj);
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_time_based_wise_notification:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                                statuses={this.getStatuses()}
                            />
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getTimeBasedNotificationJson() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_time_based_wise_notification'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            let initialFieldJson =
                this.state.viewData?.form_data
                    ?.srvc_type_time_based_wise_notification;
            if (initialFieldJson) {
                return JSON.parse(initialFieldJson);
            }
        }
        return {};
    }
    getPeriodicAutomationJson() {
        // console.info(
        //     this.state.viewData?.form_data?.srvc_type_periodic_automation,
        //     'adiiii'
        // );
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_periodic_automation'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            let initialFieldJson =
                this.state.viewData?.form_data?.srvc_type_periodic_automation;
            if (initialFieldJson) {
                return JSON.parse(initialFieldJson);
            }
        }
        return {};
    }
    getMandatoryStatusFieldsMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [...this.mandatoryStatusFieldsMeta()],
        };
        return meta;
    };

    getCollapsableSpecificFieldsMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [...this.collapsableSpecificFieldsMetaFrStatus()],
        };
        return meta;
    };

    collapsableSpecificFieldsMetaFrStatus() {
        const status_prefix_key = 'collapsable_specific_fields_fr_status';
        let statusWiseFields = [];
        let statuses = this.getStatuses();
        if (statuses != undefined) {
            try {
                for (const status_type in statuses) {
                    if (Object.hasOwnProperty.call(statuses, status_type)) {
                        let statusesFrType = statuses[status_type];
                        statusesFrType.map((singleStatus) => {
                            statusWiseFields.push(
                                {
                                    label: singleStatus.title,
                                    key: `${status_prefix_key}_${singleStatus.key}`,
                                    widget: 'select',
                                    options:
                                        this.getPossibleSpecificFieldsFrCollapsable(
                                            status_prefix_key,
                                            singleStatus.key
                                        ),
                                    onChange: () => {
                                        this.forceUpdate();
                                    },
                                    widgetProps: {
                                        allowClear: true,
                                        mode: 'multiple',
                                    },
                                },
                                {
                                    label: `Label for ${singleStatus.title}`,
                                    key: `${status_prefix_key}_${singleStatus.key}_label`,
                                    widget: 'input',
                                    widgetProps: {
                                        allowClear: true,
                                    },
                                },
                                {
                                    render: () => {
                                        return <hr />;
                                    },
                                }
                            );
                        });
                    }
                }
            } catch (error) {
                console.log(
                    'Error while collapsableSpecificFieldsMetaFrStatus fields failed',
                    error
                );
            }
        }
        return statusWiseFields;
    }

    getPossibleSpecificFieldsFrCollapsable(status_prefix_key, statusKey) {
        let statuses = this.getStatuses();
        let statusesKeysArray = [];
        for (const status_type in statuses) {
            if (Object.hasOwnProperty.call(statuses, status_type)) {
                const statusesFrType = statuses[status_type];
                let filteredFields = [];
                filteredFields = statusesFrType.filter(
                    (singleStatusesFrType) =>
                        singleStatusesFrType.key != statusKey
                );
                statusesKeysArray = [...statusesKeysArray, ...filteredFields];
            }
        }
        let selectedCustomFields = [];
        statusesKeysArray.forEach((singleStatusesKey) => {
            let selectedCustomFieldsFrStatus =
                this.formRef?.current?.getFieldValue(
                    `${status_prefix_key}_${singleStatusesKey.key}`
                );
            if (selectedCustomFieldsFrStatus == undefined) {
                selectedCustomFieldsFrStatus =
                    this.state.viewData?.form_data[
                        `${status_prefix_key}_${singleStatusesKey.key}`
                    ];
            }
            if (
                selectedCustomFieldsFrStatus &&
                selectedCustomFieldsFrStatus.length != 0
            ) {
                selectedCustomFields.push(...selectedCustomFieldsFrStatus);
            }
        });

        let labelValuePairData = this.getPossibleSpecificFields(
            false,
            selectedCustomFields
        );
        return labelValuePairData;
    }
    srvcReqStatusUpdateLambdaMeta() {
        let lambdaArn = [];
        let enableLambdaFrStatusUpdate = this.formRef?.current?.getFieldValue(
            `enable_lambda_for_status_update`
        );
        if (enableLambdaFrStatusUpdate) {
            lambdaArn.push({
                key: `service_status_movement_lambda_arn`,
                label: 'Lambda ARN',
                placeholder: 'Eg: Enter Lambda ARN',
                required: true,
            });
        }
        let statusUpdateLambdaFields = [
            {
                key: 'heading_status_update_lambda',
                render: () => {
                    return (
                        <div className="gx-mt-4">
                            <h4>Configure status update lambda</h4>
                        </div>
                    );
                },
            },
            {
                key: `enable_lambda_for_status_update`,
                label: 'Enable lambda for status update',
                widget: 'checkbox',
                onChange: () => this.refresh(),
            },
            ...lambdaArn,
        ];
        return statusUpdateLambdaFields;
    }

    mandatoryStatusFieldsMeta() {
        const status_prefix_key = 'mandatory_fields_fr_status';
        let statusWiseFields = [
            {
                key: 'heading_mandatory_status',
                render: () => {
                    return (
                        <div className="gx-mt-4">
                            <h4>Configure status wise mandatory fields</h4>
                        </div>
                    );
                },
            },
        ];
        let statuses = this.getStatuses();
        if (statuses != undefined) {
            try {
                for (const status_type in statuses) {
                    if (Object.hasOwnProperty.call(statuses, status_type)) {
                        let statusesFrType = statuses[status_type];
                        statusesFrType = statusesFrType?.filter(
                            (item) => item.key != 'open'
                        );
                        statusesFrType.map((singleStatus) => {
                            statusWiseFields.push({
                                label: singleStatus.title,
                                key: `${status_prefix_key}_${singleStatus.key}`,
                                widget: 'select',
                                options: this.getPossibleSpecificFields(
                                    true,
                                    []
                                ),
                                widgetProps: {
                                    mode: 'multiple',
                                },
                            });
                        });
                    }
                }
            } catch (error) {
                console.log(
                    'Error while createing dynamic stage wise fields',
                    error
                );
            }
        }
        return statusWiseFields;
    }

    getPossibleSpecificFields(
        Files = false,
        selectedCustomFields = [],
        onlyAttachments = false,
        essentialFields = []
    ) {
        var customFields =
            this.state.viewData?.form_data?.srvc_cust_fields_json;
        let translatedFields = Files
            ? decodeFieldsMetaFrmJson(customFields, undefined, true)
            : decodeFieldsMetaFrmJson(customFields);
        // console.log("labelValuePairData",translatedFields)
        let labelValuePairData = [];
        if (translatedFields) {
            translatedFields.map((singleField) => {
                if (
                    !Files ||
                    (Files && !selectedCustomFields.includes(singleField.key))
                ) {
                    if (
                        essentialFields.length == 0 ||
                        (this.checkIfPresentInEssentialField(
                            singleField,
                            essentialFields
                        ) &&
                            !onlyAttachments)
                    ) {
                        labelValuePairData.push({
                            label: singleField.label,
                            value: singleField.key,
                            key: singleField.key,
                        });
                    } else if (
                        ((essentialFields.length == 0 ||
                            this.checkIfPresentInEssentialField(
                                singleField,
                                essentialFields
                            )) &&
                            !singleField.cust_component) ||
                        singleField?.cust_component == 'Files'
                    ) {
                        labelValuePairData.push({
                            label: singleField.label,
                            value: singleField.key,
                            key: singleField.key,
                        });
                    }
                }
            });
        }
        // console.log("labelValuePairData",labelValuePairData)
        return labelValuePairData;
    }
    getPossibleNumberFields(Files = false) {
        var customFields =
            this.state.viewData?.form_data?.srvc_cust_fields_json;
        let translatedFields = Files
            ? decodeFieldsMetaFrmJson(customFields, undefined, true)
            : decodeFieldsMetaFrmJson(customFields);
        // console.log("labelValuePairData",translatedFields)
        let labelValuePairData = [];
        if (translatedFields) {
            translatedFields.map((singleField) => {
                if (singleField?.cust_component == 'Mobile') {
                    //if((essentialFields.length == 0 || this.checkIfPresentInEssentialField(singleField,essentialFields) && !onlyAttachments)){
                    labelValuePairData.push({
                        label: singleField.label,
                        value: singleField.key,
                        key: singleField.key,
                    });
                }
            });
        }
        // console.log("labelValuePairData",labelValuePairData)
        return labelValuePairData;
    }

    checkIfPresentInEssentialField = (singleField, essentialFields) => {
        for (let essentialField of essentialFields) {
            if (
                singleField.widget == essentialField.widget &&
                singleField.widgetProps.mode != 'multiple'
            ) {
                return true;
            }
        }
    };

    getPossibleAuthorities(selectedAuthorities) {
        let srvcTypeAuthorities =
            this.formRef.current?.getFieldValue('srvc_authorities');
        let allAuthoritiesList = this.state.viewData?.authorities_list;

        // gets all the authorities that are in the service type
        //Get authorities that are not selected in any other status
        let labelValuePairData = [];
        if (srvcTypeAuthorities) {
            labelValuePairData = allAuthoritiesList?.filter(
                (singleAuthority) => {
                    return (
                        srvcTypeAuthorities?.includes(singleAuthority.value) &&
                        !selectedAuthorities?.includes(singleAuthority.value)
                    );
                }
            );
        }

        return labelValuePairData;
    }

    getNumberCustomFields() {
        var customFields =
            this.state.viewData?.form_data?.srvc_cust_fields_json;
        let translatedFields = decodeFieldsMetaFrmJson(customFields);
        let labelValuePairData = [];
        if (translatedFields) {
            translatedFields.map((singleField) => {
                if (singleField.widget == 'number') {
                    labelValuePairData.push({
                        label: singleField.label,
                        value: singleField.key,
                        key: singleField.key,
                    });
                }
            });
        }
        return labelValuePairData;
    }

    getPossibleAuthoritiesFrFilter() {
        let srvcTypeAuthorities =
            this.formRef.current?.getFieldValue('srvc_authorities');
        let allAuthoritiesList = this.state.viewData?.authorities_list;

        // gets all the authorities that are in the service type
        let labelValuePairData = [];
        if (srvcTypeAuthorities) {
            labelValuePairData = allAuthoritiesList?.filter(
                (singleAuthority) => {
                    return srvcTypeAuthorities.includes(singleAuthority.value);
                }
            );
        }
        return labelValuePairData;
    }

    getPossibleAuthoritiesFrAutomationMovement(statusKey) {
        let statuses = this.getStatuses();
        let statusesKeysArray = [];

        //get all statuses of the service type except open and the status key you are checking for

        for (const status_type in statuses) {
            if (Object.hasOwnProperty.call(statuses, status_type)) {
                const statusesFrType = statuses[status_type];
                let dummyArray = [];
                dummyArray = statusesFrType.filter(
                    (singleStatusesFrType) =>
                        singleStatusesFrType.key != 'open' &&
                        singleStatusesFrType.key != statusKey
                );
                statusesKeysArray = [...statusesKeysArray, ...dummyArray];
            }
        }

        let selectedAuthorities = [];

        // Get all values(authorities) that are selected for a status

        statusesKeysArray.forEach((singleStatusesKey) => {
            let selectedAuthoritiesFrStatus =
                this.formRef?.current?.getFieldValue(
                    `srvc_authority_based_movement_status_${singleStatusesKey.key}`
                );

            if (selectedAuthoritiesFrStatus == undefined) {
                selectedAuthoritiesFrStatus =
                    this.state.viewData?.form_data[
                        `srvc_authority_based_movement_status_${singleStatusesKey.key}`
                    ];
            }

            if (
                selectedAuthoritiesFrStatus &&
                selectedAuthoritiesFrStatus.length != 0
            ) {
                selectedAuthorities.push(...selectedAuthoritiesFrStatus);
            }
        });

        let labelValuePairData =
            this.getPossibleAuthorities(selectedAuthorities);

        return labelValuePairData;
    }

    getPossibleSpecificFieldsFrAutomationMovementFileds(statusKey) {
        let statuses = this.getStatuses();
        let statusesKeysArray = [];
        for (const status_type in statuses) {
            if (Object.hasOwnProperty.call(statuses, status_type)) {
                const statusesFrType = statuses[status_type];
                let dummyArray = [];
                dummyArray = statusesFrType.filter(
                    (singleStatusesFrType) =>
                        singleStatusesFrType.key != 'open' &&
                        singleStatusesFrType.key != statusKey
                );
                statusesKeysArray = [...statusesKeysArray, ...dummyArray];
            }
        }

        let selectedCustomFields = [];

        statusesKeysArray.forEach((singleStatusesKey) => {
            let selectedCustomFieldsFrStatus =
                this.formRef?.current?.getFieldValue(
                    `srvc_auto_movement_fr_${singleStatusesKey.key}_key`
                );

            if (selectedCustomFieldsFrStatus == undefined) {
                selectedCustomFieldsFrStatus =
                    this.state.viewData?.form_data[
                        `srvc_auto_movement_fr_${singleStatusesKey.key}_key`
                    ];
            }

            if (
                selectedCustomFieldsFrStatus &&
                selectedCustomFieldsFrStatus.length != 0
            ) {
                selectedCustomFields.push(...selectedCustomFieldsFrStatus);
            }
        });

        let labelValuePairData = this.getPossibleSpecificFields(
            true,
            selectedCustomFields
        );

        return labelValuePairData;
    }

    getPossibleSpecificFieldsFrAuthority() {
        var customFields =
            this.state.viewData?.form_data?.srvc_cust_fields_json;
        let translatedFields = decodeFieldsMetaFrmJson(customFields);
        // console.log("labelValuePairData2",translatedFields)
        let labelValuePairData = [];
        if (translatedFields) {
            translatedFields.map((singleField) => {
                if (singleField.widget != 'select') {
                    labelValuePairData.push({
                        label: singleField.label,
                        value: singleField.key,
                    });
                }
            });
        }
        // console.log("labelValuePairData",labelValuePairData)
        return labelValuePairData;
    }

    getPossibleSrcSrvcTypes() {
        const editorItemId = this.props.editorItem?.id;
        const srvcTypeList = this.state.viewData?.srvc_types_list;
        for (let index = 0; index < srvcTypeList?.length; index++) {
            let singleSrvcType = srvcTypeList[index];
            if (singleSrvcType?.value == editorItemId) {
                srvcTypeList.splice(index, 1);
            }
        }
        return srvcTypeList;
    }

    getCumulativeDataFieldsMeta() {
        let prefix = 'cumulative_field';
        let data = [];
        if (!ConfigHelpers.isServiceProvider()) {
            data = [
                {
                    label: <b>Show cumulative total on selected statuses</b>,
                    key: `${prefix}_show_cumulative_total_on_selected_statuses`,
                    widget: 'checkbox',
                    onChange: () =>
                        this.setState({
                            render_helper: !this.state.render_helper,
                        }),
                },
            ];

            let isCumulativeSelected = this.formRef?.current?.getFieldValue(
                `${prefix}_show_cumulative_total_on_selected_statuses`
            );
            if (isCumulativeSelected == undefined) {
                isCumulativeSelected =
                    this.state.viewData?.form_data?.[
                        `${prefix}_show_cumulative_total_on_selected_statuses`
                    ];
            }
            if (isCumulativeSelected) {
                let cumulativeFields = [
                    {
                        label: 'Label for cumulative total',
                        key: `${prefix}_label_for_cumulative_total`,
                        widget: 'input',
                        required: true,
                        widgetProps: {
                            allowClear: true,
                        },
                    },
                    {
                        label: 'Select statuses',
                        key: `${prefix}_select_statuses`,
                        widget: 'select',
                        options: this.getStatusesAsOptions(),
                        widgetProps: {
                            mode: 'multiple',
                            allowClear: true,
                        },
                        required: true,
                        onChange: () => this.refresh(),
                    },
                ];
                data.push(...cumulativeFields);

                let cumulativeFieldStatues = [];
                let isStatuesSelected =
                    this.formRef?.current?.getFieldValue(
                        `${prefix}_select_statuses`
                    ) ||
                    this.state.viewData?.form_data[`${prefix}_select_statuses`];
                if (isStatuesSelected) {
                    isStatuesSelected.map((singleSelectStatus) => {
                        let selectedStatusTitle =
                            this.state.viewData?.form_data?.srvc_statuses_db?.filter(
                                (singleStatus) =>
                                    singleStatus.key == singleSelectStatus
                            )?.[0]?.title;
                        let dummyObj = {
                            key: `${singleSelectStatus}_select_field_for_cumulative_total`,
                            label: `${selectedStatusTitle} - Select field for cumulative total`,
                            widget: 'select',
                            widgetProps: {
                                allowClear: true,
                            },
                            required: true,
                            options: this.getNumberCustomFields(),
                        };
                        cumulativeFieldStatues.push(dummyObj);
                    });
                }
                data.push(...cumulativeFieldStatues);
            }
        }
        return data;
    }

    getProActiveMeta() {
        let prefix = 'pro_active';
        let proActiveMetaFields = [
            {
                label: 'Enable Proactive',
                key: `${prefix}_enabled`,
                widget: 'checkbox',
                onChange: () =>
                    this.setState({ render_helper: !this.state.render_helper }),
            },
            // {
            //     label: 'Proactive',
            //     key: `${prefix}_form_data` ,
            //     formItemProps : {
            //         style : {
            //             display: 'none'
            //         }
            //     }
            // }
        ];
        let isProActiveEnabled = this.formRef?.current?.getFieldValue(
            `${prefix}_enabled`
        );
        let sourceTypeSelected = this.formRef?.current?.getFieldValue(
            `${prefix}_source_srvc_type`
        );
        if (isProActiveEnabled) {
            if (sourceTypeSelected) {
                proActiveMetaFields.push(
                    {
                        key: `proactive_lambda_arn`,
                        label: `Enter lambda arn`,
                        placeholder: 'Lambda ARN',
                        rules: [
                            {
                                validator: validateLambdaArn,
                                message: 'Please enter a valid Lambda ARN',
                            },
                        ],
                    },
                    {
                        label: 'Proactive',
                        key: `pro_active_form_data_fr_${this.props.editorItem.id}_${sourceTypeSelected}`,
                        formItemProps: {
                            style: {
                                display: 'none',
                            },
                        },
                    }
                );
            }
            let proActiveEnabledMeta = [
                {
                    label: 'Select source service type',
                    key: `${prefix}_source_srvc_type`,
                    widget: 'select',
                    onChange: () =>
                        this.setState({
                            render_helper: !this.state.render_helper,
                        }),
                    options: this.getPossibleSrcSrvcTypes(),
                    required: true,
                },
                ...(sourceTypeSelected
                    ? [
                          {
                              key: `${prefix}_editor_fr_${this.props.editorItem.id}_${sourceTypeSelected}`,
                              render: () => {
                                  return (
                                      <div className="gx-jumbotron gx-border-green gx-border-2 gx-p-3">
                                          <VisualJSONRulesEditor
                                              rulesetMeta={this.getProactiveRulesetMeta()}
                                              onJSONChanged={(newObject) => {
                                                  let field_values = {};
                                                  field_values[
                                                      `pro_active_form_data_fr_${this.props.editorItem.id}_${sourceTypeSelected}`
                                                  ] = JSON.stringify(newObject);
                                                  console.log(
                                                      'field_values',
                                                      field_values
                                                  );
                                                  this.formRef.current.setFieldsValue(
                                                      field_values
                                                  );
                                                  // this.formRef.current.setFieldsValue({
                                                  //     pro_active_form_data: JSON.stringify(newObject)
                                                  // })
                                              }}
                                              hideRulesetSelector
                                              fixedOutcome
                                              render_helper
                                          />
                                      </div>
                                  );
                              },
                          },
                      ]
                    : []),
            ];
            proActiveMetaFields.push(...proActiveEnabledMeta);
        }

        return {
            fields: proActiveMetaFields,
        };
    }

    getValueStandardizationFieldMeta() {
        let initialValues = this.state.viewData?.form_data;
        let isFieldTouched = false;
        let valueStandardizationCheckboxKey = 'value_standardization_enabled';
        if (
            this.formRef?.current?.isFieldTouched(
                valueStandardizationCheckboxKey
            )
        ) {
            isFieldTouched = true;
        }
        // check if field is touched
        let valueStandardizationEnabled = isFieldTouched
            ? this.formRef?.current?.getFieldValue(
                  valueStandardizationCheckboxKey
              )
            : initialValues?.value_standardization_enabled;

        let valueStandardizationMeta = [
            {
                label: <b>Enable value standardization</b>,
                key: valueStandardizationCheckboxKey,
                widget: 'checkbox',
                onChange: () => this.forceUpdate(),
            },
            ...(valueStandardizationEnabled
                ? [
                      {
                          label: <b>Select default format</b>,
                          key: `value_standardization_default_format`,
                          widget: 'select',
                          options: unitsFrCurrency,
                          widgetProps: {
                              allowClear: true,
                              showSearch: true,
                              optionFilterProp: 'children',
                              onChange: (value) => {
                                  if (value == undefined) {
                                      this.formRef.current.setFieldsValue({
                                          value_standardization_default_format: false,
                                      });
                                  }
                              },
                          },
                      },
                      {
                          render: () => <hr />,
                      },
                  ]
                : []),
        ];
        return valueStandardizationMeta;
    }

    customizeAgeingTimeFrameJsonValidation(arr) {
        if (!Array.isArray(JSON.parse(arr))) {
            console.log('Input is not an array');
            return false;
        }

        for (const obj of JSON.parse(arr)) {
            if (typeof obj !== 'object' || obj === null) {
                console.log('Element is not an object:', obj);
                return false;
            }

            if (!obj.hasOwnProperty('value') || !obj.hasOwnProperty('label')) {
                console.log('Missing value or label property:', obj);
                return false;
            }

            if (
                typeof obj.value !== 'string' ||
                typeof obj.label !== 'string'
            ) {
                console.log('Value or label is not a string:', obj);
                return false;
            }

            const valuePattern = /^(\d+_\d+|\d+)$/;
            //const labelPattern = /^(\d+-\d+|\d+\+)$/;

            if (!valuePattern.test(obj.value)) {
                console.log('Value does not match pattern:', obj.value);
                return false;
            }

            //   if (!labelPattern.test(obj.label)) {
            //     console.log('Label does not match pattern:', obj.label);
            //     return false;
            //   }
        }

        return true;
    }
    getAgingTimeframeMeta() {
        let returnMeta = [];
        returnMeta.push({
            key: 'customize_ageing_timeframes',
            label: <b>Customize ageing timeframes</b>,
            widget: 'checkbox',
            onChange: () => this.refresh(),
        });
        let isCustAgeingTimeFrameEnabled = this.formRef?.current?.getFieldValue(
            'customize_ageing_timeframes'
        );
        if (isCustAgeingTimeFrameEnabled == undefined) {
            isCustAgeingTimeFrameEnabled =
                this.state.viewData?.form_data?.customize_ageing_timeframes;
        }
        if (isCustAgeingTimeFrameEnabled) {
            returnMeta.push({
                key: 'input_ageing_timeframe_json',
                label: 'Input Ageing timeframe json',
                widget: 'textarea',
                required: true,
                tooltip:
                    'For e.g. [{"value":"0_3","label":"0-3","bg_color":"FA8072","text_color":"FFFFFF"}]',
                rules: [
                    {
                        validator: (rule, value, callback) => {
                            return new Promise((resolve, reject) => {
                                if (
                                    this.customizeAgeingTimeFrameJsonValidation(
                                        value
                                    )
                                ) {
                                    console.log('Ready to resolve');
                                    resolve();
                                } else {
                                    reject(
                                        new Error('Invalid json for ageing.')
                                    );
                                }
                            });
                        },
                    },
                ],
            });
        }

        return returnMeta;
    }

    getDynamicStep4Meta() {
        return {
            fields: [
                ...this.getCumulativeDataFieldsMeta(),
                ...this.getValueStandardizationFieldMeta(),
                ...this.getAgingTimeframeMeta(),
                {
                    label: 'Measure stage transtion time (Aging)',
                    key: 'srvc_measure_stage_aging',
                    widget: 'checkbox',
                },
                {
                    label: 'Measure TAT (open to closure)',
                    key: 'srvc_measure_open_closure_tat',
                    widget: 'checkbox',
                },
                {
                    label: 'Measure feedback (only applicable if feedback is on)',
                    key: 'srvc_measure_cust_feedback',
                    widget: 'checkbox',
                },
                {
                    label: 'Measure stage wise count',
                    key: 'srvc_measure_stage_wise_count',
                    widget: 'checkbox',
                },
                {
                    label: 'Measure location wise heat map',
                    key: 'srvc_measure_loc_wise_heat_map',
                    widget: 'checkbox',
                },
                {
                    label: 'Measure subtask vs task count',
                    key: 'srvc_measure_sub_task_vs_task_count',
                    widget: 'checkbox',
                    tooltip:
                        '- Will show Avg sub task per task\n- Filtered by sub task type',
                },
                {
                    label: 'Measure stage wise TAT',
                    key: 'srvc_measure_stage_wise_tat',
                    widget: 'checkbox',
                },
                // {
                //     label: 'Columns for filtering',
                //     key: 'srvc_measure_stage_wise_tat' ,
                //     widget: 'checkbox',
                // },
                {
                    label: 'Measure Assignees per request',
                    key: 'srvc_measure_assgnes_per_req',
                    widget: 'checkbox',
                },
                {
                    label: 'Measure no of request per assignee',
                    key: 'srvc_measure_no_req_per_assgne',
                    widget: 'checkbox',
                },
                {
                    label: 'Choose fields to be displayed on authority wise requests',
                    key: 'srvc_fields_for_authority_wise_requests',
                    widget: 'select',
                    options: this.getPossibleSpecificFieldsFrAuthority(),
                    widgetProps: {
                        mode: 'multiple',
                    },
                },
            ],
        };
    }
    getAutomationSrvcStatusConfigurationMeta(srvcStatuses) {
        const initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};
        const isAutomationSrvcStatusEnable =
            this.formRef.current?.getFieldValue(
                'automation_srvc_status_enable'
            );
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: <b>Enable ?</b>,
                    key: 'automation_srvc_status_enable',
                    widget: 'checkbox',
                    onChange: () => {
                        this.refresh();
                    },
                },
                ...(isAutomationSrvcStatusEnable
                    ? [
                          {
                              key: 'automation_srvc_statuses',
                              label: 'Service Type Statuses',
                              widget: 'select',
                              widgetProps: {
                                  allowClear: true,
                              },
                              placeholder: 'Select service statuses',
                              options: srvcStatuses,
                              onChange: (value) => {
                                  const newRowData =
                                      this.getRowDataFrRuleTableFrSbtskStatusMovementBasedOnSrvcStatus();
                                  this.formRef.current.setFieldsValue({
                                      sbtsk_status_movement_based_on_srvc_update_rules:
                                          JSON.stringify(newRowData),
                                  });
                                  this.refresh();
                              },
                              widgetProps: {
                                  mode: 'multiple',
                                  allowClear: true,
                                  showSearch: true,
                                  optionFilterProp: 'children',
                              },
                          },
                          {
                              key: 'sbtsk_status_movement_based_on_srvc_update_rules',
                              className: 'gx-d-none',
                              widgetProps: {
                                  hidden: true,
                              },
                          },
                          {
                              key: `automation_sbtsk_statuses_movement_based_on_srvc_update_rules`,
                              render: () => {
                                  return (
                                      <InputTable
                                          // demoMode={true}
                                          // rowData = {[{'test':123}]}
                                          onChange={(newRowData) => {
                                              this.formRef.current.setFieldsValue(
                                                  {
                                                      sbtsk_status_movement_based_on_srvc_update_rules:
                                                          JSON.stringify(
                                                              newRowData
                                                          ),
                                                  }
                                              );
                                          }}
                                          rowData={this.getRowDataFrRuleTableFrSbtskStatusMovementBasedOnSrvcStatus()}
                                          colMeta={this.getColMetaFrRuleTableFrSbtskStatusMovementBasedOnSrvcStatus(
                                              { srvcStatuses }
                                          )}
                                      />
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };
        return meta;
    }

    getDynamicStep3Meta() {
        let srvc_enable_srvc_prvdr = this.formRef?.current?.getFieldValue(
            'srvc_enable_srvc_prvdr'
        );
        let make_custom_fields_non_editable_from_provider =
            this.formRef?.current?.getFieldValue(
                'make_custom_fields_non_editable_from_provider'
            );
        let srvcPrvdrFieldMeta = [];
        let status_type = [];
        let statuses = this.getStatuses();
        if (statuses != undefined) {
            Object.keys(statuses).map((statusKey) => {
                statuses[statusKey].map((singleStatus) => {
                    let statusSelectOption = {};
                    statusSelectOption['label'] = singleStatus.title;
                    statusSelectOption['value'] = singleStatus.key;
                    statusSelectOption['title'] = singleStatus.title;
                    statusSelectOption['key'] = singleStatus.key;
                    status_type.push(statusSelectOption);
                });
            });
        }
        let auto_status_fr_on_field_full_prefix =
            'auto_status_change_fr_on_field';
        let autoStatusMovementOnFieldMeta = [
            {
                key: `${auto_status_fr_on_field_full_prefix}_status_key`,
                label: 'Auto move service request status to (Whenever onfield task is added)',
                widget: 'select',
                placeholder: 'Select status type',
                options: status_type,
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    onChange: (value) => {
                        handleClearSelect(
                            value,
                            this.formRef,
                            `${auto_status_fr_on_field_full_prefix}_status_key`
                        );
                    },
                    optionFilterProp: 'children',
                },
            },
        ];
        const autoSbtskStatusMovementOnSrvcStatusMovement = [
            {
                key: 'configure_automation_srvc_status',
                render: () => {
                    return (
                        <div className="gx-mb-3">
                            <Collapse
                                className="gx-mt-0 gx-mb-3"
                                onChange={
                                    () => {}
                                    // this.getAllPossibleSrvcTypesFrSrvcPrvdr()
                                }
                            >
                                <Collapse.Panel
                                    forceRender={true}
                                    header={
                                        'Configure Subtask Status Movement Based on Service Request Status'
                                    }
                                >
                                    <FormBuilder
                                        meta={this.getAutomationSrvcStatusConfigurationMeta(
                                            status_type
                                        )}
                                        form={this.formRef}
                                    />
                                </Collapse.Panel>
                            </Collapse>
                        </div>
                    );
                },
            },
        ];
        if (srvc_enable_srvc_prvdr) {
            srvcPrvdrFieldMeta.push(
                {
                    label: 'Possible service providers',
                    key: 'srvc_possible_prvdrs',
                    widget: 'select',
                    options: this.state.viewData?.possible_srvc_prvdrs,
                    onChange: () => {
                        const newRowData =
                            this.getRowDataFrRuleTableFrSbtskStatusMovementBasedOnSrvcStatus();
                        this.formRef.current.setFieldsValue({
                            sbtsk_status_movement_based_on_srvc_update_rules:
                                JSON.stringify(newRowData),
                        });
                        this.refresh();
                    },
                    widgetProps: {
                        mode: 'multiple',
                    },
                },
                {
                    key: 'srvc_default_provider',
                    label: 'Default service provider',
                    placeholder: 'Select..',
                    widget: 'select',
                    widgetProps: {
                        // "mode":"multiple"
                        allowClear: true,
                        showSearch: true,
                        onChange: (value) => {
                            handleClearSelect(
                                value,
                                this.formRef,
                                'srvc_default_provider'
                            );
                        },
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.possible_srvc_prvdrs,
                },
                {
                    label: 'Allow reassignment of service provider',
                    key: 'enable_reassignment_of_srvc_prvdr',
                    widget: 'checkbox',
                },
                {
                    label: 'Allow provider to add requests',
                    key: 'srvc_enable_srvc_prvdr_to_add_requests',
                    widget: 'checkbox',
                },
                ...autoStatusMovementOnFieldMeta,

                ...(this.state?.TMS250414073641
                    ? [
                          {
                              label: 'Restrict SP from viewing requests in the selected statuses',
                              key: 'srvc_prvdr_restricted_request_status',
                              widget: 'select',
                              placeholder: 'Select status type',
                              widgetProps: {
                                  mode: 'multiple',
                                  allowClear: true,
                                  showSearch: true,
                                  optionFilterProp: 'children',
                              },
                              options: status_type,
                              tooltip:
                                  'SP users will not be able to view the service request data for the selected statuses.',
                          },
                      ]
                    : []),

                ...autoSbtskStatusMovementOnSrvcStatusMovement,
                {
                    label: 'Re-use existing customer profiles while creation',
                    key: 'srvc_reuse_existing_cust',
                    widget: 'checkbox',
                },
                {
                    label: 'Hide consumer phonenumber from provider',
                    key: 'srvc_hide_consumer_phonenumber_from_prvdr',
                    widget: 'checkbox',
                },
                {
                    label: 'Hide consumer phonenumber from onfield users of provider',
                    key: 'srvc_hide_consumer_phonenumber_from_onfield',
                    widget: 'checkbox',
                },
                {
                    label: 'Make custom fields non editable from provider',
                    key: 'make_custom_fields_non_editable_from_provider',
                    widget: 'checkbox',
                    onChange: () =>
                        this.setState({
                            render_helper: !this.state.render_helper,
                        }),
                },
                ...(make_custom_fields_non_editable_from_provider
                    ? [
                          {
                              label: 'Select custom fields to be made non editable',
                              key: 'select_custom_fields_to_be_made_non_editable',
                              widget: 'select',
                              options: this.getPossibleSpecificFields(true),
                              widgetProps: {
                                  allowClear: true,
                                  mode: 'multiple',
                                  onChange: (value) => {
                                      handleClearSelect(
                                          value,
                                          this.formRef,
                                          'select_custom_fields_to_be_made_non_editable'
                                      );
                                  },
                              },
                          },
                      ]
                    : [])
            );
        }

        return {
            fields: [
                // {
                //     label: 'Can be assigned to',
                //     key: 'srvc_can_be_assg_to' ,
                //     widget: 'select',
                //     option: this.state.viewData?.possibleRoles,
                //     mode:'multiple'
                // },
                {
                    label: 'Allow requests to be assigned to service providers',
                    key: 'srvc_enable_srvc_prvdr',
                    widget: 'checkbox',
                    onChange: () =>
                        this.setState({
                            render_helper: !this.state.render_helper,
                        }),
                },
                ...srvcPrvdrFieldMeta,
            ],
        };
    }

    isProjectNature() {
        return (
            this.formRef?.current?.getFieldValue('srvc_type_nature') ==
            PROJECT_BASED_SERVICE_TYPE
        );
    }

    getDynamicStep2Meta() {
        // console.log("Applicable subtask list",this.state.viewData?.applicable_subtasks_list);
        const initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};

        var rateFields = this.formRef?.current?.getFieldValue(
            'srvc_rate_fields_json'
        );
        // console.log('Customer fields',rateFields);
        if (rateFields == undefined) {
            rateFields = initialValues?.srvc_rate_fields_json;
        }

        //make a dynamic authorities role access base on selected authority role
        let roleWiseAuthoritySpecificFields = [];
        let selected_roles_srvc_authorities = [];
        let show_authorities_that_report_to_the_assigner = [];
        let authorities_list = this.state.viewData?.authorities_list;
        var srvc_authorities =
            this.formRef?.current?.getFieldValue('srvc_authorities');
        if (srvc_authorities) {
            srvc_authorities.forEach((singleSrvcAuthority) => {
                let seletcedRoleTitle = authorities_list?.filter(
                    (singleAuthority) =>
                        singleAuthority.value == singleSrvcAuthority
                )?.[0]?.label;

                let dummyObj = {
                    label: seletcedRoleTitle,
                    key:
                        singleSrvcAuthority +
                        '_enable_cross_visibility_of_authorities',
                    widget: 'checkbox',
                };
                selected_roles_srvc_authorities.push(dummyObj);
                if (!ConfigHelpers.isServiceProvider()) {
                    let showAuthoritiesThatReportToTheAssignerObj = {
                        label:
                            seletcedRoleTitle + ' - Show only direct reportees',
                        key:
                            singleSrvcAuthority +
                            '_show_authorities_that_report_to_the_assigner',
                        widget: 'checkbox',
                    };
                    show_authorities_that_report_to_the_assigner.push(
                        showAuthoritiesThatReportToTheAssignerObj
                    );
                }

                let roleWiseAuthoritySpecificFieldsobj = {
                    label:
                        seletcedRoleTitle +
                        ' Specific Fields (only ' +
                        seletcedRoleTitle +
                        ' of the selected ' +
                        this.props?.editorItem?.title +
                        ' can edit these fields)',
                    key:
                        'srvc_authority_' +
                        singleSrvcAuthority +
                        '_specific_fields',
                    widget: 'select',
                    options: this.getPossibleSpecificFields(true, [], true),
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                };
                roleWiseAuthoritySpecificFields.push(
                    roleWiseAuthoritySpecificFieldsobj
                );
            });
        }
        let selected_roles_srvc_authorities_seperator = [];
        if (selected_roles_srvc_authorities.length > 0) {
            let dummyObj = {
                key: 'selected_roles_srvc_authorities_seperator',
                render: () => {
                    return (
                        <Alert
                            message="Enable cross visibility of selected authorities"
                            type="info"
                        />
                    );
                },
            };
            selected_roles_srvc_authorities_seperator.push(dummyObj);
        }
        let srvc_feedback_send_by_whatsapp =
            this.formRef?.current?.getFieldValue(
                'srvc_feedback_send_by_whatsapp'
            );
        return {
            fields: [
                // {
                //     label: 'Has physical address',
                //     key: 'srvc_has_physical_addr' ,
                //     widget: 'checkbox'

                // },
                // {
                //     label: 'Service locations',
                //     key: 'srvc_locations' ,
                //     widget: 'select',
                //     options: this.state.viewData?.locations,
                //     widgetProps:{
                //         mode:'multiple'
                //     }

                // },
                // {
                //     label: 'Customer groups',
                //     key: 'srvc_cust_grps' ,
                //     widget: 'select',
                //     options: this.state.viewData?.cust_grps,
                //     widgetProps:{
                //         mode:'multiple'
                //     }

                // },
                // {
                //     label: 'Dynamic customer groups ?',
                //     key: 'srvc_dynamic_cust_grps' ,
                //     widget: 'checkbox',
                //     extra: 'If yes then at time of creation if group doesnot exists in system then user can add/create group directly.'
                // },
                {
                    label: <b>Customer mobile not mandatory ?</b>,
                    key: 'srvc_cust_mobile_not_mandatory',
                    widget: 'checkbox',
                },
                {
                    label: (
                        <b>
                            Request service date mandatory while request
                            creation ?
                        </b>
                    ),
                    key: 'request_service_date_mandatory_while_request_creation',
                    widget: 'checkbox',
                },
                ...(this.state?.TMS250312325161
                    ? [
                          {
                              label: 'Select mandatory address fields for a request',
                              key: 'select_mandatory_address_fields_for_a_request',
                              widget: 'select',
                              widgetProps: {
                                  options: [
                                      {
                                          label: 'Flat no',
                                          value: 'cust_line_0',
                                      },
                                      {
                                          label: 'Building/Apartment name',
                                          value: 'cust_line_1',
                                      },
                                      { label: 'Line 1', value: 'cust_line_2' },
                                      { label: 'Line 2', value: 'cust_line_3' },
                                      {
                                          label: 'Pincode',
                                          value: 'cust_pincode',
                                      },
                                      { label: 'City', value: 'cust_city' },
                                      { label: 'State', value: 'cust_state' },
                                  ],
                                  allowClear: true,
                                  placeholder: 'Select address field to show',
                                  mode: 'multiple',
                              },
                          },
                      ]
                    : [
                          {
                              label: (
                                  <b>
                                      Pin code mandatory while request creation
                                      ?
                                  </b>
                              ),
                              key: 'srvc_req_cust_pincode_mandatory',
                              widget: 'checkbox',
                          },
                      ]),
                {
                    label: <b>Can customer rate the service ?</b>,
                    key: 'srvc_can_cust_rate',
                    widget: 'checkbox',
                    onChange: (event) => {
                        this.formRef.current.setFieldsValue({
                            dont_send_srvc_feedback_sms: false,
                        });
                        this.refresh();
                    },
                    extra: '( An sms will be sent to customer to rate the service )',
                },
                ...(this.formRef?.current?.getFieldValue('srvc_can_cust_rate')
                    ? [
                          {
                              label: <span>Don't Send feedback SMS </span>,
                              key: 'dont_send_srvc_feedback_sms',
                              widget: 'checkbox',
                              // onChange : event => {
                              //     this.refresh()
                              // }
                          },
                      ]
                    : []),
                ...this.getSmsFeedbackMessageMeta(),
                {
                    key: 'seperator_3',
                    render: () => {
                        return <div className="gx-mt-3"> </div>;
                    },
                },
                {
                    label: (
                        <span>
                            Send Feedback Whatsapp Message{' '}
                            <WhatsAppOutlined className="gx-text-green" />
                        </span>
                    ),
                    key: 'srvc_feedback_send_by_whatsapp',
                    widget: 'checkbox',
                    onChange: (event) => {
                        this.refresh();
                    },
                },
                ...this.getWhatsAppFeedbackMessageMeta(),
                {
                    key: 'show_ext_order_id_on_the_subtask',
                    label: 'Show external order id instead of TMS request ID',
                    widget: 'checkbox',
                },
                {
                    label: (
                        <span>
                            <b>Customer feedback</b> form fields
                        </span>
                    ),
                    key: 'srvc_rate_fields_json',
                    widget: 'textarea',
                    placeholder: 'Please paste the JSON here',
                    onChange: (event) => {
                        this.setState(
                            {
                                render_helper: !this.state.render_helper,
                            },
                            () => this.setDefaultRatingsField()
                        );
                    },
                },
                {
                    key: 'link_to_rate_field_creator',
                    render: () => {
                        return (
                            <div className="gx-mb-4">
                                {rateFields != '' && (
                                    <div>
                                        <Button
                                            onClick={(e) =>
                                                this.handleShowFieldsPreviewClick(
                                                    rateFields
                                                )
                                            }
                                            icon={<ZoomInOutlined />}
                                        >
                                            Preview Rate form
                                        </Button>
                                    </div>
                                )}
                                <Link
                                    to={
                                        '/fields-creator?edit=' +
                                        encodeURIComponent(rateFields)
                                    }
                                    target="_blank"
                                >
                                    Open field creator {'-->'}
                                </Link>
                            </div>
                        );
                    },
                },
                {
                    label: (
                        <span>
                            <b>Send feedback sms when request is moved to</b>
                        </span>
                    ),
                    key: 'srvc_feedback_trigger_status_key',
                    widget: 'select',
                    options: this.getStatusesAsOptions(),
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
                {
                    label: (
                        <span>
                            <b>Ratings field</b>
                        </span>
                    ),
                    key: 'srvc_feedback_ratings_field_key',
                    widget: 'select',
                    options: this.getRatingsFormFieldAsOptions(),
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
                {
                    key: 'seperator_1',
                    render: () => {
                        return (
                            <div className="gx-mb-3 gx-bg-grey">
                                <hr></hr>
                            </div>
                        );
                    },
                },
                // {
                //     label: 'Can have linkage to other request ?',
                //     key: 'srvc_can_have_link' ,
                //     widget: 'checkbox',
                //     extra: 'This means your service request could be connected to a previous request.'
                // },
                {
                    label: <b>Can users subscribe ?</b>,
                    key: 'srvc_can_usrs_subscribe',
                    widget: 'checkbox',
                    extra: 'CC users capability the user will be able to get notification for update.',
                },
                {
                    label: <b>Applicable sub-task type</b>,
                    key: 'srvc_appl_sub_tasks',
                    widget: 'select',
                    options: this.state.viewData?.applicable_subtasks_list,
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    onChange: () => {
                        this.getAllTheApplicableSbtskTypeFrReadiness();
                        this.refresh();
                    },
                },
                {
                    key: 'auto_status_creation_title',
                    render: () => {
                        return (
                            <div>
                                <p>
                                    <b>
                                        <i class="icon icon-callout gx-mr-1" />
                                        Configure auto status movement based on
                                        subtask creation
                                    </b>
                                </p>
                            </div>
                        );
                    },
                },
                ...this.getMetaFrAutoStatusMovement()?.fields,
                {
                    key: 'seperator_2',
                    render: () => {
                        return <div className="gx-mt-3"> </div>;
                    },
                },
                ...(!ConfigHelpers.isServiceProvider()
                    ? [
                          {
                              label: (
                                  <b>
                                      Enable subtask closure on service request
                                      closure
                                  </b>
                              ),
                              key: 'subtask_closure_on_srvc_req_closure',
                              widget: 'checkbox',
                              tooltip:
                                  'If enabled, all subtasks (brand and SP) on the request will be closed on service request closure',
                          },
                      ]
                    : []),
                {
                    label: (
                        <span>
                            <b>Authorities</b> ( Make sure that these roles have
                            access to this service request )
                        </span>
                    ),
                    key: 'srvc_authorities',
                    widget: 'select',
                    options: this.state.viewData?.authorities_list,
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    onChange: () => this.refresh(),
                },
                {
                    label: (
                        <span>
                            <b>
                                Hide authority selection from specific details
                            </b>
                        </span>
                    ),
                    key: 'hide_authority_selection_from_specific_details',
                    widget: 'checkbox',
                },
                {
                    label: (
                        <span>
                            <b>
                                Restrict manual authority selection during
                                creation
                            </b>
                        </span>
                    ),
                    key: 'restrict_manual_authority_selection',
                    widget: 'checkbox',
                },
                ...selected_roles_srvc_authorities_seperator,
                ...selected_roles_srvc_authorities,
                ...show_authorities_that_report_to_the_assigner,
                ...roleWiseAuthoritySpecificFields,
            ],
        };
    }
    getLineItemsConfigMeta() {
        return {
            fields: [...this.getMetaFrLineItemConfiguration()],
        };
    }

    onSaveOfPricingConfig(data, singleLineItemGrpKey) {
        let pricingConfigData = this.getPricingConfig();
        pricingConfigData[singleLineItemGrpKey] = data;
        // console.log('onSaveOfPricingConfig',pricingConfigData)
        this.formRef.current.setFieldsValue({
            srvc_type_pricing_config_for_line_item:
                JSON.stringify(pricingConfigData),
        });
    }

    onSaveOfPricingConfigFrManday(data) {
        let pricingConfigDataFrManday = this.getPricingConfigFrManday();
        pricingConfigDataFrManday['srvc_type_pricing_config_for_manday'] = data;
        console.log('pricingConfigDataFrManday', pricingConfigDataFrManday);
        this.formRef.current.setFieldsValue({
            srvc_type_pricing_config_for_manday: JSON.stringify(
                pricingConfigDataFrManday
            ),
        });
    }

    getPricingMasterConfigMeta(initialValues) {
        let lineItemConfigData = this.getLineItemConfig();
        let pricingConfigData = this.getPricingConfig();
        let pricingConfigDataFrManday = this.getPricingConfigFrManday();
        return {
            fields: [
                {
                    label: 'Service type line item pricing config json',
                    key: 'srvc_type_pricing_config_for_line_item',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    label: 'Service type line item pricing config json for manday',
                    key: 'srvc_type_pricing_config_for_manday',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `srvc_type_pricing_master_config`,
                    render: () => {
                        return (
                            <Tabs defaultActiveKey="line_item">
                                {Object.keys(lineItemConfigData).length > 0 &&
                                    this.isProjectNature() && (
                                        <Tabs.TabPane
                                            tab="Line Items"
                                            key="line_item"
                                        >
                                            <Tabs defaultActiveKey="line_item">
                                                {Object.keys(
                                                    lineItemConfigData
                                                ).map(
                                                    (
                                                        singleLineItemGrpKey,
                                                        index
                                                    ) => (
                                                        <Tabs.TabPane
                                                            key={index}
                                                            tab={
                                                                lineItemConfigData?.[
                                                                    singleLineItemGrpKey
                                                                ]?.label
                                                            }
                                                        >
                                                            <PricingMaster
                                                                pricingInitialValue={
                                                                    pricingConfigData[
                                                                        singleLineItemGrpKey
                                                                    ]
                                                                }
                                                                fullFormInitialValue={
                                                                    initialValues
                                                                }
                                                                form={
                                                                    this.formRef
                                                                }
                                                                config_data={
                                                                    lineItemConfigData[
                                                                        singleLineItemGrpKey
                                                                    ]
                                                                }
                                                                onChange={(
                                                                    data
                                                                ) => {
                                                                    // WE GET pricing for single line item group here
                                                                    this.onSaveOfPricingConfig(
                                                                        data,
                                                                        singleLineItemGrpKey
                                                                    );
                                                                }}
                                                            />
                                                        </Tabs.TabPane>
                                                    )
                                                )}
                                            </Tabs>
                                        </Tabs.TabPane>
                                    )}
                                <Tabs.TabPane tab="Manday">
                                    <PricingMaster
                                        isManday
                                        pricingInitialValue={
                                            pricingConfigDataFrManday?.srvc_type_pricing_config_for_manday
                                        }
                                        fullFormInitialValue={initialValues}
                                        form={this.formRef}
                                        config_data={
                                            this.state.viewData
                                                ?.location_grp_list
                                        }
                                        onChange={(data) => {
                                            // WE GET pricing for single line item group here
                                            this.onSaveOfPricingConfigFrManday(
                                                data
                                            );
                                        }}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }

    //Create a new component for Billing to be done later
    getBillingformMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Enable Billing',
                    key: 'srvc_type_enable_billing',
                    widget: 'checkbox',
                },
                {
                    key: 'srvc_type_billing_type',
                    label: 'Billing Type',
                    widget: 'select',
                    widgetProps: {
                        mode: 'single',
                    },
                    options: BILLING_TYPE,
                },
                {
                    key: 'srvc_type_categorize_specific_fields_for_billing',
                    label: 'Categorize specific fields for billing section',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.getPossibleSpecificFields(),
                },
                {
                    key: 'srvc_type_who_can_lock_srvc_req_for_billing',
                    label: 'Who can lock service request for billing',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.authorities_list || [],
                },
                {
                    key: 'srvc_type_who_can_sync_srvc_req_prc',
                    label: 'Who can sync service request price',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.authorities_list || [],
                },
                {
                    key: 'srvc_type_who_can_send_req_for_billing',
                    label: 'Who can send a Request for billing',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.authorities_list || [],
                },
                {
                    key: 'srvc_type_who_will_get_notified_for_billing',
                    label: 'Who will get notified for billing',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.authorities_list || [],
                },
            ],
        };
        return meta;
    };

    getDiscountingBillingformMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Enable Discounting',
                    key: 'srvc_type_enable_billing_discounting',
                    widget: 'checkbox',
                },
                {
                    label: (
                        <span>
                            {' '}
                            When discount approval status changes, notify{' '}
                        </span>
                    ),
                    key: `srvc_type_discount_approval_status_changes_notify`,
                    widget: 'select',
                    options: this.state.viewData?.authorities_list || [],
                    widgetProps: {
                        allowClear: true,
                        mode: 'multiple',
                    },
                },
                {
                    key: `srvc_type_discounting_billing_master`,
                    render: () => {
                        return (
                            <BillingDiscountingConfigModule
                                onChange={(newObj) => {
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_billing_discounting_rule_config:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                                currValue={this.getBillingDiscountConfig()}
                                authorities_list={
                                    this.state.viewData?.authorities_list
                                }
                            />
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getAdditionalBillingformMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Enable Additional Billing',
                    key: 'srvc_type_enable_additional_billing',
                    widget: 'checkbox',
                },
                {
                    key: `srvc_type_additional_billing_master`,
                    render: () => {
                        return (
                            <>
                                <LineItemManagementModule
                                    onChange={(newObj) => {
                                        this.formRef.current.setFieldsValue({
                                            srvc_type_additional_billing_config:
                                                JSON.stringify(newObj),
                                        });
                                        this.refresh();
                                    }}
                                    currValue={this.getAdditionalBillingJson()}
                                    hideBtn
                                />
                            </>
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getAdditionalBillingJson() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_additional_billing_config'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            return {
                additional_line_item: {
                    key: 'additional_line_item',
                    label: 'Additional line items',
                },
            };
        }
        return {};
    }

    getBillingMasterConfigMeta(initialValues) {
        return {
            fields: [
                {
                    //All data of Billing section will be saved under this key and this is to be done later
                    label: 'Service type billing config json for billing',
                    key: 'srvc_type_billing_config_for_billing',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    label: 'Service type discounting config json for billing',
                    key: 'srvc_type_billing_discounting_rule_config',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    label: 'Service type additional config json for billing',
                    key: 'srvc_type_additional_billing_config',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `srvc_type_billing_master_config`,
                    render: () => {
                        return (
                            <Tabs
                                defaultActiveKey="billing"
                                className="gx-mb-2"
                            >
                                <Tabs.TabPane tab="Billing" key="billing">
                                    {/* Create a new component for Billing to be done later */}
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getBillingformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>

                                <Tabs.TabPane
                                    tab="Discounting"
                                    key="discounting"
                                >
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getDiscountingBillingformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>

                                <Tabs.TabPane tab="Additional" key="additional">
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getAdditionalBillingformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }

    getRatingConfigMeta(initialValues) {
        const enableRatingValue =
            this.formRef?.current?.getFieldValue('enable_rating');
        return {
            fields: [
                {
                    key: 'enable_rating',
                    label: 'Enable Ratings',
                    widget: 'checkbox',
                    onChange: () => this.refresh(),
                },
                ...(enableRatingValue
                    ? [
                          {
                              key: `srvc_type_rating_config`,
                              render: () => {
                                  return (
                                      <Tabs
                                          defaultActiveKey="rating"
                                          className="gx-mb-2"
                                      >
                                          <Tabs.TabPane
                                              tab="Authorities"
                                              key="authorities"
                                          >
                                              <FormBuilder
                                                  form={this.formRef}
                                                  meta={getRatingFormMeta(
                                                      initialValues,
                                                      this.getParamsFrAuthorityRatings()
                                                  )}
                                              />
                                          </Tabs.TabPane>

                                          {this.isProjectNature() && (
                                              <Tabs.TabPane
                                                  tab="Assignees"
                                                  key="assignees"
                                              >
                                                  <FormBuilder
                                                      form={this.formRef}
                                                      meta={getRatingFormMeta(
                                                          initialValues,
                                                          this.getParamsFrAssigneesRatings()
                                                      )}
                                                  />
                                              </Tabs.TabPane>
                                          )}
                                      </Tabs>
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };
    }

    getParamsFrAuthorityRatings = () => {
        return {
            viewData: this.state.viewData,
            formRef: this.formRef,
            editMode: this.state.editMode,
            message: 'Here, you can configure who will rate which authority',
            keySelectedAuthoritiesorDeployment: 'srvc_authorities',
            keyRoleList: 'authorities_list',
            collapseKey: 'rating_authorites_',
            tabName: 'authority',
            ratingTypeKey: 'rating_type_',
            refresh: () => this.refresh(),
            authorityIdKey: 'srvc_authorities',
            isProjectBased: this.isProjectNature(),
            selectedRatingAuthority: 'authority_',
            staticUserKey: 'static_user',
            templateKey: 'rating_template_',
        };
    };
    getParamsFrAssigneesRatings = () => {
        return {
            viewData: this.state.viewData,
            formRef: this.formRef,
            editMode: this.state.editMode,
            message: 'Here, you can configure who will rate on fields users.',
            keySelectedAuthoritiesorDeployment: 'deployment_possible_roles',
            keyRoleList: 'authorities_list',
            collapseKey: 'rating_assignees_',
            tabName: 'assignee',
            ratingTypeKey: 'rating_type_fr_deployment_',
            refresh: () => this.refresh(),
            authorityIdKey: 'srvc_authorities',
            isProjectBased: this.isProjectNature(),
            selectedRatingAuthority: 'authority_fr_deployment_',
            staticUserKey: 'static_user_fr_deployment_',
            templateKey: 'rating_template_fr_deployment_',
        };
    };

    getAutoMovementAuthoritiesformMeta = (initialValues) => {
        let statusWiseAutoMovementAuthorities = [];
        let statuses = this.getStatuses();

        for (const status_type in statuses) {
            if (Object.hasOwnProperty.call(statuses, status_type)) {
                const statusesFrType = statuses[status_type];
                statusesFrType.forEach((singleStatusesFrType) => {
                    if (singleStatusesFrType.key != 'open') {
                        statusWiseAutoMovementAuthorities.push({
                            label: (
                                <span>
                                    <b>{singleStatusesFrType.title}</b>
                                </span>
                            ),
                            key: `srvc_authority_based_movement_status_${singleStatusesFrType.key}`,
                            widget: 'select',
                            options:
                                this.getPossibleAuthoritiesFrAutomationMovement(
                                    singleStatusesFrType.key
                                ),
                            onChange: () => {
                                this.forceUpdate();
                            },
                            widgetProps: {
                                allowClear: true,
                                mode: 'multiple',
                            },
                        });
                    }
                });
            }
        }

        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    render: () => {
                        return (
                            <h4 className="gx-mb-md-3">
                                Select Automation Movement Authorities
                            </h4>
                        );
                    },
                },
                ...statusWiseAutoMovementAuthorities,
            ],
        };
        return meta;
    };
    getAllTheApplicableSbtskTypeFrReadiness = () => {
        let sbtskSelected =
            this.formRef?.current?.getFieldValue(
                'select_subtask_fr_readiness'
            ) || this.state.viewData?.form_data?.select_subtask_fr_readiness;
        let applicableSubTaskListOfSrvcType = this.getApplicableSubTasks();
        let selectedSbtskAreApplicable = [];
        selectedSbtskAreApplicable = sbtskSelected?.filter((e) =>
            applicableSubTaskListOfSrvcType?.includes(e)
        );
        return selectedSbtskAreApplicable;
    };
    getReadinessFieldsMeta = (initialValues) => {
        let sbtskSelected = this.getAllTheApplicableSbtskTypeFrReadiness();
        const allSbtskList = this.state.viewData?.applicable_subtasks_list;
        let singleSbtskMeta = []; //sbtskselected and getTheapplicatioble compare these two
        let applicableSubTaskListOfSrvcType = this.getApplicableSubTasks();
        let getTheApplicableSubTaskList =
            allSbtskList.filter((e) =>
                applicableSubTaskListOfSrvcType?.includes(e?.value)
            ) || [];
        if (sbtskSelected && sbtskSelected.length > 0) {
            sbtskSelected.forEach((singleSbtsk) => {
                const sbtskDetails = allSbtskList.filter(
                    (sbtsk) => sbtsk.value == singleSbtsk
                )[0];
                singleSbtskMeta = [
                    ...singleSbtskMeta,
                    {
                        key: `select_mandatory_field_fr_${singleSbtsk}`,
                        label: `Select mandatory field for ${sbtskDetails.label}`,
                        widget: 'select',
                        widgetProps: {
                            mode: 'multiple',
                            allowClear: true,
                        },
                        required: true,
                        options: getSrvcReqStaticAndCustomPossibleFields(
                            this.getPossibleSpecificFields(true),
                            ['full_address', 'srvc_prvdr', 'sbtsks'],
                            true
                        ),
                    },
                ];
            });
        }
        this.formRef.current.setFieldsValue({
            select_subtask_fr_readiness:
                this.getAllTheApplicableSbtskTypeFrReadiness(),
        });
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            colon: true,
            fields: [
                {
                    key: 'select_subtask_fr_readiness',
                    label: 'Select subtask',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        optionFilterProp: 'children',
                    },
                    options: getTheApplicableSubTaskList,
                    onChange: () => {
                        this.refresh();
                    },
                    colSpan: 4,
                },
                ...singleSbtskMeta,
            ],
        };
        return meta;
    };

    getAssignmentMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [...this.getAutoAssignAuthorityFields()],
        };
        return meta;
    };
    getPeriodicAutomationsMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Service type periodic automation',
                    key: 'srvc_type_periodic_automation',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    key: `srvc_type_periodic_automation_component`,
                    render: () => {
                        return (
                            <PeriodicAutoModule
                                srvc_type_id={this.props.editorItem.id}
                                roles_ids={this.state.viewData?.roles_list}
                                initialValue={this.getPeriodicAutomationJson()}
                                // initialValue={{ 1: 2 }}
                                onChange={(newObj) => {
                                    // console.log("newObj",newObj);
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_periodic_automation:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                                statuses={this.getStatuses()}
                            />
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getAllApplicableSbtsk() {
        const { viewData } = this.state;
        if (!viewData) return [];

        const allSubtasks = viewData.applicable_subtasks_list || [];
        let possibleSubtasks = [];

        if (this.isProjectNature()) {
            const deployment_possible_roles =
                this.formRef?.current?.getFieldValue(
                    'deployment_possible_roles'
                ) ||
                viewData.form_data.deployment_possible_roles ||
                [];

            possibleSubtasks = deployment_possible_roles
                .map(
                    (role) =>
                        this.formRef?.current?.getFieldValue(
                            `sbtsk_fr_${role}`
                        ) || viewData.form_data?.[`sbtsk_fr_${role}`]
                )
                .filter(Boolean); // Filter out any undefined/null values
        } else {
            possibleSubtasks = this.getApplicableSubTasks();
        }

        return allSubtasks.filter((task) =>
            possibleSubtasks.includes(task?.value)
        );
    }

    getRowDataForSubtaskStatusMovementRules() {
        let rulesJson = this.formRef?.current?.getFieldValue(
            'sbtsk_status_movement_based_on_selected_srvc_status_update_rules'
        );

        let existingRules;
        if (rulesJson) {
            existingRules = JSON.parse(rulesJson);
        }

        const applicableSubtasksList = this.getAllApplicableSbtsk();
        const selectedSrvcStatuses = this.formRef?.current?.getFieldValue(
            'srvc_type_statuses_fr_automation'
        );
        if (!applicableSubtasksList) {
            return;
        }
        if (!selectedSrvcStatuses) {
            return;
        }
        let rowData = [];

        if (selectedSrvcStatuses) {
            selectedSrvcStatuses.forEach((singleSelectedSbtskTsk) => {
                // for each status and each org add a row
                if (applicableSubtasksList) {
                    applicableSubtasksList.forEach((singleOrgData) => {
                        //make row_data for table
                        let row_id =
                            singleOrgData.value + '_' + singleSelectedSbtskTsk;
                        let rowDataObj = {
                            row_id: row_id,
                            sbtsk_type: singleOrgData.value,
                            serive_type_status: singleSelectedSbtskTsk,
                        };

                        if (existingRules) {
                            let existingRule = existingRules.filter(
                                (singleRule) => singleRule.row_id == row_id
                            )[0];

                            if (existingRule != undefined) {
                                // console.log('existingRule',existingRule);
                                rowDataObj['sbtsk_status'] =
                                    existingRule['sbtsk_status'];
                            }
                        }
                        rowData.push(rowDataObj);
                    });
                }
            });
        }

        return rowData;
    }

    getColumnMetaForSubtaskStatusAutomation(srvcStatuses) {
        const applicableSubtasksByServiceType = this.getAllApplicableSbtsk();
        const selectedServiceTypeStatuses =
            this.formRef.current?.getFieldValue(
                'srvc_type_statuses_fr_automation'
            ) || this.state.viewData.srvc_type_statuses_fr_automation;

        const filteredServiceStatuses = srvcStatuses.filter(
            (singleSrvcStatus) =>
                selectedServiceTypeStatuses?.includes(singleSrvcStatus.value)
        );

        let subtaskTypeOptions = [];
        let statusOptionsBySubtaskType = {};
        if (applicableSubtasksByServiceType) {
            applicableSubtasksByServiceType.map((singleOrgData) => {
                let srvcTypeObj = {
                    label: singleOrgData.label,
                    value: singleOrgData.value,
                };
                subtaskTypeOptions.push(srvcTypeObj);
                statusOptionsBySubtaskType[singleOrgData.value] =
                    singleOrgData.sbtsk_statuses;
            });
        }

        let colMeta = [
            {
                key: 'sbtsk_type',
                label: 'Subtask type',
                widget: 'select',
                widgetProps: {
                    disabled: true,
                },
                options: subtaskTypeOptions,
            },
            {
                key: 'serive_type_status',
                label: 'Service Type Status',
                widget: 'select',
                widgetProps: {
                    disabled: true,
                },
                options: filteredServiceStatuses,
            },
            {
                key: 'sbtsk_status',
                label: 'Subtask Status',
                widget: 'select',
                options: [],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                disable_filter: true,
                dynamicMeta: (row, originalFieldMeta) => {
                    let dynamicMetaRow = { ...originalFieldMeta };

                    if (statusOptionsBySubtaskType[row.sbtsk_type]) {
                        dynamicMetaRow['options'] = [
                            ...dynamicMetaRow['options'],
                            ...statusOptionsBySubtaskType[row.sbtsk_type],
                        ];
                    }

                    return dynamicMetaRow;
                },
            },
        ];
        // console.log("colMeta-",colMeta);
        return colMeta;
    }
    getAutoMoveSubtaskStatusConfigMeta() {
        const initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};
        let autoMoveEnabled =
            this.formRef.current?.getFieldValue(
                'auto_move_subtask_status_enabled'
            ) == undefined
                ? this.state.viewData.form_data.auto_move_subtask_status_enabled
                : this.formRef.current?.getFieldValue(
                      'auto_move_subtask_status_enabled'
                  );

        let srvc_statuses = this.getStatusesAsOptions();
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: <b>Enable ?</b>,
                    key: 'auto_move_subtask_status_enabled',
                    widget: 'checkbox',
                    onChange: () => {
                        this.refresh();
                    },
                },
                ...(autoMoveEnabled
                    ? [
                          {
                              key: 'srvc_type_statuses_fr_automation',
                              label: 'Service Type Statuses',
                              widget: 'select',
                              widgetProps: {
                                  allowClear: true,
                              },
                              placeholder: 'Select service statuses',
                              options: srvc_statuses,
                              onChange: (value) => {
                                  const newRowData =
                                      this.getRowDataForSubtaskStatusMovementRules();
                                  this.formRef.current.setFieldsValue({
                                      sbtsk_status_movement_based_on_selected_srvc_status_update_rules:
                                          JSON.stringify(newRowData),
                                  });
                                  this.refresh();
                              },
                              widgetProps: {
                                  mode: 'multiple',
                                  allowClear: true,
                                  showSearch: true,
                                  optionFilterProp: 'children',
                              },
                          },
                          {
                              key: 'sbtsk_status_movement_based_on_selected_srvc_status_update_rules',
                              className: 'gx-d-none',
                              widgetProps: {
                                  hidden: true,
                              },
                          },
                          {
                              key: `automation_sbtsk_statuses_movement_based_on_srvc_update_rules_table`,
                              render: () => {
                                  return (
                                      <InputTable
                                          // demoMode={true}
                                          // rowData = {[{'test':123}]}
                                          onChange={(newRowData) => {
                                              this.formRef.current.setFieldsValue(
                                                  {
                                                      sbtsk_status_movement_based_on_selected_srvc_status_update_rules:
                                                          JSON.stringify(
                                                              newRowData
                                                          ),
                                                  }
                                              );
                                          }}
                                          rowData={this.getRowDataForSubtaskStatusMovementRules()}
                                          colMeta={this.getColumnMetaForSubtaskStatusAutomation(
                                              srvc_statuses
                                          )}
                                      />
                                  );
                              },
                          },
                      ]
                    : []),
            ],
        };
        return meta;
    }

    getSubtaskAutomationsMeta = (initialValues) => {
        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    label: 'Subtask Type automation',
                    key: 'sbtsk_type_automation',
                    formItemProps: {
                        style: {
                            display: 'none',
                        },
                    },
                },
                {
                    render: () => {
                        return (
                            <div className="gx-mt-1">
                                <Alert
                                    message={
                                        <div>
                                            Here, you can configure auto subtask
                                            status movement based on service
                                            request status change.
                                            <ul>
                                                <li>
                                                    The list of subtasks will be
                                                    fetched from : Applicable
                                                    subtask type (Task based
                                                    service type)
                                                </li>
                                                <li>
                                                    The list of subtasks will be
                                                    fetched from : Deployment
                                                    tab (Project based service
                                                    type)
                                                </li>
                                            </ul>
                                        </div>
                                    }
                                    type="info"
                                />
                            </div>
                        );
                    },
                },
                {
                    key: 'configure_automation_srvc_status',
                    render: () => {
                        return (
                            <div className="gx-mb-3">
                                <Collapse
                                    className="gx-mt-0 gx-mb-3"
                                    onChange={
                                        () => {}
                                        // this.getAllPossibleSrvcTypesFrSrvcPrvdr()
                                    }
                                >
                                    <Collapse.Panel
                                        forceRender={true}
                                        header={
                                            'Configure Subtask Status Movement Based on Service Request status'
                                        }
                                    >
                                        <FormBuilder
                                            meta={this.getAutoMoveSubtaskStatusConfigMeta()}
                                            form={this.formRef}
                                        />
                                    </Collapse.Panel>
                                </Collapse>
                            </div>
                        );
                    },
                },
            ],
        };
        return meta;
    };

    getAutoAssignAuthorityFields = () => {
        let authorities_list = this.state?.viewData.authorities_list;
        let srvc_authorities =
            this.formRef?.current?.getFieldValue('srvc_authorities') ||
            this.state.viewData.form_data.srvc_authorities;
        let auto_assign_authority = [];
        if (srvc_authorities && srvc_authorities.length > 0) {
            srvc_authorities.forEach((singleSrvcAuthority) => {
                let seletcedRoleTitle = authorities_list?.filter(
                    (singleAuthority) =>
                        singleAuthority.value == singleSrvcAuthority
                )?.[0]?.label;
                let auto_assign_authority_fields = [
                    {
                        key: `${singleSrvcAuthority}_auto_assign_based_on_location`,
                        label: `${seletcedRoleTitle}- auto assign based on location`,
                        widget: 'checkbox',
                    },
                    {
                        key: `${singleSrvcAuthority}_apply_to_existing_requests`,
                        label: `Apply to unassigned requests`,
                        render: () => {
                            return (
                                <div className="gx-d-inline-block gx-mr-3 gx-mt-2">
                                    <Tooltip
                                        placement="right"
                                        title="On click of this, auto assignment will be done for the requests where there is no authority assigned and the request is in active status."
                                    >
                                        <Button
                                            onClick={() =>
                                                this.handleApplyToExistingRequestsClick(
                                                    singleSrvcAuthority
                                                )
                                            }
                                            disabled={
                                                this.state
                                                    .applyingAutoAssignAuthorityFrRole !=
                                                undefined
                                            }
                                        >
                                            Apply to unassigned requests
                                        </Button>
                                    </Tooltip>
                                    {this.state
                                        .applyingAutoAssignAuthorityFrRole ===
                                        singleSrvcAuthority && (
                                        <div className="gx-mt-2 gx-ml-2 gx-d-inline-block">
                                            <Spin />
                                        </div>
                                    )}
                                </div>
                            );
                        },
                    },
                    {
                        key: `${singleSrvcAuthority}_apply_to_all_active_requests`,
                        label: `Apply to all active requests`,
                        render: () => {
                            return (
                                <div className="gx-d-inline-block">
                                    <Tooltip
                                        placement="right"
                                        title="On click of this, auto assignment will be done for all active requests."
                                    >
                                        <Button
                                            onClick={() =>
                                                this.handleApplyToAllActiveRequestsClick(
                                                    singleSrvcAuthority
                                                )
                                            }
                                            disabled={
                                                this.state
                                                    .applyingAutoAssignAuthorityFrAllActiveReq !=
                                                undefined
                                            }
                                        >
                                            Apply to all active requests
                                        </Button>
                                    </Tooltip>
                                    {this.state
                                        .applyingAutoAssignAuthorityFrAllActiveReq ===
                                        singleSrvcAuthority && (
                                        <div className="gx-mt-2 gx-ml-2 gx-d-inline-block">
                                            <Spin />
                                        </div>
                                    )}
                                </div>
                            );
                        },
                    },
                ];

                auto_assign_authority.push(...auto_assign_authority_fields);
            });
        } else {
            return [
                {
                    render: () => {
                        return (
                            <div className="gx-d-flex gx-mt-2">
                                <p>
                                    No authorities configured for this service
                                    type
                                </p>
                            </div>
                        );
                    },
                },
            ];
        }
        return auto_assign_authority;
    };

    getAutoMovementFieldsformMeta = (initialValues) => {
        let statusWiseAutoMovementFields = [];
        let statuses = this.getStatuses();
        // console.log("statuses", statuses);

        for (const status_type in statuses) {
            if (Object.hasOwnProperty.call(statuses, status_type)) {
                const statusesFrType = statuses[status_type];
                statusesFrType.forEach((singleStatusesFrType) => {
                    if (singleStatusesFrType.key != 'open') {
                        statusWiseAutoMovementFields.push({
                            label: (
                                <span>
                                    <b>{singleStatusesFrType.title}</b>
                                </span>
                            ),
                            key: `srvc_auto_movement_fr_${singleStatusesFrType.key}_key`,
                            widget: 'select',
                            options:
                                this.getPossibleSpecificFieldsFrAutomationMovementFileds(
                                    singleStatusesFrType.key
                                ),
                            onChange: () => {
                                this.forceUpdate();
                            },
                            widgetProps: {
                                allowClear: true,
                                mode: 'multiple',
                            },
                        });
                    }
                });
            }
        }

        const meta = {
            formItemLayout: null,
            initialValues: initialValues,
            fields: [
                {
                    render: () => {
                        return (
                            <h4 className="gx-mb-md-3">
                                Select Automation Movement Fields
                            </h4>
                        );
                    },
                },
                ...statusWiseAutoMovementFields,
            ],
        };
        return meta;
    };

    getAutoMovementFieldsConfigMeta(initialValues) {
        return {
            fields: [
                {
                    key: `srvc_type_auto_movement_fields_config`,
                    render: () => {
                        return (
                            <Tabs defaultActiveKey="fields" className="gx-mb-2">
                                <Tabs.TabPane tab="Fields" key="fields">
                                    {/* Create a new component for Fields for Automation */}
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getAutoMovementFieldsformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    tab="Authorities"
                                    key="authorities"
                                >
                                    {/* Create a new component for Authorities for Automation */}
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getAutoMovementAuthoritiesformMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane tab="Readiness" key="readiness">
                                    {/* Create a new component for Readiness */}
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getReadinessFieldsMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    data-testid="assignment-tab"
                                    tab="Assignment"
                                    key="assignment"
                                >
                                    {/* Create a new component for Assignment */}
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getAssignmentMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    data-testid="periodic-tab"
                                    tab="Periodic"
                                    key="periodic"
                                >
                                    {/* Create a new component for Assignment */}
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getPeriodicAutomationsMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    data-testid="subtask-tab"
                                    tab="Subtask"
                                    key="subtask"
                                >
                                    <FormBuilder
                                        form={this.formRef}
                                        meta={this.getSubtaskAutomationsMeta(
                                            initialValues
                                        )}
                                    />
                                </Tabs.TabPane>
                            </Tabs>
                        );
                    },
                },
            ],
        };
    }

    getTabularViewFieldsConfigMeta() {
        return {
            fields: [
                {
                    label: 'Select table columns',
                    key: 'srvc_type_tabular_view_columns',
                    widget: 'select',
                    options: getSrvcReqStaticAndCustomPossibleFields(
                        this.getPossibleSpecificFields(),
                        [],
                        false,
                        getAuthorityLabelValuePairsWithPrefix(
                            this.getPossibleAuthorities()
                        )
                    ),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
            ],
        };
    }

    getFilterViewFieldsConfigMeta() {
        let statuses = this.getStatuses();
        let allStatusesArr = [];
        if (statuses) {
            allStatusesArr = Object.values(statuses).reduce(
                (acc, val) => acc.concat(val),
                []
            );
        }

        return {
            fields: [
                {
                    key: 'select_brand_filetrs',
                    label: 'Select Brand Filters',
                    widget: 'select',
                    options: getStaticAndCustomFieldsFilter(
                        this.getPossibleSpecificFields(),
                        this.getPossibleAuthoritiesFrFilter(),
                        allStatusesArr
                    ),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
            ],
        };
    }

    getDailyUpdatesMeta() {
        let possible_roles = this.state.viewData?.authorities_list || [];
        var daily_update_issue_fields = this.formRef?.current?.getFieldValue(
            'daily_update_issue_form_fields'
        );
        // console.log('Customer fields',daily_update_issue_fields);
        let initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};
        if (daily_update_issue_fields == undefined) {
            daily_update_issue_fields =
                initialValues?.daily_update_issue_form_fields;
        }
        var daily_update_form_fields = this.formRef?.current?.getFieldValue(
            'daily_update_form_fields'
        );
        if (daily_update_form_fields == undefined) {
            daily_update_form_fields = initialValues?.daily_update_form_fields;
        }
        const isTrackingAllLineItemWiseProgress =
            this.formRef?.current?.getFieldValue(
                'daily_update_track_line_item_progress'
            ) ||
            this.state.viewData?.form_data
                ?.daily_update_track_line_item_progress;
        return {
            fields: [
                {
                    render: () => (
                        <h3 className="gx-mb-2">
                            Configure daily updates form
                        </h3>
                    ),
                },
                {
                    key: 'daily_update_who_can_edit',
                    label: 'Who can edit daily updates ?',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                    },
                    options: possible_roles,
                },
                {
                    label: 'Daily update form fields',
                    key: 'daily_update_form_fields',
                    widget: 'textarea',
                    placeholder: 'Please paste the JSON here',
                    onChange: (event) => {
                        this.forceUpdate();
                    },
                },
                {
                    render: () => {
                        return (
                            <div>
                                {daily_update_form_fields != '' && (
                                    <div>
                                        <Button
                                            onClick={(e) =>
                                                this.handleShowFieldsPreviewClick(
                                                    daily_update_form_fields
                                                )
                                            }
                                            icon={<ZoomInOutlined />}
                                        >
                                            Form preview
                                        </Button>
                                    </div>
                                )}
                                <Link
                                    to={
                                        '/fields-creator?edit=' +
                                        encodeURIComponent(
                                            daily_update_form_fields
                                        )
                                    }
                                    target="_blank"
                                >
                                    Open field creator {'-->'}
                                </Link>
                            </div>
                        );
                    },
                },
                {
                    render: () => <hr></hr>,
                },
                {
                    key: `daily_update_will_have_issues`,
                    label: 'Will have issues?',
                    widget: 'radio-group',
                    options: ['Yes', 'No'],
                },
                {
                    label: 'Issue form fields',
                    key: 'daily_update_issue_form_fields',
                    widget: 'textarea',
                    placeholder: 'Please paste the JSON here',
                    onChange: (event) => {
                        // this.setState({
                        //     render_helper:!this.state.render_helper
                        // })
                        this.forceUpdate();
                    },
                },
                {
                    render: () => {
                        return (
                            <div>
                                {daily_update_issue_fields != '' && (
                                    <div>
                                        <Button
                                            onClick={(e) =>
                                                this.handleShowFieldsPreviewClick(
                                                    daily_update_issue_fields
                                                )
                                            }
                                            icon={<ZoomInOutlined />}
                                        >
                                            Form preview
                                        </Button>
                                    </div>
                                )}
                                <Link
                                    to={
                                        '/fields-creator?edit=' +
                                        encodeURIComponent(
                                            daily_update_issue_fields
                                        )
                                    }
                                    target="_blank"
                                >
                                    Open field creator {'-->'}
                                </Link>
                            </div>
                        );
                    },
                },
                {
                    render: () => <hr></hr>,
                },
                {
                    key: 'daily_progress_update_mode',
                    label: 'Daily progress update mode',
                    widget: 'select',
                    widgetProps: {
                        defaultValue: 'lenient_mode',
                    },
                    options: DailyUpdateModes,
                },
                {
                    key: 'daily_update_track_line_item_progress',
                    label: 'Track line item wise progress & assignment?',
                    tooltip:
                        'Updater will be able to update line item wise progress and cummulative progress will be calculated automatically',
                    widget: 'radio-group',
                    options: ['Yes', 'No'],
                    onChange: () => {
                        this.refresh();
                    },
                },
                ...(isTrackingAllLineItemWiseProgress === 'Yes'
                    ? [
                          {
                              key: 'show_line_item_by_selection',
                              widget: (props) => (
                                  <InlineCheckbox
                                      label="Show line items by selection"
                                      tooltip="If enabled, line item progress will be shown only for the selected line items."
                                      value={props.value}
                                      onChange={(e) =>
                                          props.onChange(e.target.checked)
                                      }
                                  />
                              ),
                          },
                      ]
                    : []),
                {
                    key: 'daily_update_dynamic_line_item_wise_files',
                    label: 'Enable line item wise photos/videos ?',
                    tooltip:
                        'The updater will be able to upload photo videos with respect to each line item (in addition to a general photo section)',
                    widget: 'radio-group',
                    options: ['Yes', 'No'],
                },
            ],
        };
    }

    getDeploymentConfigMeta() {
        const startTimeFrEndTime = this.formRef.current?.getFieldValue(
            'deployment_time_slot_lower_limit'
        );
        let role_vs_subtask_fields = [];
        let possible_roles = this.state.viewData?.authorities_list || [];
        var deployment_roles =
            this.formRef?.current?.getFieldValue('deployment_possible_roles') ||
            this.state.viewData?.form_data?.deployment_possible_roles;
        if (deployment_roles) {
            deployment_roles.forEach((singleDeploymentRoleId) => {
                let role_details =
                    possible_roles.filter(
                        (singleRole) =>
                            singleRole.value == singleDeploymentRoleId
                    )[0] || {};
                role_vs_subtask_fields.push({
                    key: `sbtsk_fr_${singleDeploymentRoleId}`,
                    label: `Subtask for ${role_details.label}`,
                    required: true,
                    widget: 'select',
                    options:
                        this.state.viewData?.applicable_subtasks_list || [],
                });
            });
        }
        return {
            fields: [
                {
                    key: 'deployment_who_can_edit',
                    label: 'Who can edit deployment ?',
                    widget: 'select',
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                    },
                    options: possible_roles,
                },
                {
                    key: 'deployment_time_slot_lower_limit',
                    label: (
                        <span>
                            <i className="icon icon-timepicker gx-mr-2"></i>
                            Start Time Slot (Lower Limit)
                        </span>
                    ),
                    widget: TimePickerWidget,
                    widgetProps: {
                        beginLimit: startOfDay,
                        endLimit: endOfDay,
                        step: 15,
                        onChange: (e) => {
                            this.refresh();
                        },
                    },
                },
                {
                    key: 'deployment_time_slot_upper_limit',
                    label: (
                        <span>
                            <i className="icon icon-timepicker gx-mr-2"></i>End
                            Time Slot (Upper Limit)
                        </span>
                    ),
                    widget: TimePickerWidget,
                    widgetProps: {
                        beginLimit: startTimeFrEndTime || startOfDay,
                        endLimit: endOfDay,
                        step: 15,
                    },
                },
                {
                    key: `deployment_possible_roles`,
                    label: 'Please select roles to deploy',
                    widget: 'select',
                    placeholder: 'Select roles',
                    onChange: () => this.refresh(),
                    widgetProps: {
                        mode: 'multiple',
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    options: this.state.viewData?.authorities_list || [],
                },
                ...role_vs_subtask_fields,
            ],
        };
    }

    getPricingConfig() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_pricing_config_for_line_item'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            const config_in_form_data =
                this.state.viewData?.form_data
                    ?.srvc_type_pricing_config_for_line_item;
            return config_in_form_data ? JSON.parse(config_in_form_data) : {};
        }
        return {};
    }

    getPricingConfigFrManday() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_pricing_config_for_manday'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            const config_in_form_data =
                this.state.viewData?.form_data
                    ?.srvc_type_pricing_config_for_manday;
            return config_in_form_data ? JSON.parse(config_in_form_data) : {};
        }
        return {};
    }

    getLineItemConfig() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_line_item_config'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            return (
                this.state.viewData?.form_data?.srvc_type_line_item_config || {}
            );
        }
        return {};
    }

    getBillingDiscountConfig() {
        if (this.state.editMode) {
            if (this.formRef?.current?.getFieldValue) {
                const fieldJson = this.formRef.current.getFieldValue(
                    'srvc_type_billing_discounting_rule_config'
                );
                if (fieldJson) {
                    return JSON.parse(fieldJson);
                }
            }
            let initialFieldJson =
                this.state.viewData?.form_data
                    ?.srvc_type_billing_discounting_rule_config;
            if (initialFieldJson) {
                return JSON.parse(initialFieldJson);
            }
        }
        return {};
    }

    getMetaFrLineItemConfiguration() {
        const { activeTabKey } = this.state;
        return [
            {
                label: 'Service type line item config json',
                key: 'srvc_type_line_item_config',
                formItemProps: {
                    style: {
                        display: 'none',
                    },
                },
            },
            {
                key: `show_line_items_to_sp`,
                label: 'Show line items to SP',
                widget: 'checkbox',
                // onChange: () => this.refresh(),
            },
            {
                key: 'srvc_type_line_item_common_qty_label',
                label: 'Label for total of all qty colums across line item groups',
            },
             ...(activeTabKey === 'line_items' ? [
                {
                    key: 'Line items',
                    render: () => (
                        <>
                            <LineItemManagementModule
                                onChange={(newObj) => {
                                    this.formRef.current.setFieldsValue({
                                        srvc_type_line_item_config:
                                            JSON.stringify(newObj),
                                    });
                                    this.refresh();
                                }}
                                currValue={this.getLineItemConfig()}
                            />
                        </>
                    ),
                },
                {
                    key: 'seperator_6',
                    render: () => (
                        <div className="gx-mb-3 gx-bg-grey">
                            <hr></hr>
                        </div>
                    ),
                }
            ] : [])
        ];
    }

    getMetaFrAutoStatusMovementFrSingleSubtaskType(subtype_id) {
        let full_prefix = `auto_status_change_fr_subtask_${subtype_id}`;
        let status_type = [];
        let statuses = this.getStatuses();
        let isSubTypeEnabled = this.isAutoStatusChangeEnabledFrSubtask(
            `${full_prefix}_enabled`
        );
        if (statuses != undefined) {
            Object.keys(statuses).map((statusKey) => {
                statuses[statusKey].map((singleStatus) => {
                    let statusSelectOption = {};
                    statusSelectOption['label'] = singleStatus.title;
                    statusSelectOption['value'] = singleStatus.key;
                    status_type.push(statusSelectOption);
                });
            });
        }

        var fields = [
            {
                key: `${full_prefix}_enabled`,
                label: 'Enable',
                widget: 'checkbox',
                onChange: () => this.refresh(),
            },
        ];

        if (isSubTypeEnabled) {
            fields.push({
                key: `${full_prefix}_status_key`,
                label: 'Auto move service request status to',
                widget: 'select',
                placeholder: 'Select status type',
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                options: status_type,
            });
        }

        return {
            // formItemLayout: null,
            fields,
        };
    }

    getMetaFrAutoStatusMovement() {
        let subTaskWiseFields = [];
        let statuses = this.getStatuses();
        let subTypes = this.state.viewData?.applicable_subtasks_list;
        let initialValues = this.state.editMode
            ? this.state.viewData?.form_data
            : {};
        let applicableSubTasks = this.getApplicableSubTasks();
        if (statuses != undefined && subTypes != undefined) {
            try {
                for (const sub_type in subTypes) {
                    if (Object.hasOwnProperty.call(subTypes, sub_type)) {
                        const singleSubType = subTypes[sub_type];
                        const singleSubTypeKey = singleSubType.value;
                        if (applicableSubTasks.includes(singleSubType.value)) {
                            subTaskWiseFields.push({
                                key: `subtask_type_status_${singleSubTypeKey}_fields`,
                                render: () => {
                                    return (
                                        <div className="gx-mb-2">
                                            {
                                                <div>
                                                    <Collapse
                                                        onChange={() => {
                                                            // alert('Hello')
                                                            this.refreshPageForNotificationMeta();
                                                        }}
                                                    >
                                                        <Collapse.Panel
                                                            key={
                                                                singleSubTypeKey
                                                            }
                                                            forceRender={true}
                                                            header={
                                                                <span>
                                                                    {
                                                                        singleSubType.label
                                                                    }
                                                                </span>
                                                            }
                                                        >
                                                            <>
                                                                <FormBuilder
                                                                    meta={{
                                                                        ...this.getMetaFrAutoStatusMovementFrSingleSubtaskType(
                                                                            singleSubTypeKey
                                                                        ),
                                                                        initialValues:
                                                                            initialValues,
                                                                    }}
                                                                    form={
                                                                        this
                                                                            .formRef
                                                                    }
                                                                />
                                                            </>
                                                        </Collapse.Panel>
                                                    </Collapse>
                                                </div>
                                            }
                                        </div>
                                    );
                                },
                            });
                        }
                    }
                }
            } catch (error) {
                console.log(
                    'Error while createing dynamic stage wise fields',
                    error
                );
            }
        }
        const meta = {
            // formItemLayout: null,
            // initialValues : this.state.editMode ? this.state.viewData?.form_data : {},
            fields: [...subTaskWiseFields],
        };
        return meta;
    }

    getApplicableSubTasks() {
        // console.log("Tried calling get statuses");
        // console.log("FormRef",this.formRef?.current?.getFieldValue('srvc_appl_sub_tasks'));
        var subTasksFrmFormField = this.formRef?.current?.getFieldValue(
            'srvc_appl_sub_tasks'
        );
        return subTasksFrmFormField;
    }

    isAutoStatusChangeEnabledFrSubtask(full_key) {
        // console.log("Tried calling get statuses");
        // console.log("FormRef",this.formRef?.current?.getFieldValue(full_key),full_key);
        var subTasksFrmFormField =
            this.formRef?.current?.getFieldValue(full_key);
        return subTasksFrmFormField;
    }

    refresh() {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    }

    getSmsNotificationMeta(status_key) {
        let full_prefix = `cnsmr_ntfctn_fr_status_${status_key}_sms`;
        let custom_fields_meta = [];
        const custom_sms_templates_status_type_wise =
            this.state.viewData?.custom_sms_templates || [];
        let status_type = status_type_templates_;
        let status_type_val = this.formRef?.current?.getFieldValue(
            `${full_prefix}_status_type`
        );

        let status_type_templates = [];
        let selected_template_preview = '';
        if (status_type_val != undefined && status_type_val != '') {
            let customSmsTemplates =
                custom_sms_templates_status_type_wise.filter(
                    (item) => item.value == status_type_val
                )[0]?.templates || [];
            status_type_templates = status_type.filter(
                (item) => item.value == status_type_val
            )[0].templates;
            status_type_templates = [
                ...status_type_templates,
                ...customSmsTemplates,
            ];
            let sms_template = this.formRef?.current?.getFieldValue(
                `${full_prefix}_template`
            );

            if (sms_template != undefined && sms_template != '') {
                let selected_status_type_template =
                    status_type_templates.filter(
                        (item) => item.value == sms_template
                    )[0];

                if (
                    selected_status_type_template != undefined &&
                    selected_status_type_template != ''
                ) {
                    selected_template_preview =
                        selected_status_type_template.label;
                    let selected_custom_fields_meta =
                        selected_status_type_template.custom_fields_meta;
                    let filledCustomVars = {};
                    if (
                        selected_custom_fields_meta != undefined &&
                        selected_custom_fields_meta != ''
                    ) {
                        selected_custom_fields_meta.map((singleField) => {
                            let copy_field = { ...singleField };
                            copy_field.key =
                                full_prefix + '_' + singleField.key;
                            copy_field['onChange'] = () => this.refresh();
                            custom_fields_meta.push(copy_field);
                            filledCustomVars[`%${singleField.key}%`] =
                                this.formRef?.current?.getFieldValue(
                                    copy_field.key
                                );
                        });
                    }
                    // parsing template to get preview
                    selected_template_preview = parseSMSTemplateWithValues(
                        selected_status_type_template.value,
                        filledCustomVars
                    );
                }
            }
        }

        return {
            // formItemLayout: null,
            fields: [
                {
                    key: `${full_prefix}_enabled`,
                    label: 'Enable',
                    widget: 'checkbox',
                },
                {
                    key: `${full_prefix}_status_type`,
                    label: 'Status Type',
                    widget: 'select',
                    placeholder: 'Select status type',
                    onChange: () => this.refresh(),
                    options: status_type,
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
                {
                    key: `${full_prefix}_template`,
                    label: 'Template',
                    widget: 'select',
                    widgetProps: {
                        allowClear: true,
                    },
                    placeholder: 'Select template',
                    options: status_type_templates,
                    onChange: () => this.refresh(),
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
                ...custom_fields_meta,
                {
                    key: `${full_prefix}_template_preview`,
                    render: () => {
                        return (
                            <div className="gx-bg-amber-light gx-p-3">
                                <h4 className="gx-text-center">Preview</h4>
                                <p className="gx-text-red">
                                    {selected_template_preview}
                                </p>
                            </div>
                        );
                    },
                },
            ],
        };
    }

    getWhatsAppMessageNotificationMeta(status_key) {
        let full_prefix = `cnsmr_ntfctn_fr_status_${status_key}_whatsapp_message`;
        // console.log("full_prefix",full_prefix)
        let custom_fields_meta = [];
        const custom_whatsapp_templates_status_type_wise =
            this.state.viewData?.custom_whatsapp_templates || [];
        let status_type = status_type_templates_fr_whatsapp;
        // console.log("status_type",status_type);

        let status_type_val = this.formRef?.current?.getFieldValue(
            `${full_prefix}_status_type`
        );
        // console.log("status_type_val",this.formRef?.current?.getFieldValue(`${full_prefix}_status_type`))

        let status_type_templates = [];
        let selected_template_preview = '';

        if (status_type_val != undefined && status_type_val != '') {
            let customWhatsAppTemplates =
                custom_whatsapp_templates_status_type_wise.filter(
                    (item) => item.value == status_type_val
                )[0]?.templates || [];
            status_type_templates = status_type.filter(
                (item) => item.value == status_type_val
            )[0].templates;
            status_type_templates = [
                ...status_type_templates,
                ...customWhatsAppTemplates,
            ];
            let whatsapp_template = this.formRef?.current?.getFieldValue(
                `${full_prefix}_template`
            );

            if (whatsapp_template != undefined && whatsapp_template != '') {
                let selected_status_type_template =
                    status_type_templates.filter(
                        (item) => item.value == whatsapp_template
                    )[0];

                if (
                    selected_status_type_template != undefined &&
                    selected_status_type_template != ''
                ) {
                    selected_template_preview =
                        selected_status_type_template.label;
                    let selected_custom_fields_meta =
                        selected_status_type_template.custom_fields_meta;
                    let filledCustomVars = {};
                    if (
                        selected_custom_fields_meta != undefined &&
                        selected_custom_fields_meta != ''
                    ) {
                        selected_custom_fields_meta.map((singleField) => {
                            let copy_field = { ...singleField };
                            copy_field.key =
                                full_prefix + '_' + singleField.key;
                            copy_field['onChange'] = () => this.refresh();
                            custom_fields_meta.push(copy_field);
                            filledCustomVars[`%${singleField.key}%`] =
                                this.formRef?.current?.getFieldValue(
                                    copy_field.key
                                );
                        });
                    }
                    // parsing template to get preview
                    selected_template_preview = parseWhatsappTemplateWithValues(
                        selected_status_type_template.value,
                        filledCustomVars
                    );
                }
            }
            // console.log("status_type_val2",this.formRef?.current?.getFieldValue(`${full_prefix}_status_type`))
        }

        return {
            // formItemLayout: null,
            fields: [
                {
                    key: `${full_prefix}_enabled`,
                    label: 'Enable',
                    widget: 'checkbox',
                },
                {
                    key: `${full_prefix}_status_type`,
                    label: 'Status Type',
                    widget: 'select',
                    placeholder: 'Select status type',
                    onChange: () => this.refresh(),
                    options: status_type,
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
                {
                    key: `${full_prefix}_template`,
                    label: 'Template',
                    widget: 'select',
                    widgetProps: {
                        allowClear: true,
                    },
                    placeholder: 'Select template',
                    options: status_type_templates,
                    onChange: () => this.refresh(),
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                },
                ...custom_fields_meta,
                {
                    key: `${full_prefix}_template_preview`,
                    render: () => {
                        return (
                            <div className="gx-bg-amber-light gx-p-3">
                                <h4 className="gx-text-center">
                                    Preview{' '}
                                    <WhatsAppOutlined className="gx-text-green gx-fs-xl" />{' '}
                                </h4>
                                <p className="gx-text-red">
                                    {selected_template_preview}
                                </p>
                            </div>
                        );
                    },
                },
            ],
        };
    }

    getWhatsAppFeedbackMessageMeta() {
        const customWhatsAppFeedbackTemplates =
            this.state.viewData?.custom_whatsapp_feedback_templates || [];
        let feedbackWhatsAppMessageTemplates = [
            ...consumer_feedback_templates_fr_whatsapp,
            ...customWhatsAppFeedbackTemplates,
        ];
        let custom_fields_meta = [];
        let selected_template_preview = '';
        let selectedWhatsAppFeedbackTemplate =
            this.formRef?.current?.getFieldValue(
                'select_whatsApp_feedback_template'
            ) ||
            this.state.viewData?.form_data?.select_whatsApp_feedback_template;

        if (!selectedWhatsAppFeedbackTemplate && this.formRef?.current) {
            this.formRef.current.setFieldsValue({
                select_whatsApp_feedback_template:
                    'Dear %cust_name%, for %display_code% please share your feedback at WIFY - %feedback_link%',
            });
        }

        if (
            selectedWhatsAppFeedbackTemplate != undefined &&
            selectedWhatsAppFeedbackTemplate != ''
        ) {
            let selected_status_type_template_object =
                feedbackWhatsAppMessageTemplates.filter(
                    (item) => item.value == selectedWhatsAppFeedbackTemplate
                )[0];
            selected_template_preview =
                selected_status_type_template_object?.label;
            let selected_custom_fields_meta =
                selected_status_type_template_object?.custom_fields_meta;
            let filledCustomVars = {};
            if (
                selected_custom_fields_meta != undefined &&
                selected_custom_fields_meta != ''
            ) {
                selected_custom_fields_meta.map((singleField) => {
                    let copy_field = { ...singleField };
                    copy_field.key = singleField.key;
                    copy_field['onChange'] = () => this.refresh();
                    custom_fields_meta.push(copy_field);
                    filledCustomVars[`%${singleField.key}%`] =
                        this.formRef?.current?.getFieldValue(copy_field.key);
                });
            }
            if (selected_status_type_template_object) {
                // parsing template to get preview
                selected_template_preview =
                    parseWhatsappFeedbackTemplateWithValues(
                        selected_status_type_template_object?.value,
                        filledCustomVars
                    );
            }
        }

        let sendFeedbackWhatsappMsg = this.formRef?.current?.getFieldValue(
            'srvc_feedback_send_by_whatsapp'
        );

        if (sendFeedbackWhatsappMsg) {
            return [
                {
                    key: `select_whatsApp_feedback_template`,
                    label: 'Select WhatsApp feedback template',
                    widget: 'select',
                    placeholder: 'Select template',
                    options: feedbackWhatsAppMessageTemplates,
                    onChange: () => {
                        this.refresh();
                    },
                    widgetProps: {
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    required: true,
                },
                ...custom_fields_meta,
                {
                    key: `whatsapp_feedback_template_preview`,
                    render: () => {
                        return (
                            <div className="gx-bg-amber-light gx-p-3">
                                <h4 className="gx-text-center">
                                    Preview{' '}
                                    <WhatsAppOutlined className="gx-text-green gx-fs-xl" />{' '}
                                </h4>
                                <p className="gx-text-red">
                                    {selected_template_preview}
                                </p>
                            </div>
                        );
                    },
                },
            ];
        } else {
            return [];
        }
    }
    getSmsFeedbackMessageMeta() {
        const customSmsFeedbackTemplates =
            this.state.viewData?.custom_sms_feedback_templates || [];
        const feedbackSmsMessageTemplates = [
            ...consumer_feedback_templates_fr_sms,
            ...customSmsFeedbackTemplates,
        ];
        let customFieldsMeta = [];
        let selectedTemplatePreview = '';
        const selectedSmsFeedbackTemplate =
            this.formRef?.current?.getFieldValue(
                'select_sms_feedback_template'
            ) || this.state.viewData?.form_data?.select_sms_feedback_template;

        if (!selectedSmsFeedbackTemplate && this.formRef?.current) {
            this.formRef.current.setFieldsValue({
                select_sms_feedback_template:
                    'Dear %cust_name%, for %display_code% please share your feedback at WIFY - %feedback_link%',
            });
        }

        if (
            selectedSmsFeedbackTemplate != undefined &&
            selectedSmsFeedbackTemplate != ''
        ) {
            const selectedStatusTypeTemplateObject =
                feedbackSmsMessageTemplates.filter(
                    (item) => item.value == selectedSmsFeedbackTemplate
                )[0];
            selectedTemplatePreview = selectedStatusTypeTemplateObject?.label;
            let selectedCustomFieldsMeta =
                selectedStatusTypeTemplateObject?.custom_fields_meta;
            let filledCustomVars = {};
            if (
                selectedCustomFieldsMeta != undefined &&
                selectedCustomFieldsMeta != ''
            ) {
                selectedCustomFieldsMeta.map((singleField) => {
                    let copy_field = { ...singleField };
                    copy_field.key = singleField.key;
                    copy_field['onChange'] = () => this.refresh();
                    customFieldsMeta.push(copy_field);
                    filledCustomVars[`%${singleField.key}%`] =
                        this.formRef?.current?.getFieldValue(copy_field.key);
                });
            }
            if (selectedStatusTypeTemplateObject) {
                // parsing template to get preview
                selectedTemplatePreview = parseSmsFeedbackTemplateWithValues(
                    selectedStatusTypeTemplateObject?.value,
                    filledCustomVars
                );
            }
        }

        let sendFeedbackSmsMsg =
            this.formRef?.current?.getFieldValue('srvc_can_cust_rate');

        if (sendFeedbackSmsMsg) {
            return [
                {
                    key: `select_sms_feedback_template`,
                    label: 'Select SMS feedback template',
                    widget: 'select',
                    placeholder: 'Select template',
                    options: feedbackSmsMessageTemplates,
                    onChange: () => {
                        this.refresh();
                    },
                    widgetProps: {
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    required: true,
                },
                ...customFieldsMeta,
                {
                    key: `sms_feedback_template_preview`,
                    render: () => {
                        return (
                            <div className="gx-bg-amber-light gx-p-3">
                                <h4 className="gx-text-center">Preview </h4>
                                <p className="gx-text-red">
                                    {selectedTemplatePreview}
                                </p>
                            </div>
                        );
                    },
                },
            ];
        } else {
            return [];
        }
    }

    getEmailNotificationMeta(status_key) {
        let full_prefix = `cnsmr_ntfctn_fr_status_${status_key}_email`;
        return {
            fields: [
                {
                    key: `${full_prefix}_enabled`,
                    label: 'Enable',
                    widget: 'checkbox',
                },
                {
                    key: `${full_prefix}_cc_users`,
                    label: 'Select CC users',
                    placeholder: 'Select CC users',
                },
                {
                    key: `${full_prefix}_bcc_users`,
                    label: 'Select BCC users',
                    placeholder: 'Select BCC users',
                },
                {
                    key: `${full_prefix}_subject`,
                    label: 'Subject',
                    placeholder: 'Subject',
                },
                {
                    key: `${full_prefix}_message`,
                    label: 'Message',
                    widget: 'textarea',
                    placeholder: 'Message',
                    widgetProps: {
                        rows: 3,
                    },
                },
            ],
        };
    }

    setDefaultRatingsField() {
        let possibleRatingsFields = this.getRatingsFormFieldAsOptions();
        if (possibleRatingsFields.length > 0) {
            let firstRatingsField = possibleRatingsFields[0];
            this.formRef.current.setFieldsValue({
                srvc_feedback_ratings_field_key: firstRatingsField.value,
            });
        }
    }

    getRatingsFormFieldAsOptions() {
        let allStarFields = [];
        var rateFieldsJson = this.formRef?.current?.getFieldValue(
            'srvc_rate_fields_json'
        );
        // console.log("rateFieldsJson",rateFieldsJson);
        if (rateFieldsJson != undefined) {
            try {
                rateFieldsJson = JSON.parse(rateFieldsJson);
                let translatedFields = rateFieldsJson.translatedFields;
                let fieldsWithWidgetRating = translatedFields.filter(
                    (fieldMeta) => fieldMeta.cust_widget == 'Rating'
                );
                allStarFields = fieldsWithWidgetRating.map(
                    (singleRatingFieldMeta) => {
                        return {
                            label: singleRatingFieldMeta.label,
                            value: singleRatingFieldMeta.key,
                        };
                    }
                );
                // console.log("Successfully got statuses from");
            } catch (e) {}
        }
        return allStarFields;
    }

    getStatusesAsOptions() {
        let finalOptions = [];
        let statuses = this.getStatuses();
        if (statuses != undefined) {
            try {
                for (const status_type in statuses) {
                    if (Object.hasOwnProperty.call(statuses, status_type)) {
                        const statusesFrType = statuses[status_type];
                        // console.log("Statuses - ",statusesFrType);
                        statusesFrType.map((singleStatus) => {
                            finalOptions.push({
                                label: singleStatus.title,
                                value: singleStatus.key,
                            });
                        });
                    }
                }
            } catch (error) {
                console.log(
                    'Error while createing dynamic stage wise fields',
                    error
                );
            }
        }
        // console.log('Statuses',this.getStatuses());
        return finalOptions;
    }

    getDynamicStep1Meta() {
        const meta = {
            formItemLayout: null,
            initialValues: this.state.editMode
                ? this.state.viewData?.form_data
                : {},
            fields: [
                {
                    label: 'Name your service',
                    key: 'srvc_type_name',
                    extra: '(max 20 characters)',
                    placeholder: 'Enter service name',
                    rules: [
                        {
                            required: true,
                        },
                        { max: 20 },
                    ],
                    onChange: (event) => {
                        this.formRef.current.setFieldsValue({
                            srvc_type_key: event.target.value
                                .toLowerCase()
                                .replace(/\s+/g, '_'),
                            srvc_type_prefix: this.getServiceTypePrefix(
                                event.target.value
                            ),
                        });
                    },
                },
                {
                    label: 'Service key',
                    key: 'srvc_type_key',
                    extra: '(only a-z & _ allowed max 20 characters)',
                    placeholder: 'Enter service key',
                    rules: [
                        {
                            required: true,
                        },
                        { max: 20 },
                        {
                            validator: (rule, value, callback) => {
                                return new Promise((resolve, reject) => {
                                    if (isValidServiceKey(value)) {
                                        // console.log('Ready to resolve')
                                        resolve();
                                    } else {
                                        reject(
                                            new Error('Invalid text entered')
                                        );
                                    }
                                });
                            },
                        },
                    ],
                },
                {
                    label: <span>Nature of service</span>,
                    key: 'srvc_type_nature',
                    widget: 'radio-group',
                    onChange: () => {
                        this.refresh();
                    },
                    widgetProps: {
                        defaultValue: 'task_based',
                    },
                    options: getOptionsFrNatureOfServiceType(),
                },
                {
                    label: 'Service description',
                    key: 'srvc_type_desc',
                    extra: '(max 200 characters)',
                    placeholder: 'Enter description',
                    widget: 'textarea',
                    rules: [
                        {
                            required: true,
                        },
                        { max: 200 },
                    ],
                },
                {
                    key: 'seperator_2',
                    render: () => {
                        return (
                            <div className="gx-mb-3 gx-bg-grey">
                                <hr></hr>
                            </div>
                        );
                    },
                },
                {
                    label: 'Prefix for request',
                    key: 'srvc_type_prefix',
                    extra: '(min 3 max 4 characters), only future requests will be affected',
                    tooltip: (
                        <span>
                            Eg: If prefix is HMLT and a request is created on
                            1st Feb 2021, then request id will be
                            HMLT210201XXXXXX where XXXXXX will be random digits
                        </span>
                    ),
                    placeholder: 'Enter description',
                    // required: true,
                    rules: [{ max: 4 }, { min: 3 }],
                },
                {
                    key: `srvc_type_prefix_custom_field`,
                    label: 'Select custom field for prefix (Max 10 character will be included)',
                    widget: 'select',
                    options: this.getPossibleSpecificFields(),
                    widgetProps: {
                        // mode:'multiple'
                        allowClear: true,
                        onChange: (value) =>
                            handleClearSelect(
                                value,
                                this.formRef,
                                'srvc_type_prefix_custom_field'
                            ),
                    },
                    tooltip: (
                        <span>
                            Eg: If prefix is HMLT and a request is created on
                            1st Feb 2021, then request id will be
                            HMLT-MYFIELDVALUE... where MYFIELDVALUE will be the
                            value entered in the custom field
                        </span>
                    ),
                },
                {
                    label: 'Include customer name in ID ?',
                    key: 'srvc_type_include_customer_name_in_code',
                    widget: 'checkbox',
                    extra: '(Max 10 character will be included from 1st word), only future requests will be affected',
                    tooltip: (
                        <span>
                            Eg: If prefix is HMLT and customer name is
                            Ravikishan Patil and a request is created on 1st Feb
                            2021, then request id will be
                            HMLT-RAVIKISHAN-210201XXXXXX where XXXXXX will be
                            random digits
                        </span>
                    ),
                },
                {
                    key: 'seperator_3',
                    render: () => {
                        return (
                            <div className="gx-mb-3 gx-bg-grey">
                                <hr></hr>
                            </div>
                        );
                    },
                },
                {
                    key: 'srvc_type_icon_selector',
                    label: 'Icon',
                    widget: IconSelectorWidget,
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                    },
                    rules: [
                        {
                            required: true,
                        },
                    ],
                },
            ],
        };
        // console.log("Getting dynamic form fields",this.state.viewData);
        return meta;
    }
    getServiceTypePrefix(serviceName) {
        if (serviceName) {
            return serviceName.toUpperCase().substring(0, 4);
        }
        // let orgName = ConfigHelpers.getUserDetailsInLocalStorage()?.org?.nickname;
    }

    next = (currentStep) => {
        var fieldsArray = this.getStepsMeta()[currentStep].formMeta.fields;
        var keys = [];
        fieldsArray.map((item) => {
            keys.push(item.key);
        });
        // console.log(keys)//.current.validateFields([],{force: true});
        this.formRef.current
            .validateFields(keys)
            .then((values) => {
                // move ahead only when the fields of this step is validated
                this.setState({ currentStep: this.state.currentStep + 1 });
            })
            .catch((error) => {});
    };

    prev = (currentStep) => {
        this.setState({ currentStep: this.state.currentStep - 1 });
    };

    handleShowFieldsPreviewClick(customFields) {
        this.setState({
            showFormPreview: true,
            formPreviewMeta: customFields,
        });
    }

    onStepChange = (current) => {
        this.setState({ currentStep: current });
    };

    renderInJSONCreatorMode = () => {
        const { editorItem } = this.props;
        const { isFormSubmitting, error, currentStep } = this.state;

        var editorTitle = editorItem?.title;
        var editMode = true;
        if (editorTitle == undefined) {
            editorTitle = 'Add new service type';
            editMode = false;
        } else {
            editorTitle = 'Edit service type - ' + editorTitle;
        }
        var steps = this.getStepsMeta();
        return (
            <Form
                // {...formItemLayout}
                layout="vertical"
                className="gx-w-100"
                ref={this.formRef}
                onFinish={(data) => {
                    if (this.props.onSubmit) {
                        this.props.onSubmit(data);
                    } else {
                        message.warning('Ab kya ?');
                    }
                    // this.submitForm(data);
                }}
                // initialValues={editMode ? viewData : {}}
            >
                {
                    <div className="gx-overflow-auto gx-pb-3 wy-custom-scroll-bar">
                        <Steps
                            current={currentStep}
                            type="navigation"
                            onChange={this.onStepChange}
                            className="site-navigation-steps"
                        >
                            {steps.map((item) => (
                                <Step key={item.title} title={item.title} />
                            ))}
                        </Steps>
                    </div>
                }

                <div className={`steps-content gx-mt-4`}>
                    {/* {currentStep === 0 && <Step1 />} */}

                    {steps.map((item, index) => (
                        <fieldset
                            style={
                                index == currentStep
                                    ? { display: 'block' }
                                    : { display: 'none' }
                            }
                        >
                            <FormBuilder
                                meta={item.formMeta}
                                form={this.formRef}
                            />
                        </fieldset>
                    ))}
                </div>
                <div className="steps-action gx-pt-3">
                    <Button
                        className="gx-bg-teal gx-text-white"
                        htmlType="submit"
                        disabled={isFormSubmitting}
                    >
                        Get JSON
                    </Button>
                    {/* )} */}
                </div>
            </Form>
        );
    };

    getRowDataFrRuleTableFrSbtskStatusMovementBasedOnSrvcStatus() {
        const rulesJson = this.formRef?.current?.getFieldValue(
            'sbtsk_status_movement_based_on_srvc_update_rules'
        );

        const srvcPossiblePrvdrs =
            this.formRef?.current?.getFieldValue('srvc_possible_prvdrs') || [];

        let existingRules;
        if (rulesJson) {
            existingRules = JSON.parse(rulesJson);
        }

        const possibleSrvcPrvdrsSbtskTypes =
            this.state.viewData?.possible_srvc_prvdrs_sbtsk_types;
        const selectedSrvcStatuses =
            this.formRef?.current?.getFieldValue('automation_srvc_statuses') ||
            this.state.viewData.automation_srvc_statuses;
        let rowData = [];

        if (!selectedSrvcStatuses) {
            return;
        }

        if (!possibleSrvcPrvdrsSbtskTypes) {
            return;
        }

        selectedSrvcStatuses.forEach((singleSelectedSrvcStatus) => {
            // for each status and each org add a row

            possibleSrvcPrvdrsSbtskTypes.forEach((singleOrgData) => {
                //make row_data for table
                if (srvcPossiblePrvdrs.includes(singleOrgData?.org_id)) {
                    let row_id =
                        singleOrgData.sbtsk_type_id +
                        '_' +
                        singleSelectedSrvcStatus;
                    let rowDataObj = {
                        row_id: row_id,
                        sbtsk_type: singleOrgData.sbtsk_type_id,
                        srvc_status_key: singleSelectedSrvcStatus,
                    };

                    if (existingRules) {
                        let existingRule = existingRules.filter(
                            (singleRule) => singleRule.row_id == row_id
                        )[0];

                        if (existingRule != undefined) {
                            rowDataObj['sbtsk_type_status'] =
                                existingRule['sbtsk_type_status'];
                        }
                    }
                    rowData.push(rowDataObj);
                }
            });
        });
        // console.log('rowData-', rowData);
        return rowData;
    }

    getColMetaFrRuleTableFrSbtskStatusMovementBasedOnSrvcStatus({
        srvcStatuses,
    }) {
        const possibleSrvcPrvdrsSbtskTypes =
            this.state.viewData?.possible_srvc_prvdrs_sbtsk_types;
        const srvcPossiblePrvdrs =
            this.formRef?.current?.getFieldValue('srvc_possible_prvdrs') || [];
        const selectedAutomationSrvcStatuses =
            this.formRef.current?.getFieldValue('automation_srvc_statuses') ||
            this.state.viewData.automation_srvc_statuses;

        const selectedAutomationSrvcStatusesWithKeyAndValue =
            srvcStatuses.filter((singleSrvcStatus) =>
                selectedAutomationSrvcStatuses?.includes(singleSrvcStatus.value)
            );

        let sbtskTypeIdOptions = [];
        let statusesOptionsBySrvcTypeId = {};
        if (possibleSrvcPrvdrsSbtskTypes) {
            possibleSrvcPrvdrsSbtskTypes.forEach((singleOrgData) => {
                //make sbtsk_type option
                if (srvcPossiblePrvdrs.includes(singleOrgData?.org_id)) {
                    let sbtsk_type_obj = {
                        label:
                            singleOrgData.nickname +
                            ' - ' +
                            singleOrgData.title,
                        value: singleOrgData.sbtsk_type_id,
                    };
                    sbtskTypeIdOptions.push(sbtsk_type_obj);
                    statusesOptionsBySrvcTypeId[singleOrgData.sbtsk_type_id] =
                        singleOrgData.sbtsk_statuses;
                }
            });
        }
        // console.log("statusesOptionsBySrvcTypeId-",statusesOptionsBySrvcTypeId);

        let colMeta = [
            {
                key: 'sbtsk_type',
                label: 'Subtask Type',
                widget: 'select',
                widgetProps: {
                    disabled: true,
                },
                options: sbtskTypeIdOptions,
            },
            {
                key: 'srvc_status_key',
                label: 'Service Type Status ',
                widget: 'select',
                widgetProps: {
                    disabled: true,
                },
                options: selectedAutomationSrvcStatusesWithKeyAndValue,
            },
            {
                key: 'sbtsk_type_status',
                label: 'Subtask Status',
                widget: 'select',
                options: [
                    {
                        label: '--Select--',
                        value: '',
                    },
                ],
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                disable_filter: true,
                dynamicMeta: (row, original_field_meta) => {
                    let dynamicMetaRow = { ...original_field_meta };

                    let optionsFrCurrSrvcTypeId =
                        statusesOptionsBySrvcTypeId[row.sbtsk_type];
                    if (optionsFrCurrSrvcTypeId) {
                        dynamicMetaRow['options'] = [
                            ...dynamicMetaRow['options'],
                            ...optionsFrCurrSrvcTypeId,
                        ];
                    }
                    return dynamicMetaRow;
                },
            },
        ];
        // console.log("colMeta-",colMeta);
        return colMeta;
    }

    getErrorMessage(error) {
        try {
            const parsedError = JSON.parse(error);
            return `${parsedError?.message} (${parsedError?.subCode})` || error;
        } catch (e) {
            return error; // Return original error if parsing fails
        }
    }

    render() {
        const { editorItem, asJSONCreator } = this.props;
        const {
            isFormSubmitting,
            visible,
            isLoadingViewData,
            error,
            viewData,
            currentStep,
            showFormPreview,
            formPreviewMeta,
        } = this.state;
        var editorTitle = editorItem?.title;
        var editMode = true;
        if (editorTitle == undefined) {
            editorTitle = 'Add new service type';
            editMode = false;
        } else {
            editorTitle = 'Edit service type - ' + editorTitle;
        }
        var steps = this.getStepsMeta();
        const errorMessage = this.getErrorMessage(error);

        return asJSONCreator ? (
            <>{this.renderInJSONCreatorMode()}</>
        ) : visible ? (
            <Modal
                title={`${editorTitle}`}
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={1300}
                onCancel={this.handleCancel}
                footer={null}
            >
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <Form
                        // {...formItemLayout}
                        layout="vertical"
                        className="gx-w-100"
                        ref={this.formRef}
                        onFinish={(data) => {
                            this.submitForm(data);
                        }}
                        // initialValues={editMode ? viewData : {}}
                    >
                        {editMode && (
                            <div className="gx-overflow-auto gx-pb-3 wy-custom-scroll-bar">
                                <Steps
                                    current={currentStep}
                                    type="navigation"
                                    onChange={this.onStepChange}
                                    className="site-navigation-steps"
                                >
                                    {steps.map((item) => (
                                        <Step
                                            key={item.title}
                                            title={item.title}
                                        />
                                    ))}
                                </Steps>
                            </div>
                        )}

                        <div
                            className={`steps-content ${editMode ? 'gx-mt-4' : null}`}
                        >
                            {/* {currentStep === 0 && <Step1 />} */}

                            {steps.map((item, index) => (
                                <fieldset
                                    style={
                                        index == currentStep
                                            ? { display: 'block' }
                                            : { display: 'none' }
                                    }
                                >
                                    <FormBuilder
                                        meta={item.formMeta}
                                        form={this.formRef}
                                    />
                                </fieldset>
                            ))}
                        </div>
                        <div className="steps-action gx-pt-3">
                            {editMode && currentStep > 0 && (
                                <Button onClick={() => this.prev(currentStep)}>
                                    Previous
                                </Button>
                            )}
                            {editMode && currentStep < steps.length - 1 && (
                                <Button
                                    data-testid="save-next"
                                    type="primary"
                                    onClick={() => this.next(currentStep)}
                                >
                                    Save & Next
                                </Button>
                            )}
                            {/*  {( currentStep === steps.length - 1 || !editMode ) && ( */}
                            <Button
                                className="gx-bg-teal gx-text-white"
                                htmlType="submit"
                                disabled={isFormSubmitting}
                            >
                                {editMode ? 'Finish' : 'Submit'}
                            </Button>
                            {/* )} */}
                        </div>
                        {/* <FormBuilder 
                            meta={this.getDynamicStep1Meta()} 
                            
                            form={this.formRef}  /> */}

                        {isFormSubmitting ? (
                            <div className="gx-loader-view gx-loader-position">
                                <CircularProgress />
                            </div>
                        ) : null}
                        {error ? (
                            <p className="gx-text-red">{errorMessage}</p>
                        ) : null}
                    </Form>
                )}
                {showFormPreview && (
                    <FormPreviewMeta
                        formPreviewMeta={formPreviewMeta}
                        onCancel={() => {
                            this.setState({ showFormPreview: false });
                        }}
                    />
                )}
            </Modal>
        ) : (
            <></>
        );
    }
}

export default SrvcTypeEditor;
