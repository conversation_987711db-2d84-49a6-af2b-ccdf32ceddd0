CREATE OR REPLACE FUNCTION public.tms_create_user(form_data_ json, entry_id uuid)
 RETURNS json
 LANGUAGE plpgsql
AS $function$

-- Declarations
declare 
	status boolean;
	message text;
	affected_rows integer;
	last_ins_usr_id uuid;
	resp_data json;
	single_role_id integer;
	--form data 
	user_org_id_ integer;
	user_name_ text;
	user_designation_ text;
	user_reporting_to_ uuid;
	user_mobile_ text;
	user_email_ text;
	ip_address_ text;
	user_agent_ text;
	validation_resp integer[];
	validation_resp_for_ins integer[];
	_is_annonymous bool;
	loc_group_ int[];
	password_ text;
	assgnd_org_ids_ int[];
	assgnd_srvc_type_ids_ int[];
	user_id_ uuid;
	user_code_ text;	
	is_active_ bool default false;
	is_active_status text;
	del_future_sbtsk_resp json;
	is_bulk_update bool default false;
	primary_role_id_ int;
	temp_actual_usr_org_id int;
	temp_user_parent_verification bool;
	user_role_ json;
	user_role_array integer[];
	roles_validation_resp json;
    usr_password_policy_resp json;
	existing_usr_form_data json := '{}';
	

begin
	
	-- check if email and mobile exists in same org_id from usr_identities table
	-- Yes --> return false (status = false, message = 'usr_identity_already_exists')
	
	-- check user_email is not empty 
	-- Yes --> set user_ind_type EMAIL
	
	-- check user_mobile is not empty 
	-- Yes --> set user_ind_type MOBILE_NUM
	
	-- insert into user table
	
	-- insert into usr identity table
	
	-- insert into usr roles table

	
	status = false;
	message = 'Internal_error';
	
	--form data 
    user_id_            = json_extract_path_text(form_data_,'usr_id');
	user_org_id_ 	    = json_extract_path_text(form_data_,'org_id');
	user_name_          = json_extract_path_text(form_data_,'user_name');
	user_designation_ 	= json_extract_path_text(form_data_,'user_designation'); 
	user_reporting_to_ 	= json_extract_path_text(form_data_,'user_reporting_to');
	user_mobile_ 		= json_extract_path_text(form_data_,'user_mobile');
	user_email_  	    = json_extract_path_text(form_data_,'user_email');
	ip_address_ 		= json_extract_path_text(form_data_,'ip_address');
	user_agent_ 		= json_extract_path_text(form_data_,'user_agent');
    password_  	        = form_data_->>'password';  
	_is_annonymous      = form_data_->'is_annonymous';
	user_code_          = form_data_->>'user_code';
	is_active_status    = form_data_->>'is_active';
	is_bulk_update = form_data_->'is_bulk_update';
	--Get user role in array
	user_role_ = json_extract_path(form_data_,'user_role');
	user_role_array	= array( select json_array_elements_text(user_role_))::integer[];
	primary_role_id_ = form_data_->'user_primary_role';
	
	-- check user_id_'s organisation is same as user_org_id_
	select org_id 
	  from cl_tx_users 
	 where usr_id  = user_id_
	  into temp_actual_usr_org_id; 
	 
	if temp_actual_usr_org_id <> user_org_id_ then
		-- if not then check if atleast the user_id_'s organisation is a parent org to user_org_id_
 		temp_user_parent_verification = tms_verify_parent_child_relation_for_orgs(temp_actual_usr_org_id,user_org_id_)->>'status';
 		if temp_user_parent_verification is false then 
 			status = false;
			message = 'failed';
 			return json_build_object('status',status,'code',message,'data','{}');
 		end if;
	end if;

    loc_group_ = array( select json_array_elements_text(json_extract_path(form_data_,'user_loc_group')) )::int[];
    assgnd_org_ids_ = array( select json_array_elements_text(json_extract_path(form_data_,'assgnd_org_ids')) )::int[];
	assgnd_srvc_type_ids_ = array( select json_array_elements_text(json_extract_path(form_data_,'srvc_types_assigned_to_user')) )::int[];
   
	if _is_annonymous is null then
		_is_annonymous = false;
	end if;

--	is_active_status = 'Yes' condition added for Bulk Update 
--	because when we download an Export csv file for users the Active column has value "Yes" this same Xlsx file is uploaded for bulk update 
	if is_active_status = 'true' or UPPER(is_active_status) = 'ACTIVE' or is_active_status = 'Yes' then 
		is_active_ = true;
	end if;

--	raise notice 'entry_id %', entry_id;

	--check if email or mobile no exists from usr_identities table by same org_id
	validation_resp := array ( 
		  select usr_identities.id 
		    from cl_tx_users as usr
		   inner join cl_tx_usr_identities as usr_identities
		      on usr_identities.user_id = usr.usr_id 
		   where usr_identities.org_id = user_org_id_
		     and usr_identities.user_id <> entry_id
		     and (
		    	  	trim(lower(usr_identities.ind_id)) = trim(lower(user_email_))
		    		or 
		    		usr_identities.ind_id = user_mobile_
		    		or 
		    		(
		    			user_code_ <> ''
		    			and
		    			usr.user_code = user_code_
		    		)
		    		
		   	 	 )    
	);
	--check if user role not exists for same org_id
	roles_validation_resp = array_to_json(array(
		SELECT roles.role_id 
	   	  FROM cl_cf_roles as roles
	  	 WHERE roles.org_id <> user_org_id_
	  	   and roles.role_id = any(user_role_array)
	));
	if array_length(validation_resp,1) > 0 then 
	
		status = false;
		message = 'usr_identity_already_exists';
	
	elsif (json_array_length(roles_validation_resp) > 0 or user_role_array = '{}') 
	  and user_name_ <> 'API access' and user_name_ <> 'System' and is_bulk_update is not true then 
	
		status = false;
		message = 'usr_roles_not_exists';
	
	elsif entry_id is null then
		
		if primary_role_id_ is null then
			primary_role_id_ = (user_role_->>0)::int;
			if primary_role_id_ is not null then
				form_data_ = jsonb_set(form_data_::jsonb, '{user_primary_role}', (form_data_->'user_role'->>0)::jsonb, true);
			end if;
		end if;
		
		--check if email or mobile no exists from usr_identities table by same org_id
		validation_resp_for_ins := array ( 
			 select usr_identities.id 
			   from cl_tx_users as usr
		      inner join cl_tx_usr_identities as usr_identities
		         on usr_identities.user_id = usr.usr_id 
			  where usr_identities.org_id = user_org_id_
			  	and (
				    	trim(lower(usr_identities.ind_id)) = trim(lower(user_email_))
				    	or 
				    	usr_identities.ind_id = user_mobile_
				    	or 
		    			usr.user_code = user_code_
		    			or
		    			usr."name" = user_name_
			        )    
		);
	    if array_length(validation_resp_for_ins,1) > 0 then 
		
			status = false;
			message = 'usr_identity_already_exists';
	
		else
		    insert into public.cl_tx_users (
					"name" , "org_id" , "designation" , "reporting_to", "is_active","c_meta","u_meta","is_annonymous",
					"loc_group","assgnd_org_ids","assgnd_srvc_type_ids","user_code","form_data","c_by", primary_role, 
					"primary_vertical","primary_srvc_hub","primary_skill1","primary_skill2","primary_skill3",
					"secondary_vertical","secondary_srvc_hub","secondary_skill1","secondary_skill2","secondary_skill3",
					"third_vertical","third_srvc_hub","third_skill1","third_skill2","third_skill3"
					)
					values (
						user_name_, 
						user_org_id_,
						user_designation_, 
						cast(user_reporting_to_ as uuid),
						true,
						row(ip_address_,user_agent_,now() at time zone 'utc'),
						row(ip_address_,user_agent_,now() at time zone 'utc'),
						_is_annonymous,
						loc_group_::int[],
						assgnd_org_ids_::int[],
						assgnd_srvc_type_ids_::int[],
						user_code_,
						form_data_,
						user_id_,
						primary_role_id_,
						tms_hlpr_get_int_null_or_clear(form_data_, 'primary_vertical', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'primary_srvc_hub', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'primary_skill1', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'primary_skill2', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'primary_skill3', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_vertical', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_srvc_hub', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_skill1', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_skill2', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_skill3', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'third_vertical', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'third_srvc_hub', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'third_skill1', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'third_skill2', NULL),
						tms_hlpr_get_int_null_or_clear(form_data_, 'third_skill3', NULL)
					)
			RETURNING usr_id into last_ins_usr_id;		
		
			GET DIAGNOSTICS affected_rows = ROW_COUNT;
	
			if affected_rows = 1 then
			
				--insert user identity table for email
				affected_rows = tms_create_user_identity(last_ins_usr_id, 'EMAIL', user_email_, password_, form_data_);
			
				--insert user identity table for mobile
				affected_rows = tms_create_user_identity(last_ins_usr_id, 'MOBILE_NUM', user_mobile_, '',  form_data_);
			
			
				affected_rows = tms_create_user_roles(form_data_,last_ins_usr_id);
			
				--save user history
				perform tms_add_to_user_history(last_ins_usr_id, 'CREATE', form_data_);
				
				status = true;
				message = 'success';
				resp_data =  json_build_object('entry_id',last_ins_usr_id);
					
			end if;
		end if;
	
	else
--		raise notice 'entry_id %', entry_id;

		-- Retrieve existing user details for history update
		existing_usr_form_data = tms_get_user_details(entry_id)->'data'->'form_data';

		update public.cl_tx_users
			   set "org_id" 	  =  user_org_id_, 
			       "name" 		  =  case 
			   		when user_name_ is null and is_bulk_update is true then "name" 
			   		else user_name_ -- keep as is
			   		end,   
			       "designation"  =  case 
			   		when user_designation_ is null and is_bulk_update is true then designation 
			   		else user_designation_ -- keep as is
			   		end,  
			       "reporting_to" = case 
			   		when user_reporting_to_ is null and is_bulk_update is true then reporting_to 
			   		else cast(user_reporting_to_ as uuid) -- keep as is
			   		end, 
				   "u_meta" =   row(ip_address_,user_agent_,now() at time zone 'utc') ,
				   "loc_group" = case 
			   		when cardinality(loc_group_) = 0 and is_bulk_update is true then loc_group 
			   		else loc_group_ -- keep as is
			   		end,
				   "assgnd_org_ids" = case 
			   		when cardinality(assgnd_org_ids_) = 0 and is_bulk_update is true then assgnd_org_ids 
			   		else assgnd_org_ids_ -- keep as is
			   		end,
				   "assgnd_srvc_type_ids" = case 
			   		when cardinality(assgnd_srvc_type_ids_) = 0 and is_bulk_update is true then assgnd_srvc_type_ids 
			   		else assgnd_srvc_type_ids_ -- keep as is
			   		end,
				   "user_code" =  case 
			   		when user_code_ is null and is_bulk_update is true then user_code 
			   		else user_code_ -- keep as is
			   		end, 
					"form_data" = case
                    when is_bulk_update is true then ((form_data)::jsonb || (form_data_)::jsonb)::json
                    else form_data_ -- keep as is
                    end, 
				   "is_active" =  case 
			   		when is_active_status is null and is_bulk_update is true then is_active 
			   		else is_active_ -- keep as is
			   		end, 
				   "activation_u_by" =  case 
			   		when user_id_ is null and is_bulk_update is true then activation_u_by 
			   		else user_id_ -- keep as is
			   		end,
			   		primary_role =  case 
			   		when primary_role_id_ is null then primary_role -- keep as is
			   		else primary_role_id_ 
			   		end,
					"primary_vertical" = tms_hlpr_get_int_null_or_clear(form_data_, 'primary_vertical', primary_vertical),
					"primary_srvc_hub" = tms_hlpr_get_int_null_or_clear(form_data_, 'primary_srvc_hub', primary_srvc_hub),
					"primary_skill1" = tms_hlpr_get_int_null_or_clear(form_data_, 'primary_skill1', primary_skill1),
					"primary_skill2" = tms_hlpr_get_int_null_or_clear(form_data_, 'primary_skill2', primary_skill2),
					"primary_skill3" = tms_hlpr_get_int_null_or_clear(form_data_, 'primary_skill3', primary_skill3),
					"secondary_vertical" = tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_vertical', secondary_vertical),
					"secondary_srvc_hub" = tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_srvc_hub', secondary_srvc_hub),
					"secondary_skill1" = tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_skill1', secondary_skill1),
					"secondary_skill2" = tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_skill2', secondary_skill2),
					"secondary_skill3" = tms_hlpr_get_int_null_or_clear(form_data_, 'secondary_skill3', secondary_skill3),
					"third_vertical" = tms_hlpr_get_int_null_or_clear(form_data_, 'third_vertical', third_vertical),
					"third_srvc_hub" = tms_hlpr_get_int_null_or_clear(form_data_, 'third_srvc_hub', third_srvc_hub),
					"third_skill1" = tms_hlpr_get_int_null_or_clear(form_data_, 'third_skill1', third_skill1),
					"third_skill2" = tms_hlpr_get_int_null_or_clear(form_data_, 'third_skill2', third_skill2),
					"third_skill3" = tms_hlpr_get_int_null_or_clear(form_data_, 'third_skill3', third_skill3)
			 where usr_id = entry_id ;
			
		GET DIAGNOSTICS affected_rows = ROW_COUNT;
		if affected_rows = 1 then
		
					
			if user_email_ is not null then
				--update user identity table for email 
				affected_rows = tms_create_user_identity(entry_id, 'EMAIL', user_email_, password_, form_data_);
			end if;
			
			if user_mobile_ is not null then
				--insert user identity table for mobile
				affected_rows = tms_create_user_identity(entry_id, 'MOBILE_NUM', user_mobile_, '',  form_data_);
			end if;
			
			if json_array_length(json_extract_path(form_data_,'user_role')) > 0 then
				affected_rows = tms_create_user_roles(form_data_,entry_id);
			end if;
		
			if is_active_ is not true then
				del_future_sbtsk_resp = tms_del_future_sbtsk_fr_deactivated_user(form_data_,entry_id);	
			end if;

			-- Update the user history with existing form data
 			form_data_ = jsonb_set(form_data_::jsonb,'{existing_details}',existing_usr_form_data::jsonb,true);
			perform tms_add_to_user_history(entry_id, 'UPDATE', form_data_);

			status = true;
			message = 'success';
			resp_data =  json_build_object('entry_id',entry_id);
		else
--		while Bulk update if a userId is invalid(does not exist in table) 
			status = false;
			message = 'UserID does not exist';
			resp_data =  json_build_object('entry_id',entry_id);
		end if;	
	
	end if;
	
	return json_build_object('status',status,'code',message,'data',resp_data);

END;
$function$
;
