name: Create HUL DB
on:
    workflow_dispatch: # Putting here is also fine!!
    release:
        types: [created]

env:
    PGPASSWORD: ${{ secrets.dev_db_cred }}

jobs:
    DB-for-HUL:
        runs-on: ubuntu-latest

        steps:
            - name: Start PostgreSQL on Ubuntu and Drop the OLD DBs
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'DROP DATABASE IF EXISTS 'tmsdevhul' WITH ( FORCE ) ;'
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'DROP DATABASE IF EXISTS "tmsdev-registry" WITH ( FORCE ) ;'

            - name: Postgres Dump Backup for ORIGIN Dev DB
              uses: tj-actions/pg-dump@v2.3
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/wify_tms'
                  path: 'backups/backup.sql'
                  options: '-O'

            - name: Start PostgreSQL on Ubuntu and Create blank Dev DBs temp
              run: |
                  echo Installing psql
                  sudo apt-get install -y postgresql-client
                  echo connecting with rds db
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'create database 'tmsdevhul' ;'
                  psql -h ${{ secrets.dev_db_endp }} -d wify_tms -U tms-dev1 -c 'create database "tmsdev-registry" ;'

            - name: Postgres Backup Restore from Origin Dev to new DB Dev temp (tmsdevhul)
              uses: tj-actions/pg-restore@v4.5
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsdevhul'
                  backup_file: 'backups/backup.sql'

            - name: Postgres Backup Restore from Origin Dev to new DB Dev temp (tmsdev-registry)
              uses: tj-actions/pg-restore@v4.5
              with:
                  database_url: 'postgres://tms-dev1:${{ secrets.dev_db_cred }}@${{ secrets.dev_db_endp }}:${{ secrets.dev_db_port}}/tmsdev-registry'
                  backup_file: 'backups/backup.sql'
