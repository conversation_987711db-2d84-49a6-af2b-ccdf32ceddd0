-- Drop the old version function
DROP FUNCTION IF EXISTS public.tms_hlpr_int_or_null(text);

CREATE OR REPLACE FUNCTION public.tms_hlpr_get_int_null_or_clear(form_data_ json, key_ text, existing_value_ bigint DEFAULT NULL)
 RETURNS bigint
 LANGUAGE plpgsql
AS $function$
declare
    field_value_ text;
begin
    -- Check if key exists in JSON
    if NOT ((form_data_::jsonb) ? key_) then
        return existing_value_;
    end if;

    -- Extract the value
    field_value_ := form_data_->>key_;

    -- Handle empty string or null values
    if field_value_ = '' OR field_value_ IS NULL then
        return NULL;
    end if;

    return field_value_::bigint;
END;
$function$;

