import React, { useState, useRef, useEffect } from 'react';
import { GoThumbsup, GoThumbsdown, GoComment, GoSync } from 'react-icons/go';
import { Alert, Button, Input, message as notification, Tooltip } from 'antd';
import { MdOutlineClose } from 'react-icons/md';
import RenderAgentResponseBlock from './ReactMarkDown';
import http_utils from '../../../util/http_utils';

// Feedback options configuration
const FEEDBACK_OPTIONS = [
    {
        key: 'confused',
        label: "Didn't fully follow instructions",
    },
    {
        key: 'irrelevant',
        label: 'Inaccurate result',
    },
    {
        key: 'not_relevant_2',
        label: 'This does not make sense',
    },
];

// FeedbackSection component moved outside to prevent re-creation on every render
const FeedbackSection = ({
    idx,
    msg,
    toggleFeedbackSection,
    handleFeedbackClick,
    handleCustomFeedbackChange,
    handleCustomFeedbackSubmit,
    messages,
}) => {
    const wrapperRef = useRef(null);

    useEffect(() => {
        function handleClickOutside(event) {
            if (
                wrapperRef.current &&
                !wrapperRef.current.contains(event.target)
            ) {
                toggleFeedbackSection(idx); // Close feedback section
            }
        }

        document.addEventListener('click', handleClickOutside);
        return () => {
            document.removeEventListener('click', handleClickOutside);
        };
    }, [idx, toggleFeedbackSection]);

    // Scroll feedback section into view when it opens
    useEffect(() => {
        if (msg.showFeedbackOptions && wrapperRef.current) {
            // Small delay to ensure the feedback section is fully rendered
            setTimeout(() => {
                const isLastMessage = idx === messages.length - 1;

                if (isLastMessage) {
                    // If this is the last message, scroll to bottom of container
                    const chatContainer =
                        wrapperRef.current.closest('.wy-acb-messages');
                    if (chatContainer) {
                        chatContainer.scrollTo({
                            top: chatContainer.scrollHeight,
                            behavior: 'smooth',
                        });
                    }
                } else {
                    // For other messages, scroll feedback section into view
                    wrapperRef.current.scrollIntoView({
                        behavior: 'smooth',
                        block: 'nearest',
                    });
                }
            }, 100);
        }
    }, [msg.showFeedbackOptions, idx, messages.length]);

    return (
        msg.showFeedbackOptions && (
            <div ref={wrapperRef} className="wy-acb-feedback-section-wrapper">
                <div className="wy-acb-feedback-section-header gx-mb-3 gx-position-relative">
                    <div className="wy-acb-title-decor"></div>
                    <div>Tell us more...</div>
                    <MdOutlineClose
                        className="gx-text-red wy-cursor-pointer gx-fs-xl"
                        onClick={() => toggleFeedbackSection(idx)}
                    />
                </div>
                <div>
                    <div>
                        <div className="wy-acb-feedback-section">
                            {FEEDBACK_OPTIONS.map((option) => (
                                <Button
                                    key={option.key}
                                    block
                                    type={
                                        msg.feedbackType?.includes(option.key)
                                            ? 'primary'
                                            : 'ghost'
                                    }
                                    onClick={() =>
                                        handleFeedbackClick(idx, option.key)
                                    }
                                    className="gx-mr-0"
                                >
                                    {option.label}
                                </Button>
                            ))}
                        </div>
                        <div className="gx-mb-2">
                            <Input.TextArea
                                value={msg.customFeedback || ''}
                                placeholder="Please describe what could be improved... (optional)"
                                rows={2}
                                className="wy-acb-textarea"
                                onChange={(e) =>
                                    handleCustomFeedbackChange(
                                        idx,
                                        e.target.value
                                    )
                                }
                            />
                        </div>
                        <div className="gx-text-right gx-mt-1">
                            <Button
                                danger
                                type="text"
                                onClick={() => toggleFeedbackSection(idx)}
                                className="gx-mr-2 gx-mt-1 gx-mb-0"
                            >
                                Cancel
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => handleCustomFeedbackSubmit(idx)}
                                className="gx-mt-1 gx-mb-0"
                            >
                                Submit
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        )
    );
};

const AgentMessage = ({
    message,
    isGenerating,
    idx,
    helpfulMap,
    setHelpfulMap,
    messages,
    setMessages,
    sessionId,
}) => {
    // API call to save rating to database
    const submitRatingToDatabase = async (ratingData) => {
        try {
            // console.log('Submitting rating to database:', ratingData);

            const onComplete = (resp) => {
                console.log('Rating submitted successfully:', resp);
                // notification.success('Thank you for your feedback!');
            };

            const onError = (error) => {
                console.error('Error submitting rating:', error);
                notification.error(
                    'Failed to submit feedback. Please try again.'
                );
            };

            http_utils.performPostCall(
                '/ai-chatbot/va-chatbot-resp-rating',
                ratingData,
                onComplete,
                onError
            );
        } catch (error) {
            console.error('Error in submitRatingToDatabase:', error);
            notification.error('Failed to submit feedback. Please try again.');
        }
    };

    // Get user message that corresponds to this AI response
    const getUserMessageForResponse = (currentIdx) => {
        // Look for the previous user message
        for (let i = currentIdx - 1; i >= 0; i--) {
            if (messages[i]?.from === 'user') {
                return messages[i];
            }
        }
        return null;
    };

    // Handle thumbs up helpful click
    const handleThumbsUpHelpfulClick = (idx) => {
        // Check if already rated
        if (messages[idx]?.ratingSubmitted) {
            return;
        }

        setHelpfulMap((prev) => ({
            ...prev,
            [idx]: !prev[idx],
        }));

        setMessages((prev) =>
            prev.map((msg, i) =>
                i === idx
                    ? {
                          ...msg,
                          showFeedbackOptions: false,
                          ratingSubmitted: true,
                          ratingType: 'positive',
                      }
                    : msg
            )
        );

        // Submit positive rating to database
        const userMessage = getUserMessageForResponse(idx);
        const currentMessage = messages[idx];

        const ratingData = {
            session_id: sessionId,
            usr_prompt: userMessage?.text || '',
            va_response: currentMessage,
            good_rating: true,
            bad_rating: false,
            additional_comments: '',
            sugg_feedback: '',
        };

        submitRatingToDatabase(ratingData);
    };

    // Toggle feedback section
    const toggleFeedbackSection = (idx) => {
        // Check if already rated
        if (messages[idx]?.ratingSubmitted) {
            return;
        }

        setHelpfulMap((prev) => ({
            ...prev,
            [idx]: false,
        }));

        setMessages((prev) =>
            prev.map((msg, i) =>
                i === idx
                    ? { ...msg, showFeedbackOptions: !msg.showFeedbackOptions }
                    : msg
            )
        );
    };

    // Handle feedback type selection
    const handleFeedbackClick = (idx, selectedType) => {
        setMessages((prevMessages) =>
            prevMessages.map((msg, i) => {
                if (i !== idx) return msg;

                const currentTypes = Array.isArray(msg.feedbackType)
                    ? msg.feedbackType
                    : [];
                const alreadySelected = currentTypes.includes(selectedType);

                const updatedTypes = alreadySelected
                    ? currentTypes.filter((type) => type !== selectedType) // deselect
                    : [...currentTypes, selectedType]; // select

                return {
                    ...msg,
                    feedbackType: updatedTypes,
                    showFeedbackOptions: true,
                    customFeedback: msg.customFeedback || '',
                };
            })
        );
    };

    // Handle custom feedback text change
    const handleCustomFeedbackChange = (index, value) => {
        setMessages((prevMessages) =>
            prevMessages.map((msg, i) =>
                i === index ? { ...msg, customFeedback: value } : msg
            )
        );
    };

    // Handle feedback submission
    const handleCustomFeedbackSubmit = (index) => {
        const currentMessage = messages[index];
        const userMessage = getUserMessageForResponse(index);

        // Prepare detailed feedback data
        const feedbackTypes = currentMessage?.feedbackType || [];
        const customFeedback = currentMessage?.customFeedback || '';

        // Map feedback types to readable format using the same array
        const feedbackMapping = FEEDBACK_OPTIONS.reduce((acc, option) => {
            acc[option.key] = option.label;
            return acc;
        }, {});

        const suggFeedback = feedbackTypes
            .map((type) => feedbackMapping[type] || type)
            .join(', ');

        const ratingData = {
            session_id: sessionId,
            usr_prompt: userMessage?.text || '',
            va_response: {
                text: currentMessage?.text || '',
                block: currentMessage?.block || null,
                time: currentMessage?.time || '',
            },
            good_rating: false,
            bad_rating: true,
            additional_comments: customFeedback,
            sugg_feedback: suggFeedback,
        };

        // Submit to database
        submitRatingToDatabase(ratingData);

        setHelpfulMap((prev) => ({
            ...prev,
            [index]: false,
        }));

        setMessages((prev) =>
            prev.map((msg, i) =>
                i === index
                    ? {
                          ...msg,
                          showFeedbackOptions: false,
                          feedbackType: null,
                          customFeedback: '',
                          feedbackSubmitted: true,
                          ratingSubmitted: true,
                          ratingType: 'negative',
                      }
                    : msg
            )
        );
    };

    return (
        <div className="wy-acb-message-wrapper ai">
            <div className="message-wrapper">
                <div className="wy-acb-message-bubble">
                    {isGenerating ? (
                        <div className="wy-acb-initial-loading"></div> // Loading animation
                    ) : (
                        <div style={{ maxHeight: '350px', overflowY: 'auto' }}>
                            <RenderAgentResponseBlock
                                text={message.text}
                                block={message?.block}
                            />
                        </div>
                    )}
                </div>

                 {!message.isWaiting && !isGenerating && (
                    <div className="gx-d-flex gx-align-items-center gx-justify-content-between gx-mt-1">
                        <div className="wy-acb-feedback-bubble-wrapper">
                            <div className="wy-acb-feedback-bubble">
                                <button
                                    className={`wy-acb-feedback-btn helpful ${
                                        helpfulMap[idx] ||
                                        message.ratingType === 'positive'
                                            ? 'wy-acb-feedback-helpful-btn-active'
                                            : ''
                                    } ${message.showFeedbackOptions || message.feedbackSubmitted ? 'gx-d-none' : ''} ${
                                        message.ratingSubmitted
                                            ? 'disabled'
                                            : ''
                                    }`}
                                    title={
                                        message.ratingSubmitted
                                            ? 'Already rated'
                                            : ''
                                    }
                                    disabled={message.ratingSubmitted}
                                    onClick={() =>
                                        handleThumbsUpHelpfulClick(idx)
                                    }
                                >
                                    <Tooltip placement="bottom" title="Helpful">
                                        <GoThumbsup
                                            stroke={
                                                helpfulMap[idx] ||
                                                message.ratingType ===
                                                    'positive'
                                                    ? 'green'
                                                    : undefined
                                            }
                                            strokeWidth={
                                                helpfulMap[idx] ||
                                                message.ratingType ===
                                                    'positive'
                                                    ? 1
                                                    : 0
                                            }
                                        />
                                    </Tooltip>
                                </button>

                                <button
                                    className={`wy-acb-feedback-btn not-helpful
                                                                            ${message.feedbackSubmitted || message.showFeedbackOptions || message.ratingType === 'negative' ? 'wy-acb-feedback-not-helpful-btn-active' : ''}
                                                                            ${helpfulMap[idx] ? 'gx-d-none' : ''} ${
                                                                                message.ratingSubmitted
                                                                                    ? 'disabled'
                                                                                    : ''
                                                                            }`}
                                    title={
                                        message.ratingSubmitted
                                            ? 'Already rated'
                                            : ''
                                    }
                                    disabled={message.ratingSubmitted}
                                    onClick={() => toggleFeedbackSection(idx)}
                                >
                                    <Tooltip
                                        placement="bottom"
                                        title="Not Helpful"
                                    >
                                        <GoThumbsdown
                                            color={
                                                message.showFeedbackOptions ||
                                                message.ratingType ===
                                                    'negative'
                                                    ? 'black'
                                                    : undefined
                                            }
                                        />
                                    </Tooltip>
                                </button>
                            </div>
                            {message.feedbackSubmitted && (
                                <Alert
                                    showIcon
                                    message="Thank you for your feedback!"
                                    type="success"
                                    className="gx-mt-2 wy-acb-full-width-chat-area gx-fs-sm"
                                />
                            )}
                        </div>
                        <div className="wy-acb-timestamp gx-d-none">
                            {message.time}
                        </div>
                    </div>
                )}

                {/* Feedback Selection Section */}
                {message.showFeedbackOptions && (
                    <FeedbackSection
                        idx={idx}
                        msg={message}
                        toggleFeedbackSection={toggleFeedbackSection}
                        handleFeedbackClick={handleFeedbackClick}
                        handleCustomFeedbackChange={handleCustomFeedbackChange}
                        handleCustomFeedbackSubmit={handleCustomFeedbackSubmit}
                        messages={messages}
                    />
                )}

                {/* Show feedback buttons when AI has completed response */}
                {/* {!isGenerating && (
                    <div className="wy-acb-feedback-bubble">
                        <button
                            className="wy-acb-feedback-btn helpful"
                            title="Helpful"
                        >
                            <GoThumbsup />
                        </button>
                        <button
                            className="wy-acb-feedback-btn not-helpful"
                            title="Not Helpful"
                        >
                            <GoThumbsdown />
                        </button>
                        <button
                            className="wy-acb-feedback-btn comment"
                            title="Comments"
                        >
                            <GoComment />
                        </button>
                        <button
                            className="wy-acb-feedback-btn regenerate"
                            title="Regenerate"
                        >
                            <GoSync />
                        </button>
                    </div>
                )} */}
            </div>
        </div>
    );
};

export default AgentMessage;
