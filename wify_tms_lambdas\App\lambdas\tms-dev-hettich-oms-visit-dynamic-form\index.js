const articleProductOptions = require("./ArticleVsProductMap");

const materialDensityMap = {
  Aluminium: 2.7,
  "Blasa Wood": 0.12,
  Glass: 2.6,
  "Hard fiber": 1,
  "Hardwood (Beech)": 0.8,
  "Plastic (PE)": 1.5,
  MDF: 0.9,
  Corian: 1.6,
  Chipboard: 0.7,
  Plywood: 0.45,
  Coreboard: 0.45,
  "Softwood (Spruce)": 0.5,
};

const materialOptionsMap = {
  "c8540fb6-4f55-4c31-b34b-309f584c81a3": "Aluminium",
  "cabe348e-f0fa-46e7-bf07-cb94acc3b62a": "Blasa Wood",
  "9c6d1add-abd8-45d3-95dc-f6b0d8f7d269": "Glass",
  "c0effa9e-151d-4b6a-86dc-52753faad50e": "Hard fiber",
  "cc464c0d-499d-4797-b080-f37acc411a36": "Hardwood (Beech)",
  "1835b403-895d-41a9-bb62-994e834e659f": "Plastic (PE)",
  "e2073181-3266-4c9e-8bd3-ecb3f5b7706c": "MDF",
  "51afc336-33ff-46ca-a8d2-8c5223b3932a": "Corian",
  "0cc239da-2d41-49b8-8066-f898360d1605": "Chipboard",
  "fcdf7883-4255-4a88-bf16-abb82a5ffdb2": "Plywood",
  "fc4e3746-b7a6-47cb-a23a-147a46eded92": "Coreboard",
  "328a5cc2-51ef-40e4-a38b-38ad09596888": "Softwood (Spruce)",
};

const modifyFieldInMeta = (meta, fieldKey, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (singleField.key == fieldKey) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const getObjByKeyFrmArray = (array = [], key, value) => {
  return array.filter((singleObj) => singleObj[key] == value)?.[0];
};

function hideEverythingOnTheFormExcept(meta, exceptionLabels = []) {
  meta.map((singleField, index) => {
    singleField = {
      ...singleField,
      hide: !exceptionLabels.includes(singleField.label),
    };
    meta[index] = singleField;
  });
}

const getArrayByKeyFrmArray = (array = [], key, filterArray = []) => {
  return array.filter((singleObj) => filterArray.includes(singleObj[key]));
};

function updateManipulatedFieldValues({
  manipulatedFieldValues = {},
  labelsToCheck = [],
  meta,
  allValues,
  labelToKeyMap,
  selectLabel,
}) {
  const isLabelsPresent = labelsToCheck.filter((_label) =>
    selectedLabelIs(meta, allValues, labelToKeyMap, selectLabel, _label)
  );
  if (
    labelsToCheck.length === 0 ||
    !labelsToCheck.includes(isLabelsPresent[0])
  ) {
    manipulatedFieldValues[labelToKeyMap[selectLabel]] = "";
  }
}

function getNumberOfProductsToShow(
  allValues,
  numberOfLineItemsOptionsMap,
  labelToKeyMap
) {
  const selectedRadio = allValues[labelToKeyMap["Product Count"]];

  const radioOption = getObjByKeyFrmArray(
    numberOfLineItemsOptionsMap,
    "value",
    selectedRadio
  );
  return radioOption?.label;
}

function modifyProductFields(meta, productDetails, labelToKeyMap, updateMeta) {
  Object.keys(productDetails).map((singleLabel) => {
    modifyFieldInMeta(
      meta,
      labelToKeyMap[productDetails[singleLabel]],
      updateMeta
    );
  });
}

function buildOptions(data) {
  return data.map((item) => ({
    label: item.cl_cf_prds_title,
    value: item.cl_cf_prds_id,
  }));
}

function filterProductsByParentId(products, parentId) {
  return products.filter(
    (product) => product.cl_cf_prds_par_id === String(parentId)
  );
}

function getKeyByValue(data, value) {
  return Object.keys(data).find((key) => data[key] === value);
}

function getValuesByLabels(objectArray, labelsArray) {
  return objectArray
    .filter((item) => labelsArray.includes(item.label))
    .map((item) => item.value);
}

function showMultipleFields(meta, labelToKeyMap, mandatoryLabels = []) {
  mandatoryLabels.forEach((singleField) => {
    modifyFieldInMeta(meta, labelToKeyMap[singleField], {
      hide: false,
    });
  });
}

function getProductByArticle(articleLabel, data) {
  const index = data.articleNo.indexOf(articleLabel);
  if (index !== -1) {
    return data.productName[index];
  }
  return null; // Return null if the article label is not found
}

function getOptionsByFieldKey(meta, key) {
  const field = meta.find((item) => item.key === key);
  return field ? field.options : [];
}

function getLabelByValue(data, value) {
  const item = data.find((obj) => obj.value === value);
  return item ? item.label : undefined;
}

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Unknown error";
  let manipulatedFieldValues = {};

  let { meta, allValues, changedValues, request_data, currentMeta } = event;

  let errorMessage;
  let disableFormSubmissionButton = false;

  console.log("event", event);
  console.log("meta", JSON.stringify(meta));

  const labelToKeyMap = {};

  meta.map((singleField) => {
    labelToKeyMap[singleField.label || singleField.cust_component_value] =
      singleField.key;
  });

  console.log(labelToKeyMap);

  const productFieldsMap = [
    {
      article_label: "Product 1 Article no.",
      product_label: "Product 1 Name",
      qty_label: "Product 1 Quantity",
      qty_type_label: "Product 1 Quantity Type",
      defective_label: "Product 1 Defective",
      defective_type_label: "Product 1 Defective Type",
      material_label: "Product 1 Material ( on which it is installed )",
      measurements_label: "Product 1 Measurements",
      dimensions_label: "Product 1 Carcass Dimensions",
      thickness_label: "Product 1 Thickness (mm)",
      width_label: "Product 1 Width (mm)",
      height_label: "Product 1 Height (mm)",
      depth_label: "Product 1 Depth (mm)",
      weight_label: "Product 1 Weight (kg)",
      issue_label: "Product 1 Issue",
      installation_correct_label: "Product 1  Installation Correct",
      dimensions_correct_label: "Product 1 Dimensions Correct",
      mishandling_label: "Product 1 Mishandling",
      remarks_label: "Product 1 Remarks",
      replacement_req_label: "Product 1 Replacement Required",
      replacement_type_label: "Product 1 Replacement Type",
      replacement_qty_label: "Product 1 Replacement Qty",
      replacement_qty_type_label: "Product 1 Replacement Qty Type",
      box_avail_label: "Product 1 Box Available",
      prod_images_label: "Product 1 Images",
      dn_sticker_label: "Product 1 DN Sticker Image",
      comp_box_img_label: "Product 1 Complete Box Image",
      dmg_missing_label: "Product 1 Damaged/Missing part Image",
      other_img_label: "Product 1 Other Images",
    },
    {
      article_label: "Product 2 Article no.",
      product_label: "Product 2 Name",
      qty_label: "Product 2 Quantity",
      qty_type_label: "Product 2 Quantity Type",
      defective_label: "Product 2 Defective",
      defective_type_label: "Product 2 Defective Type",
      material_label: "Product 2 Material ( on which it is installed )",
      measurements_label: "Product 2 Measurements",
      dimensions_label: "Product 2 Carcass Dimensions",
      thickness_label: "Product 2 Thickness (mm)",
      width_label: "Product 2 Width (mm)",
      height_label: "Product 2 Height (mm)",
      depth_label: "Product 2 Depth (mm)",
      weight_label: "Product 2 Weight (kg)",
      issue_label: "Product 2 Issue",
      installation_correct_label: "Product 2 Installation Correct",
      dimensions_correct_label: "Product 2 Dimensions Correct",
      mishandling_label: "Product 2 Mishandling",
      remarks_label: "Product 2 Remarks",
      replacement_req_label: "Product 2 Replacement Required",
      replacement_type_label: "Product 2 Replacement Type",
      replacement_qty_label: "Product 2 Replacement Qty",
      replacement_qty_type_label: "Product 2 Replacement Qty Type",
      box_avail_label: "Product 2 Box Available",
      prod_images_label: "Product 2 Images",
      prod_images_label: "Product 2 Images",
      dn_sticker_label: "Product 2 DN Sticker Image",
      comp_box_img_label: "Product 2 Complete Box Image",
      dmg_missing_label: "Product 2 Damaged/Missing part Image",
      other_img_label: "Product 2 Other Images",
    },
    {
      article_label: "Product 3 Article no.",
      product_label: "Product 3 Name",
      qty_label: "Product 3 Quantity",
      qty_type_label: "Product 3 Quantity Type",
      defective_label: "Product 3 Defective",
      defective_type_label: "Product 3 Defective Type",
      material_label: "Product 3 Material ( on which it is installed )",
      measurements_label: "Product 3 Measurements",
      dimensions_label: "Product 3 Carcass Dimensions",
      thickness_label: "Product 3 Thickness (mm)",
      width_label: "Product 3 Width (mm)",
      height_label: "Product 3 Height (mm)",
      depth_label: "Product 3 Depth (mm)",
      weight_label: "Product 3 Weight (kg)",
      issue_label: "Product 3 Issue",
      installation_correct_label: "Product 3 Installation Correct",
      dimensions_correct_label: "Product 3 Dimensions Correct",
      mishandling_label: "Product 3 Mishandling",
      remarks_label: "Product 3 Remarks",
      replacement_req_label: "Product 3 Replacement Required",
      replacement_type_label: "Product 3 Replacement Type",
      replacement_qty_label: "Product 3 Replacement Qty",
      replacement_qty_type_label: "Product 3 Replacement Qty Type",
      box_avail_label: "Product 3 Box Available",
      prod_images_label: "Product 3 Images",
      prod_images_label: "Product 3 Images",
      dn_sticker_label: "Product 3 DN Sticker Image",
      comp_box_img_label: "Product 3 Complete Box Image",
      dmg_missing_label: "Product 3 Damaged/Missing part Image",
      other_img_label: "Product 3 Other Images",
    },
    {
      article_label: "Product 4 Article no.",
      product_label: "Product 4 Name",
      qty_label: "Product 4 Quantity",
      qty_type_label: "Product 4 Quantity Type",
      defective_label: "Product 4 Defective",
      defective_type_label: "Product 4 Defective Type",
      material_label: "Product 4 Material ( on which it is installed )",
      measurements_label: "Product 4 Measurements",
      dimensions_label: "Product 4 Carcass Dimensions",
      thickness_label: "Product 4 Thickness (mm)",
      width_label: "Product 4 Width (mm)",
      height_label: "Product 4 Height (mm)",
      depth_label: "Product 4 Depth (mm)",
      weight_label: "Product 4 Weight (kg)",
      issue_label: "Product 4 Issue",
      installation_correct_label: "Product 4 Installation Correct",
      dimensions_correct_label: "Product 4 Dimensions Correct",
      mishandling_label: "Product 4 Mishandling",
      remarks_label: "Product 4 Remarks",
      replacement_req_label: "Product 4 Replacement Required",
      replacement_type_label: "Product 4 Replacement Type",
      replacement_qty_label: "Product 4 Replacement Qty",
      replacement_qty_type_label: "Product 4 Replacement Qty Type",
      box_avail_label: "Product 4 Box Available",
      prod_images_label: "Product 4 Images",
      prod_images_label: "Product 4 Images",
      dn_sticker_label: "Product 4 DN Sticker Image",
      comp_box_img_label: "Product 4 Complete Box Image",
      dmg_missing_label: "Product 4 Damaged/Missing part Image",
      other_img_label: "Product 4 Other Images",
    },
    {
      article_label: "Product 5 Article no.",
      product_label: "Product 5 Name",
      qty_label: "Product 5 Quantity",
      qty_type_label: "Product 5 Quantity Type",
      defective_label: "Product 5 Defective",
      defective_type_label: "Product 5 Defective Type",
      material_label: "Product 5 Material ( on which it is installed )",
      measurements_label: "Product 5 Measurements",
      dimensions_label: "Product 5 Carcass Dimensions",
      thickness_label: "Product 5 Thickness (mm)",
      width_label: "Product 5 Width (mm)",
      height_label: "Product 5 Height (mm)",
      depth_label: "Product 5 Depth (mm)",
      weight_label: "Product 5 Weight (kg)",
      issue_label: "Product 5 Issue",
      installation_correct_label: "Product 5 Installation Correct",
      dimensions_correct_label: "Product 5 Dimensions Correct",
      mishandling_label: "Product 5 Mishandling",
      remarks_label: "Product 5 Remarks",
      replacement_req_label: "Product 5 Replacement Required",
      replacement_type_label: "Product 5 Replacement Type",
      replacement_qty_label: "Product 5 Replacement Qty",
      replacement_qty_type_label: "Product 5 Replacement Qty Type",
      box_avail_label: "Product 5 Box Available",
      prod_images_label: "Product 5 Images",
      prod_images_label: "Product 5 Images",
      dn_sticker_label: "Product 5 DN Sticker Image",
      comp_box_img_label: "Product 5 Complete Box Image",
      dmg_missing_label: "Product 5 Damaged/Missing part Image",
      other_img_label: "Product 5 Other Images",
    },
  ];

  hideEverythingOnTheFormExcept(meta, ["Type of Service"]);

  const typeOfService = allValues[labelToKeyMap["Type of Service"]];
  console.log("typeOfService -> ", typeOfService);

  const typeOfServiceOptions = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Type of Service"]
  ).options;

  const typeOfServiceOptionsMap = {
    "Product Complaint": "343701a5-4275-4f47-8824-7ca4fd165098",
    "Installation Guidance": "8387acf5-ff04-4a49-b686-01545e9004f5",
    "Damage Material Inspection": "5b9aaf4d-c9b3-4133-baa9-89d7c87563c8",
    "Fresh Material Inspection": "2f314692-e4d0-4d93-aa36-53b0f157ea3a",
    APC: "306822de-71ba-4a1d-8449-7cf32b0b088d",
    "Dealer Display Inspection": "5f8198a5-36cf-47de-8600-5ae7eb15cafd",
    Training: "73ddbdf6-a946-47e3-b6cf-0a70f707bd98",
    Exhibition: "99ac546c-2031-4b93-9f65-ce4250832cd2",
    "Damaged in new box": "5104e532-38e2-488d-ad07-ca18d4399f89",
    "Product Missing in new box": "14c5fd7f-c880-4344-b8f2-03cd0ae68d13",
  };

  const optionsToNotShowProdFields = getValuesByLabels(typeOfServiceOptions, [
    "Damage Material Inspection",
    "Fresh Material Inspection",
    "APC",
    "Dealer Display Inspection",
    "Training",
    "Exhibition",
  ]);

  const installGuidance = getValuesByLabels(typeOfServiceOptions, [
    "Installation Guidance",
  ]);

  if (typeOfService && optionsToNotShowProdFields.includes(typeOfService)) {
    modifyFieldInMeta(meta, labelToKeyMap["Product Count"], {
      hide: true,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Images"], {
      hide: false,
    });
  } else if (
    typeOfService &&
    typeOfService == installGuidance.includes(typeOfService)
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Product Count"], {
      hide: false,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Invoice"], {
      hide: true,
    });
  } else if (typeOfService) {
    modifyFieldInMeta(meta, labelToKeyMap["Product Count"], {
      hide: false,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Invoice"], {
      hide: false,
    });
  }

  const numberOfProductCountOptionsMap = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap["Product Count"]
  ).options;

  const numberOfProductsToShow = getNumberOfProductsToShow(
    allValues,
    numberOfProductCountOptionsMap,
    labelToKeyMap
  );

  // Iterate over the productFieldsMap array
  for (let i = 0; i < productFieldsMap.length; i++) {
    let singleProduct = productFieldsMap[i]; // Get the current product field

    // Check if the current index is within the number of products to show
    if (i < parseInt(numberOfProductsToShow)) {
      //Setting Article Options
      modifyFieldInMeta(
        meta,
        labelToKeyMap[productFieldsMap[i]["article_label"]],
        {
          options: articleProductOptions.articleNo,
        }
      );
      if (typeOfService == typeOfServiceOptionsMap["Product Complaint"]) {
        showMultipleFields(meta, labelToKeyMap, [
          // "Invoice",
          productFieldsMap[i]["article_label"],
          productFieldsMap[i]["product_label"],
          productFieldsMap[i]["qty_label"],
          productFieldsMap[i]["qty_type_label"],
          productFieldsMap[i]["defective_label"],
          productFieldsMap[i]["defective_type_label"],
          productFieldsMap[i]["material_label"],
          productFieldsMap[i]["measurements_label"],
          productFieldsMap[i]["thickness_label"],
          productFieldsMap[i]["width_label"],
          productFieldsMap[i]["depth_label"],
          productFieldsMap[i]["height_label"],
          productFieldsMap[i]["weight_label"],
          productFieldsMap[i]["issue_label"],
          productFieldsMap[i]["installation_correct_label"],
          productFieldsMap[i]["dimensions_correct_label"],
          productFieldsMap[i]["mishandling_label"],
          productFieldsMap[i]["remarks_label"],
          productFieldsMap[i]["replacement_req_label"],
          productFieldsMap[i]["prod_images_label"],
        ]);

        const replacementReqValue =
          allValues[
            labelToKeyMap[productFieldsMap[i]["replacement_req_label"]]
          ];

        const replacementRequiredOptions = getOptionsByFieldKey(
          meta,
          labelToKeyMap[productFieldsMap[i]["replacement_req_label"]]
        );

        const selectedReplacementRequired = getLabelByValue(
          replacementRequiredOptions,
          replacementReqValue
        );

        console.log(
          "yeti replacementReqValue label",
          selectedReplacementRequired
        );
        if (selectedReplacementRequired == "Yes") {
          showMultipleFields(meta, labelToKeyMap, [
            productFieldsMap[i]["replacement_type_label"],
            productFieldsMap[i]["replacement_qty_label"],
            productFieldsMap[i]["replacement_qty_type_label"],
          ]);
        }
      } else if (
        typeOfService == typeOfServiceOptionsMap["Installation Guidance"]
      ) {
        showMultipleFields(meta, labelToKeyMap, [
          productFieldsMap[i]["article_label"],
          productFieldsMap[i]["product_label"],
          productFieldsMap[i]["qty_label"],
          productFieldsMap[i]["qty_type_label"],
          productFieldsMap[i]["dimensions_label"],
          productFieldsMap[i]["material_label"],
          // productFieldsMap[i]["measurements_label"],
          productFieldsMap[i]["thickness_label"],
          productFieldsMap[i]["width_label"],
          productFieldsMap[i]["depth_label"],
          productFieldsMap[i]["height_label"],
          productFieldsMap[i]["weight_label"],
          productFieldsMap[i]["remarks_label"],
          productFieldsMap[i]["prod_images_label"],
        ]);
      } else if (
        typeOfService ==
          typeOfServiceOptionsMap["Product Missing in new box"] ||
        typeOfService == typeOfServiceOptionsMap["Damaged in new box"]
      ) {
        showMultipleFields(meta, labelToKeyMap, [
          // "Invoice",
          productFieldsMap[i]["article_label"],
          productFieldsMap[i]["product_label"],
          productFieldsMap[i]["qty_label"],
          productFieldsMap[i]["qty_type_label"],
          productFieldsMap[i]["defective_label"],
          productFieldsMap[i]["defective_type_label"],
          productFieldsMap[i]["box_avail_label"],
          productFieldsMap[i]["remarks_label"],
          productFieldsMap[i]["dn_sticker_label"],
          productFieldsMap[i]["comp_box_img_label"],
          productFieldsMap[i]["dmg_missing_label"],
          productFieldsMap[i]["other_img_label"],
        ]);
      }
    }

    modifyFieldInMeta(meta, labelToKeyMap[singleProduct.weight_label], {
      disabled: true,
    });
    modifyFieldInMeta(meta, labelToKeyMap[singleProduct.product_label], {
      disabled: true,
    });

    //get article value to fill product name
    const articleValue =
      allValues[labelToKeyMap[productFieldsMap[i]["article_label"]]];

    if (articleValue) {
      manipulatedFieldValues[labelToKeyMap[singleProduct.product_label]] =
        getProductByArticle(articleValue, articleProductOptions) || "";
    }

    //calculate weight with dimensions and material density
    const materialFieldValue =
      allValues[labelToKeyMap[productFieldsMap[i]["material_label"]]];
    const materialFieldLabel = materialOptionsMap[materialFieldValue];
    const materialDensity = materialDensityMap[materialFieldLabel] || 0;
    const widthValue =
      allValues[labelToKeyMap[productFieldsMap[i]["width_label"]]] || 0;
    const depthValue =
      allValues[labelToKeyMap[productFieldsMap[i]["depth_label"]]] || 0;
    const heightValue =
      allValues[labelToKeyMap[productFieldsMap[i]["height_label"]]] || 0;
    const thicknessValue =
      allValues[labelToKeyMap[productFieldsMap[i]["thickness_label"]]] || 0;
    const qtyValue =
      allValues[labelToKeyMap[productFieldsMap[i]["qty_label"]]] || 0;

    if (materialDensity) {
      const calculatedWeight =
        (widthValue * heightValue * thicknessValue * materialDensity) / 1000000;
      const weightFormatted = calculatedWeight.toFixed(2);
      //final weight
      manipulatedFieldValues[labelToKeyMap[singleProduct.weight_label]] =
        weightFormatted;
    }

    if (
      allValues[labelToKeyMap[productFieldsMap[i]["qty_label"]]] &&
      allValues[labelToKeyMap[productFieldsMap[i]["qty_label"]]] < 1
    ) {
      errorMessage = {
        duration: 3,
        message: `Quantity ${i + 1} cannot be less than 1`,
      };
      disableFormSubmissionButton = true;
    }
  }

  currentValueTypeOfService = changedValues[labelToKeyMap["Type of Service"]];
  if (currentValueTypeOfService) {
    hideEverythingOnTheFormExcept(meta, ["Type of Service"]);
    if (
      currentValueTypeOfService &&
      optionsToNotShowProdFields.includes(currentValueTypeOfService)
    ) {
      modifyFieldInMeta(meta, labelToKeyMap["Product Count"], {
        hide: true,
      });
      modifyFieldInMeta(meta, labelToKeyMap["Images"], {
        hide: false,
      });
    } else if (
      currentValueTypeOfService &&
      currentValueTypeOfService ==
        installGuidance.includes(currentValueTypeOfService)
    ) {
      modifyFieldInMeta(meta, labelToKeyMap["Product Count"], {
        hide: false,
      });
      modifyFieldInMeta(meta, labelToKeyMap["Invoice"], {
        hide: true,
      });
    } else if (currentValueTypeOfService) {
      modifyFieldInMeta(meta, labelToKeyMap["Product Count"], {
        hide: false,
      });
      modifyFieldInMeta(meta, labelToKeyMap["Invoice"], {
        hide: false,
      });
    }
    manipulatedFieldValues[labelToKeyMap["Product Count"]] = "";
    manipulatedFieldValues[labelToKeyMap["Invoice"]] = "";
  }

  responseStatus = true;
  responseMessage = "success";
  const response = {
    status: responseStatus,
    message: responseMessage,
    data: {
      meta,
      allValues,
      changedValues,
      manipulatedFieldValues,
      disableFormSubmissionButton,
      errorMessage,
    },
  };
  return response;
};

exports.handler = handler;
