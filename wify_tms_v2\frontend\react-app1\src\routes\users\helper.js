import { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';
import ConfigHelpers from '../../util/ConfigHelpers';
import React from 'react';
import { Button } from 'antd';
import {
    addressFill,
    getAddressBasedOnLatAndLng,
    getAddressFieldKeys,
    getConcatenatedAddressFrmForm,
} from '../../util/CustomerHelpers';
import LocationSearchInput from '../../components/LocationSearchInput';
import RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';
import MapComponent from '../../components/wify-utils/MapComponent/index ';
import { handleClearSelect } from '../../util/helpers';

export const getUsersActiveInfoMeta = (
    userData,
    setShowAlertMessage,
    frBulkUpdate = false
) => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'is_active',
                label: 'Active',
                widget: 'switch',
                initialValue: userData?.form_data?.is_active || true,
                onChange: (value) => {
                    if (!frBulkUpdate) {
                        setShowAlertMessage(value);
                    }
                },
            },
        ],
    };
    return meta;
};
export const getUsersLastSeenInfoMeta = () => {
    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'last_seen',
                label: 'Last Seen',
            },
        ],
    };
    return meta;
};

export const getPrimaryUsrRole = (formRef, userData, primaryRole) => {
    let primaryRoleList = [];
    let selectedRoleList =
        formRef?.current?.getFieldValue('user_role') ||
        userData?.form_data?.user_role;
    if (selectedRoleList && userData?.roleList) {
        primaryRoleList = userData.roleList.filter(
            (role) =>
                selectedRoleList.includes(role.value) ||
                role.value == primaryRole
        );
    }
    return primaryRoleList;
};

export const getUsersInfoMeta = (
    userConfigData,
    userData,
    frBulkUpdate = false,
    frExportModal = false,
    refreshUI = undefined,
    selectedSrvcTypesOrVerticals,
    isServiceProvider = false,
    formRef,
    orgSettingsData,
    editMode
) => {
    // console.log("BulkAssign userData",userData);
    const countryCode = orgSettingsData?.country_code;
    const mobileDigit = orgSettingsData?.mobile_digit;

    let assgndOrgIdsFieldMeta = [];
    if (ConfigHelpers.isServiceProvider()) {
        assgndOrgIdsFieldMeta.push({
            key: 'assgnd_org_ids',
            label: 'Assign Customer Access (If empty then a user has access to all CUSTOMERS)',
            widget: 'select',
            widgetProps: {
                mode: 'multiple',
                optionFilterProp: 'children',
                onChange: (value) => {
                    // console.log('value - ',value);
                },
            },
            options: userData?.possbile_orgs_list,
            placeholder: 'Please select customer access',
        });
    }

    let usrCustomFieldsMetaJson = [];
    if (userConfigData) {
        usrCustomFieldsMetaJson = decodeFieldsMetaFrmJson(
            JSON.stringify(userConfigData?.value),
            1
        );
    }
    let selectedSrvcTypeOrVerticalsArray = isServiceProvider
        ? userData?.form_data?.form_data?.user_vertical
        : userData?.form_data?.form_data?.srvc_types_assigned_to_user;

    const srvcTypeFieldBasedFieldSelectors = [];
    if (selectedSrvcTypesOrVerticals || selectedSrvcTypeOrVerticalsArray) {
        let srvcTypeOrVerticalList = [];
        if (isServiceProvider) {
            srvcTypeOrVerticalList = userData?.verticals_list;
        } else {
            srvcTypeOrVerticalList = userData?.srvc_type_list;
        }

        let selectedSrvcTypesOrVerticalsFrmForm = selectedSrvcTypesOrVerticals
            ? selectedSrvcTypesOrVerticals || []
            : selectedSrvcTypeOrVerticalsArray || [];

        if (selectedSrvcTypesOrVerticalsFrmForm?.length > 0) {
            // eslint-disable-next-line no-unused-expressions
            selectedSrvcTypesOrVerticalsFrmForm?.map(
                (singleSelectedSrvcTypeOrVertical) => {
                    const formData = retrieveSelectedFormData(
                        srvcTypeOrVerticalList,
                        singleSelectedSrvcTypeOrVertical
                    );
                    const srvcTypeCategoryFieldOptions =
                        getSrvcCategoryFieldOptions(
                            formData.form_data,
                            isServiceProvider
                        );
                    let selectorKey;
                    if (isServiceProvider) {
                        selectorKey = `${singleSelectedSrvcTypeOrVertical}_skill_for_vertical`;
                    } else {
                        selectorKey = `${singleSelectedSrvcTypeOrVertical}_skill_for_srvc_type`;
                    }
                    srvcTypeCategoryFieldOptions &&
                        srvcTypeFieldBasedFieldSelectors.push({
                            key: selectorKey,
                            label: `Select skills for ${formData?.label} ${isServiceProvider ? 'vertical' : 'service type'}`,
                            widget: 'select',
                            widgetProps: {
                                mode: 'multiple',
                                allowClear: true,
                                onChange: () => {
                                    if (refreshUI) {
                                        refreshUI();
                                    }
                                },
                            },
                            options: srvcTypeCategoryFieldOptions,
                        });

                    const getSelectedSkills = () => {
                        let selected_skills =
                            formRef?.current?.getFieldValue(selectorKey);
                        return selected_skills;
                    };

                    let skillLevelPrefix = isServiceProvider
                        ? 'vertical_level'
                        : 'srvc_type_level';
                    const skillLevelOptions = [
                        {
                            value: `${skillLevelPrefix}_1`,
                            label: '1(Beginner)',
                        },
                        { value: `${skillLevelPrefix}_2`, label: '2' },
                        { value: `${skillLevelPrefix}_3`, label: '3' },
                        { value: `${skillLevelPrefix}_4`, label: '4' },
                        { value: `${skillLevelPrefix}_5`, label: '5(Expert)' },
                    ];
                    let getSelectedSkillsFrForm = getSelectedSkills()
                        ? getSelectedSkills()
                        : userData?.form_data?.form_data?.[selectorKey];
                    if (
                        getSelectedSkillsFrForm &&
                        srvcTypeCategoryFieldOptions
                    ) {
                        // eslint-disable-next-line no-unused-expressions
                        getSelectedSkillsFrForm?.map((singleSelectedSkills) => {
                            const selectedSkillObject =
                                singleSelectedSkills.value
                                    ? srvcTypeCategoryFieldOptions?.find(
                                          (option) =>
                                              option.value ===
                                              singleSelectedSkills.value
                                      )
                                    : srvcTypeCategoryFieldOptions?.find(
                                          (option) =>
                                              option.value ===
                                              singleSelectedSkills
                                      );
                            srvcTypeFieldBasedFieldSelectors.push({
                                key: `selected_key_fr_${selectedSkillObject?.label}_of_${formData?.label}`,
                                label: `Select level for ${selectedSkillObject?.label} of ${formData?.label} ${isServiceProvider ? 'vertical' : 'service type'}`,
                                widget: 'select',
                                widgetProps: {
                                    mode: '',
                                    allowClear: true,
                                    onChange: (value) => {
                                        handleClearSelect(
                                            value,
                                            formRef,
                                            `selected_key_fr_${selectedSkillObject?.label}_of_${formData?.label}`
                                        );
                                    },
                                },
                                options: skillLevelOptions,
                            });
                        });
                    }
                }
            );
        }
    }
    const primaryRole =
        formRef?.current?.getFieldValue('user_primary_role') ||
        userData?.form_data?.form_data?.user_primary_role;
    const primaryRoleOptions = getPrimaryUsrRole(
        formRef,
        userData,
        primaryRole
    );
    const primaryRoleHasOptionsMoreThanOne = primaryRoleOptions?.length > 1;

    let primaryRoleMeta =
        editMode && primaryRoleHasOptionsMoreThanOne
            ? [
                  {
                      key: 'user_primary_role',
                      label: 'Select primary role',
                      widget: 'select',
                      required: !frBulkUpdate,
                      placeholder: 'Please select primary role',
                      options: primaryRoleOptions,
                      onChange: () => {
                          if (refreshUI) {
                              refreshUI();
                          }
                      },
                  },
              ]
            : [];

    const meta = {
        columns: 4,
        formItemLayout: null,
        fields: [
            {
                key: 'user_name',
                label: 'Full Name',
                placeholder: 'Eg: john',
                required: !frBulkUpdate,
                rules: [
                    { max: 100, message: 'Name must be max 100 characters.' },
                ],
            },
            {
                key: 'user_code',
                label: 'Code',
                placeholder: 'Eg: WMUM10295',
                rules: [
                    {
                        max: 20,
                        message: 'User code must be max 20 characters.',
                    },
                ],
            },
            {
                key: 'user_designation',
                label: 'Designation',
                placeholder: 'Eg: Technician Incharges',
                rules: [
                    {
                        max: 50,
                        message: 'Designation must be max 50 characters.',
                    },
                ],
            },
            {
                key: 'user_role',
                label: 'Select specific role',
                widget: 'select',
                required: !frBulkUpdate,
                widgetProps: {
                    mode: 'multiple',
                    optionFilterProp: 'children',
                    onChange: (value) => {
                        if (refreshUI) {
                            refreshUI();
                        }

                        if (!value.includes(primaryRole)) {
                            formRef.current.setFieldsValue({
                                user_primary_role: undefined,
                            });
                            if (value?.length == 1) {
                                formRef.current.setFieldsValue({
                                    user_primary_role: value[0],
                                });
                            }
                        }
                    },
                },
                placeholder: 'Please select specific role',
                options: userData?.roleList,
            },
            ...primaryRoleMeta,
            {
                key: 'user_loc_group',
                label: 'Select Location Group',
                widget: 'select',
                widgetProps: {
                    mode: 'multiple',
                    optionFilterProp: 'children',
                    onChange: (value) => {
                        // console.log('value - ',value);
                    },
                },
                options: userData?.locGrpList,
                placeholder: 'Please select location group',
            },
            ...assgndOrgIdsFieldMeta,
            {
                key: 'user_reporting_to',
                label: 'Select Reporting to',
                widget: 'select',
                options: userData?.userList, // to be loaded from API
                widgetProps: {
                    // defaultValue:'None',
                    showSearch: true,
                    optionFilterProp: 'children',
                    onChange: (value) => {
                        // console.log('value - ',value);
                    },
                    allowClear: true,
                },
                placeholder: 'Please select reporting to',
            },
            {
                key: 'user_mobile',
                label: `Mobile Number (${countryCode})`,
                placeholder: 'Eg: 9876543210',
                required: !frBulkUpdate,
                rules: [
                    {
                        pattern: new RegExp('^[0-9]*$'),
                        message: 'Please enter your right mobile number!',
                    },
                    {
                        min: mobileDigit,
                        message: `Mobile no must be ${mobileDigit} characters.`,
                    },
                    {
                        max: mobileDigit,
                        message: `Mobile no must be ${mobileDigit} characters.`,
                    },
                ],
            },
            {
                key: 'user_email',
                label: 'Email',
                placeholder: 'Eg: <EMAIL>',
                required: !frBulkUpdate,
                rules: [
                    {
                        type: 'email',
                    },
                ],
            },
            ...(userData?.verticals_list?.length > 0 ||
            (frExportModal && ConfigHelpers.isServiceProvider())
                ? [
                      {
                          key: 'user_vertical',
                          label: 'Verticals',
                          widget: 'select',
                          widgetProps: {
                              mode: 'multiple',
                              optionFilterProp: 'children',
                              onChange: (val) => {
                                  if (refreshUI) {
                                      refreshUI();
                                  }
                              },
                          },
                          options: userData?.verticals_list,
                      },
                  ]
                : []),
            ...(ConfigHelpers.isServiceProvider()
                ? srvcTypeFieldBasedFieldSelectors
                : []),
            ...(userData?.srvc_type_list?.length > 0 ||
            frExportModal ||
            frBulkUpdate
                ? [
                      {
                          key: 'srvc_types_assigned_to_user',
                          label: 'Service Types',
                          widget: 'select',
                          widgetProps: {
                              mode: 'multiple',
                              optionFilterProp: 'children',
                              onChange: (val) => {
                                  if (refreshUI) {
                                      refreshUI();
                                  }
                              },
                          },
                          options: userData?.srvc_type_list,
                      },
                  ]
                : []),
            ...(!ConfigHelpers.isServiceProvider()
                ? srvcTypeFieldBasedFieldSelectors
                : []),
            ...usrCustomFieldsMetaJson,
        ],
    };
    return meta;
};

export const getMetaFrBulkUpdate = (
    configData,
    usersData,
    orgSettingsData = undefined
) => {
    const setShowAlertMessage = (value) => {
        this.setState({
            showAlertMessage: true,
            isActiveToggleValue: value,
        });
    };

    let userConfigData = [];
    if (configData) {
        userConfigData = configData[0];
    }

    const meta = {
        columns: 1,
        formItemLayout: null,
        fields: [
            {
                key: 'user_id',
                label: 'User ID',
                required: true,
            },
            ...getUsersInfoMeta(
                userConfigData,
                usersData,
                true,
                false,
                undefined,
                undefined,
                false,
                undefined,
                orgSettingsData
            ).fields,
            {
                key: 'password',
                label: 'Password',
                placeholder: 'Enter your password',
                widget: 'password',
                required: false,
            },
            {
                key: 'user_confirm_pass',
                label: 'Confirm Password',
                widget: 'password',
                // hasFeedback:true,
                placeholder: 'Enter your confirm password',
                required: false,
                rules: [
                    ({ getFieldValue }) => ({
                        validator(_, value) {
                            if (!value || getFieldValue('password') === value) {
                                return Promise.resolve();
                            }
                            return Promise.reject(
                                new Error(
                                    'The password and confirm password do not match !'
                                )
                            );
                        },
                    }),
                ],
            },
            ...getUsersActiveInfoMeta(usersData).fields,
        ],
    };
    return meta;
};

export const getAddressObj = (selectedPlace) => {
    const { name, address_components, geometry } = selectedPlace;
    let selectedPayload = { location: {} };
    selectedPayload.building = name;

    const typeToKeyMapping = {
        country: 'country',
        administrative_area_level_1: 'state',
        locality: 'city',
        sublocality_level_2: 'street',
        postal_code: 'postalCode',
        neighborhood: 'building',
    };

    for (const component of address_components) {
        const type = component.types[0];
        const key = typeToKeyMapping[type];
        if (key) {
            selectedPayload[key] = component.long_name;
        }
    }

    if (geometry && geometry.location) {
        const { lat, lng } = geometry.location;
        const latLng =
            typeof lat === 'function'
                ? { lat: lat(), lng: lng() }
                : { lat, lng };
        selectedPayload.location.latlng = latLng;
    }

    return selectedPayload;
};

export const getConcatenatedAddressFormData = (
    prefix = 'cust_',
    formData,
    formRef
) => {
    if (!formRef.current) {
        let addressFieldKeys = getAddressFieldKeys(prefix);
        return addressFieldKeys.filter((key) => formData?.[key]).toString();
    }
};

export const getMapAddressToUsersFieldsMeta = ({
    form_data,
    formRef,
    forceUpdateFn,
    showPickOnMapModel,
    togglePickOnMapModel,
    is_pincode_mandatory,
    orgSettingsData,
}) => {
    let filledAddress =
        getConcatenatedAddressFrmForm('cust_', formRef) ||
        getConcatenatedAddressFormData('cust_', form_data, formRef);
    let showClearFieldsButton = filledAddress && filledAddress != '';
    const latitude =
        formRef?.current?.getFieldValue('location_latitude') ||
        form_data?.location_latitude;
    const longitude =
        formRef?.current?.getFieldValue('location_Longitude') ||
        form_data?.location_Longitude;

    const onLatLngValueChange = async () => {
        const latitude = formRef?.current?.getFieldValue('location_latitude');
        const longitude = formRef?.current?.getFieldValue('location_Longitude');
        if (latitude && longitude) {
            const data = await getAddressBasedOnLatAndLng(latitude, longitude);
            addressFill(getAddressObj(data.results?.[0]), formRef);
        }
    };
    let clearGoogleAddressSearch = new Date().getTime();

    const clearAddress = (formRef) => {
        const keyEmptyValue = {};
        getAddressFieldKeys('cust_').forEach((singleKey) => {
            keyEmptyValue[singleKey] = '';
        });
        clearGoogleAddressSearch = new Date().getTime();
        let result = formRef?.current?.setFieldsValue(keyEmptyValue);
        forceUpdateFn();
    };
    const meta = {
        // colSpan:4,
        formItemLayout: null,
        fields: [
            {
                key: 'mark_location_on_map',
                render: () => {
                    return (
                        <div>
                            <LocationSearchInput
                                placeholder="Address"
                                useCountryAndID={true}
                                onChange={(address) => {
                                    addressFill(address, formRef);
                                    forceUpdateFn();
                                }}
                                orgSettingsData={orgSettingsData}
                                triggerClear={clearGoogleAddressSearch}
                            />
                            <Button
                                onClick={() => {
                                    togglePickOnMapModel();
                                }}
                            >
                                Pick on Map
                            </Button>
                            {showPickOnMapModel && (
                                <MapComponent
                                    showPickOnMapModel={showPickOnMapModel}
                                    defaultLocation={{
                                        lat: latitude || 22.5437692,
                                        lng: longitude || 79.1230844,
                                    }}
                                    onChange={(address) => {
                                        addressFill(address, formRef);
                                        forceUpdateFn();
                                        togglePickOnMapModel();
                                    }}
                                    togglePickOnMapModel={() =>
                                        togglePickOnMapModel()
                                    }
                                />
                            )}
                        </div>
                    );
                },
            },
            {
                key: 'clear_fields',
                colSpan: 4,
                label: 'Clear fields',
                render() {
                    return (
                        <>
                            {showClearFieldsButton && (
                                <Button
                                    type="link"
                                    onClick={() => {
                                        clearAddress(formRef);
                                    }}
                                >
                                    Reset Address
                                </Button>
                            )}
                        </>
                    );
                },
            },
            {
                key: 'cust_line_0',
                colSpan: 4,
                label: 'Flat no',
                onChange: () => {
                    forceUpdateFn();
                },

                rules: [
                    {
                        max: 50,
                    },
                ],
            },
            {
                key: 'cust_line_1',
                colSpan: 4,
                label: 'Building/Apartment name',
                onChange: () => {
                    forceUpdateFn();
                },
                rules: [
                    {
                        max: 200,
                    },
                ],
            },
            {
                key: 'cust_line_2',
                label: 'Line 1',
                colSpan: 4,
                disabled: true,
                rules: [
                    {
                        max: 1000,
                    },
                ],
            },
            {
                key: 'cust_line_3',
                label: 'Line 2',
                colSpan: 4,
                disabled: true,
                rules: [
                    {
                        max: 200,
                    },
                ],
            },
            {
                key: 'cust_pincode',
                label: 'Pincode',
                colSpan: 2,
                required: is_pincode_mandatory,
                // widget: RemoteSourceSelect,
                disabled: true,
                widgetProps: {
                    mode: 'single',
                    url: '/searcher',
                    // placeholder:"Start typing..",
                    params: {
                        fn: 'getPincode',
                    },
                    widgetProps: {
                        mode: 'single',
                        labelInValue: false,
                        showSearch: true,
                        style: {
                            width: '100%',
                        },
                    },
                },
            },
            {
                key: 'cust_city',
                label: 'City',
                colSpan: 2,
                // widget: RemoteSourceSelect,
                disabled: true,
                widgetProps: {
                    mode: 'single',
                    url: '/searcher',
                    // placeholder:"Start typing..",
                    params: {
                        fn: 'getCities',
                    },
                    widgetProps: {
                        mode: 'single',
                        labelInValue: false,
                        showSearch: true,
                        style: {
                            width: '100%',
                        },
                    },
                },
            },
            {
                key: 'cust_state',
                label: 'State',
                colSpan: 4,
                // widget: RemoteSourceSelect,
                disabled: true,
                widgetProps: {
                    mode: 'single',
                    url: '/searcher',
                    // placeholder:"Start typing..",
                    params: {
                        fn: 'getState',
                    },
                    widgetProps: {
                        mode: 'single',
                        labelInValue: false,
                        showSearch: true,
                        style: {
                            width: '100%',
                        },
                    },
                },
            },
            {
                key: 'location_latitude',
                label: 'Latitude',
                colSpan: 4,
                required: formRef?.current?.getFieldValue('location_Longitude'),
                placeholder: 'Eg 37.7749',
                onChange: (e) => {
                    onLatLngValueChange();
                    forceUpdateFn();
                },
            },
            {
                key: 'location_Longitude',
                label: 'Longitude',
                colSpan: 4,
                required: formRef?.current?.getFieldValue('location_latitude'),
                placeholder: 'Eg -122.4194',
                onChange: (e) => {
                    onLatLngValueChange();
                    forceUpdateFn();
                },
            },
            ...(latitude && longitude
                ? [
                      {
                          key: 'view_location',
                          render() {
                              let url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;
                              return (
                                  <a href={url} target="_blank">
                                      <i class="icon icon-location"> </i>View on
                                      Google Map
                                  </a>
                              );
                          },
                      },
                  ]
                : []),
        ],
    };
    return meta;
};

export const getCapacityFieldsMeta = (
    getForm,
    verticals_list,
    capacityConfigs,
    handleVerticalChange,
    fieldSetDetails,
    keys
) => {
    const { label, value } = fieldSetDetails;
    const meta = {
        // colSpan:4,
        formItemLayout: null,
        fields: [
            {
                key: keys.vertical_key,
                label: `${label} Vertical`,
                widget: 'select',
                widgetProps: {
                    mode: 'single',
                    showSearch: true,
                    optionFilterProp: 'children',
                    allowClear: true,
                    style: { width: '100%' },
                },
                onChange: (value) => {
                    // If field is cleared, set it to empty string directly
                    if (value === undefined) {
                        getForm().setFieldsValue({
                            [keys.vertical_key]: '',
                        });
                    }

                    // Handle vertical change and clear dependent fields
                    handleVerticalChange(value, keys);
                    getForm().setFieldsValue({
                        [keys.srvc_hub_key]: '',
                        [keys.skill_key_1]: '',
                        [keys.skill_key_2]: '',
                        [keys.skill_key_3]: '',
                    });
                },
                options: verticals_list,
            },
            {
                key: keys.srvc_hub_key,
                label: `${label} Service Hub`,
                widget: 'select',
                widgetProps: {
                    mode: 'single',
                    showSearch: true,
                    optionFilterProp: 'children',
                    allowClear: true,
                    style: { width: '100%' },
                },
                onChange: (value) => {
                    // If field is cleared, set it to empty string directly
                    if (value === undefined) {
                        getForm().setFieldsValue({
                            [keys.srvc_hub_key]: '',
                        });
                    }
                },
                options: capacityConfigs.srvc_hub_list,
            },
            {
                key: keys.skill_key_1,
                label: `${label} Skill 1`,
                widget: 'select',
                widgetProps: {
                    mode: 'single',
                    showSearch: true,
                    optionFilterProp: 'children',
                    allowClear: true,
                    style: { width: '100%' },
                },
                onChange: (value) => {
                    // If field is cleared, set it to empty string directly
                    if (value === undefined) {
                        getForm().setFieldsValue({
                            [keys.skill_key_1]: '',
                        });
                    }
                },
                options: capacityConfigs.skills,
                tooltip: 'Will get jobs for this first',
                // antd rule to make sure that this skill is not selected in 2 & 3
                rules: [
                    {
                        validator: (_, value) => {
                            let skill2 = getForm().getFieldValue(
                                keys.skill_key_2
                            );
                            let skill3 = getForm().getFieldValue(
                                keys.skill_key_3
                            );
                            if (value && (value == skill2 || value == skill3)) {
                                return Promise.reject(
                                    new Error('Skill already selected')
                                );
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                key: keys.skill_key_2,
                label: `${label} Skill 2`,
                widget: 'select',
                widgetProps: {
                    mode: 'single',
                    showSearch: true,
                    optionFilterProp: 'children',
                    allowClear: true,
                    style: { width: '100%' },
                },
                onChange: (value) => {
                    // If field is cleared, set it to empty string directly
                    if (value === undefined) {
                        getForm().setFieldsValue({
                            [keys.skill_key_2]: '',
                        });
                    }
                },
                options: capacityConfigs.skills,
                tooltip: 'Will get jobs for this if no job for skill 1',
                // antd rule to make sure that this skill is not selected in 1 & 3
                rules: [
                    {
                        validator: (_, value) => {
                            let skill1 = getForm().getFieldValue(
                                keys.skill_key_1
                            );
                            let skill3 = getForm().getFieldValue(
                                keys.skill_key_3
                            );
                            if (value && (value == skill1 || value == skill3)) {
                                return Promise.reject(
                                    new Error(
                                        'Skill already selected, you can keep this blank not mandatory!'
                                    )
                                );
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
            {
                key: keys.skill_key_3,
                label: `${label} Skill 3`,
                widget: 'select',
                widgetProps: {
                    mode: 'single',
                    showSearch: true,
                    optionFilterProp: 'children',
                    allowClear: true,
                    style: { width: '100%' },
                },
                onChange: (value) => {
                    // If field is cleared, set it to empty string directly
                    if (value === undefined) {
                        getForm().setFieldsValue({
                            [keys.skill_key_3]: '',
                        });
                    }
                },
                options: capacityConfigs.skills,
                tooltip: 'Will get jobs for this if no job for skill 2',
                // antd rule to make sure that this skill is not selected in 1 & 2
                rules: [
                    {
                        validator: (_, value) => {
                            let skill1 = getForm().getFieldValue(
                                keys.skill_key_1
                            );
                            let skill2 = getForm().getFieldValue(
                                keys.skill_key_2
                            );
                            if (value && (value == skill1 || value == skill2)) {
                                return Promise.reject(
                                    new Error(
                                        'Skill already selected, you can keep this blank not mandatory!'
                                    )
                                );
                            }
                            return Promise.resolve();
                        },
                    },
                ],
            },
        ],
    };
    return meta;
};

export const retrieveSelectedFormData = (
    srvcTypeOrVerticalList,
    singleSelectedSrvcTypeOrVertical
) => {
    const formData = srvcTypeOrVerticalList.filter((eachData) => {
        return eachData.value == singleSelectedSrvcTypeOrVertical;
    });
    return formData[0];
};

export const getSrvcCategoryFieldOptions = (formData, isServiceProvider) => {
    let categoryFieldKey;
    if (isServiceProvider) {
        categoryFieldKey = 'srvc_prvdr_category_field';
    } else {
        categoryFieldKey = 'srvc_category_field';
    }

    const originalFields = decodeFieldsMetaFrmJson(
        isServiceProvider
            ? formData?.sp_cust_fields_json
            : formData?.srvc_cust_fields_json
    );
    const categoryFieldValue = formData?.[categoryFieldKey];
    const srvcCategoryFieldOptions = originalFields.filter((originalField) => {
        return originalField.key == categoryFieldValue;
    });
    return srvcCategoryFieldOptions[0]?.options;
};

export const formatDate = (dateString) => {
    try {
        const date = new Date(dateString);
        const month = date.toLocaleString('default', { month: 'short' });
        const day = date.getDate();
        // Replace spaces with hyphens
        const formattedDate = `${day} ${month}`;
        return formattedDate;
    } catch (error) {
        //swallow
    }
    return dateString;
};

export const removeAndAppendSixDigitIntegers = (inputString) => {
    try {
        const regex = /\b\d{6}, /; // Matches consecutive 6-digit numbers followed by a comma and a space
        const matches = inputString.match(regex);
        if (!matches) {
            return inputString; // No matches found, return the input as is
        }
        // Remove the matched 6-digit integers from the input string
        const stringWithoutIntegers = inputString.replace(regex, '').trim();
        // Remove any double commas, remove the comma following the 6-digit numbers, and then append the matched 6-digit integers at the end, separated by a single comma
        const cleanedString = stringWithoutIntegers.replace(/,+/g, ',').trim();
        const appendedString =
            cleanedString.replace(/,\s*$/, '') + ', ' + matches.join(', ');
        return appendedString.slice(0, -2);
    } catch (error) {
        //swallow
    }
    return inputString;
};
