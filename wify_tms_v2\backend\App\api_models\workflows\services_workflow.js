const { shortenLink } = require('../link_shortner_model');
const { sendFeedbackSMS } = require('../utils/sms_templates');
const {
    parseSMSTemplateWithValues,
    getSMSTemplateDetails,
} = require('./consumer-sms-notification-template-manager');
const {
    parseWhatsappTemplateWithValues,
    getWhatsappTemplateDetails,
    createDataFrWhatsappAPICall,
} = require('./consumer-whatsapp-notification-template-manager');
const {
    concatAddress,
    checkIfQueryHasPincode,
} = require('../utils/geo_coding_utils');
const { allQueues } = require('../queues_v2/queues');
const { TRANSACTION_CHANNEL } = require('../queues_v2/processors/send_sms');
const {
    sendFeedbackWhatsappMsg,
} = require('../utils/feedback_whatsapp_template');
const {
    srvcStatusUpdateNotificationEmailTemplate,
} = require('../queues_v2/processors/email/templates/srvc_status_update_template');

const users_model = require('../users_model');
const user_model = require('../user_model');
let searcher_model = require('../searcher');
const {
    setParamsToUserModel,
    getParamsToSubtaskModel,
    getParamsToVaAiChatbotModel,
} = require('../queues/processors/helpers');
const { callLambdaFn } = require('../utils/lambda_helpers');
const {
    getWhatsappFeedbackTemplateDetails,
    parseWhatsappFeedbackTemplateWithValues,
} = require('./consumer-whatsapp-feedback-template-manager');
const {
    getSearcherModelFrQueue,
} = require('../queues_v2/processors/helpers/searcher_helper');
const subtasks_model = require('../subtasks_model');
const va_ai_chatbot_model = require('../va_ai_chatbot_model');
const {
    getSmsFeedbackTemplateDetails,
    parseSmsFeedbackTemplateWithValues,
} = require('./consumer-sms-feedback-template-manager');
const {
    getSubjectFrSrvcStatusUpdate,
    srvcStatusUpdateViaVANotificationEmailTemplate,
} = require('../queues_v2/processors/email/templates/srvc_req_bulk_updation_via_VA');
const {
    getHiddenAndRequiredKeys,
    getMissingRequiredFields,
} = require('../utils/helper');
function checkQueryFrAuthorityModification(query) {
    return Object.keys(query).some((key) => key.startsWith('authority_'));
}

class services_workflow {
    constructor(services_model) {
        this.services_model = services_model.getFreshInstance(services_model);
    }

    /*
        Basic rules set that would drive what to do next
        when a service request is created or updated
        Make sure you just add jobs to the queue 
        and not do anything heavy here
    */
    trigger(query, entry_id, dbResp, is_customer_access = 0, db = undefined) {
        console.log(
            'service request creation workflow trigger',
            entry_id,
            query,
            dbResp
        );
        // if its multiple requests
        if (entry_id > 0) {
            // A service request was updated
            // this could be status update
            // this could form update
            // this could be addition of comment
            if (query['new_status']) {
                // this is a status update
                this.triggetStatusUpdateWorkflow(query, entry_id);
            } else if (query.update_deletion) {
                this.triggerSrvcReqDeletionNotificationWorkflow(
                    query,
                    entry_id,
                    dbResp
                );
            } else {
                this.triggerSrvcUpdateWorkflow(query, entry_id);
            }
        } else if (query.is_bulk_update && dbResp.data.entry_ids.length > 0) {
            let entry_ids = dbResp.data.entry_ids || [];
            let entry_ids_vs_query = dbResp.data?.entry_ids_vs_query;
            if (entry_ids.length > 0) {
                entry_ids.forEach((single_entry_id) => {
                    let updatedQueryFrmDb = entry_ids_vs_query[single_entry_id];
                    this.triggerSrvcUpdateWorkflow(
                        updatedQueryFrmDb,
                        single_entry_id
                    );
                });
            }
        } else {
            // this was creation
            let cust_org_id = dbResp.data.cust_org_id;
            let new_entry_id = dbResp.data.entry_id;
            let new_entry_ids = dbResp.data.entry_ids;
            let existing_req_ids_fr_api_call =
                dbResp.data.existing_req_ids_fr_api_call || [];
            let entry_ids_vs_query = dbResp.data?.entry_ids_vs_query;
            if (new_entry_id > 0) {
                let updatedQueryFrmDb = dbResp?.data?.form_data || query;
                console.log(
                    'single srvc_req creation updatedQueryFrmDb',
                    new_entry_id,
                    updatedQueryFrmDb
                );
                this.triggerRequestCreationWorkflow(
                    updatedQueryFrmDb,
                    new_entry_id,
                    is_customer_access,
                    cust_org_id
                );
            } else if (new_entry_ids?.length > 0) {
                new_entry_ids.forEach((single_entry_id) => {
                    let updatedQueryFrmDb = entry_ids_vs_query[single_entry_id];
                    if (
                        existing_req_ids_fr_api_call.includes(single_entry_id)
                    ) {
                        //When calling the update via API, we should call the update workflow process.
                        if (updatedQueryFrmDb['new_status']) {
                            this.triggetStatusUpdateWorkflow(
                                updatedQueryFrmDb,
                                single_entry_id
                            );
                        } else {
                            this.triggerSrvcUpdateWorkflow(
                                updatedQueryFrmDb,
                                single_entry_id,
                                true //isBulkCreateorApiCall
                            );
                        }
                        // this entry was already there, so lets skip this
                        console.log(
                            'Existing order id ignored',
                            single_entry_id
                        );
                    } else {
                        this.triggerRequestCreationWorkflow(
                            updatedQueryFrmDb,
                            single_entry_id,
                            is_customer_access,
                            cust_org_id,
                            true //isBulkCreateorApiCall
                        );
                    }
                });
            }
        }
    }

    triggerSrvcUpdateWorkflow(query, entry_id, isBulkCreateorApiCall = false) {
        let jobData = { query, entry_id, isBulkCreateorApiCall };
        jobData['services_model_data'] =
            this.services_model.getServicesModelData(this.services_model);
        allQueues.WIFY_SRVC_REQ_UPDATE_WORKFLOW.addJob(jobData);
    }

    triggetStatusUpdateWorkflow(query, entry_id) {
        let jobData = { query, entry_id };
        jobData['services_model_data'] =
            this.services_model.getServicesModelData(this.services_model);
        allQueues.WIFY_SRVC_REQ_STATUS_UPDATE_WORKFLOW.addJob(jobData);
    }

    triggerRequestCreationWorkflow(
        query,
        new_entry_id,
        is_customer_access = 0,
        cust_org_id = 0,
        isBulkCreateorApiCall = false
    ) {
        let jobData = {
            query,
            new_entry_id,
            is_customer_access,
            cust_org_id,
            isBulkCreateorApiCall,
        };
        jobData['services_model_data'] =
            this.services_model.getServicesModelData(this.services_model);
        allQueues.WIFY_SRVC_REQ_CREATION_WORKFLOW.addJob(jobData);
    }

    triggerSrvcReqDeletionWorkflowFrmApi(
        query,
        entry_id,
        dbResp,
        is_customer_access = 0,
        db = undefined
    ) {
        let cust_org_id = dbResp.data.cust_org_id;
        let new_entry_id = dbResp.data.entry_id;
        let new_entry_ids = dbResp.data.entry_ids;
        let existing_req_ids_fr_api_call =
            dbResp.data.existing_req_ids_fr_api_call || [];
        let entry_ids_vs_query = dbResp.data?.entry_ids_vs_query;
        if (new_entry_id > 0) {
            let updatedQueryFrmDb = dbResp?.data?.form_data || query;
            console.log(
                'single srvc_req creation updatedQueryFrmDb',
                new_entry_id,
                updatedQueryFrmDb
            );
            if (updatedQueryFrmDb.for_deletion) {
                this.triggerSrvcReqDeletion(updatedQueryFrmDb, new_entry_id);
            }
        } else if (new_entry_ids?.length > 0) {
            new_entry_ids.forEach((single_entry_id) => {
                let updatedQueryFrmDb = entry_ids_vs_query[single_entry_id];
                if (existing_req_ids_fr_api_call.includes(single_entry_id)) {
                    // this entry was already there, so lets skip this
                    console.log('Existing order id ignored', single_entry_id);
                    if (updatedQueryFrmDb.for_deletion) {
                        this.triggerSrvcReqDeletion(
                            updatedQueryFrmDb,
                            single_entry_id
                        );
                    }
                }
            });
        }
    }

    triggerSrvcReqDeletion(
        query,
        new_entry_id,
        is_customer_access = 0,
        cust_org_id = 0
    ) {
        let jobData = { query, new_entry_id, is_customer_access, cust_org_id };
        jobData['services_model_data'] =
            this.services_model.getServicesModelData(this.services_model);
        allQueues.WIFY_SRVC_REQ_DELETION.addJob(jobData);
    }

    triggerSrvcReqDeletionNotificationWorkflow(
        query,
        new_entry_id,
        dbResp,
        is_customer_access = 0,
        cust_org_id = 0
    ) {
        let jobData = {
            query,
            new_entry_id,
            dbResp,
            is_customer_access,
            cust_org_id,
        };
        jobData['services_model_data'] =
            this.services_model.getServicesModelData(this.services_model);
        allQueues.WIFY_TMS_PROCESS_SRVC_REQ_DELETION_NOTIFICATION_WORKFLOW.addJob(
            jobData
        );
    }

    trigger_v1(
        query,
        entry_id,
        dbResp,
        is_customer_access = 0,
        db = undefined
    ) {
        return new Promise((resolve, reject) => {
            // setTimeout(()=>{
            //     console.log('Workflow runner - ',query,entry_id,dbResp);
            // },5000);
            // Getting the config data
            this.services_model
                .getOverviewProto(query)
                .then((operationResp) => {
                    if (operationResp.isSuccess()) {
                        // we got the configuration data for this service type id
                        this.config_data = JSON.parse(
                            operationResp.resp
                        )?.config_data;
                        // console.log('Got config data');
                        if (entry_id > 0) {
                            // A service request was updated
                            // this could be status update
                            // this could form update
                            // this could be addition of comment
                            if (query['new_status']) {
                                // this is a status update
                                this.processStatusUpdateWorkflows(
                                    query,
                                    entry_id
                                );
                            } else {
                                this.processSrvcUpdateWorkFlow(
                                    query,
                                    entry_id,
                                    db
                                );
                            }
                        } else {
                            // A service request was created or multiple service request where created
                            // entry id resides in the dbResp

                            let cust_org_id = dbResp.data.cust_org_id;
                            let new_entry_id = dbResp.data.entry_id;
                            let new_entry_ids = dbResp.data.entry_ids;
                            if (new_entry_id > 0) {
                                this.processSrvcReqCreationWorkFlows(
                                    query,
                                    new_entry_id,
                                    is_customer_access,
                                    cust_org_id,
                                    db
                                );
                            } else if (new_entry_ids?.length > 0) {
                                new_entry_ids.forEach((single_entry_id) => {
                                    this.processSrvcReqCreationWorkFlows(
                                        query,
                                        single_entry_id,
                                        is_customer_access,
                                        cust_org_id,
                                        db
                                    );
                                });
                            }
                        }
                    } else {
                        console.log('Get config data failed in workflow');
                    }
                });
            // we always do resolve
            resolve();
        });
    }

    //add a trigger for processSrvcReqUpdationWorkFlowFrmVA
    triggerSrvcReqUpdationWorkFlowFrmVA(query, dbResp, is_customer_access = 0) {
        let jobData = {
            query,
            dbResp,
            is_customer_access,
        };
        jobData['services_model_data'] =
            this.services_model.getServicesModelData(this.services_model);
        allQueues.WIFY_TMS_PROCESS_SRVC_REQ_UPDATION_THR_VA_WORKFLOW.addJob(
            jobData
        );
    }

    async processSrvcReqUpdationWorkFlowFrmVA(query, job_id) {
        const va_ai_chatbot_model = getParamsToVaAiChatbotModel(
            this.services_model,
            this.services_model.db
        );

        if (query?.custom_fields_config_data?.is_cstm_fields_dynamic) {
            // Process dynamic custom fields for each entry in batch_Data
            if (query?.batch_Data && Array.isArray(query.batch_Data)) {
                const { callLambdaFn } = require('../utils/lambda_helpers');
                const lambdaARN = query?.custom_fields_config_data?.lambda_arn;

                if (lambdaARN) {
                    try {
                        // Process each batch entry asynchronously
                        const processedBatchData = await Promise.all(
                            query?.batch_Data.map(async (singleBatchEntry) => {
                                try {
                                    // Prepare lambda payload for dynamic form processing
                                    const lambdaPayload = {
                                        meta: JSON.parse(
                                            query?.custom_fields_config_data
                                                ?.cust_fields_json || '[]'
                                        ),
                                        allValues: {
                                            attachments: {},
                                            mic_files: {},
                                            camera_files: {},
                                        },
                                        changedValues: {},
                                        request_data:
                                            singleBatchEntry?.form_data,
                                    };

                                    const params = {
                                        FunctionName: lambdaARN,
                                        InvocationType: 'RequestResponse',
                                        LogType: 'Tail',
                                        Payload: JSON.stringify(lambdaPayload),
                                    };

                                    console.log(
                                        'processSrvcReqUpdationWorkFlowFrmVA :: calling lambda for batch entry',
                                        {
                                            lambdaARN,
                                            entryKeys:
                                                Object.keys(singleBatchEntry),
                                        }
                                    );

                                    // Call lambda function
                                    const lambdaResponse =
                                        await callLambdaFn(params);
                                    let requiredFields = [];
                                    let missingRequiredFields = [];

                                    if (
                                        lambdaResponse.StatusCode === 200 &&
                                        lambdaResponse.Payload
                                    ) {
                                        const parsedData = JSON.parse(
                                            lambdaResponse.Payload
                                        );
                                        if (
                                            parsedData.status &&
                                            parsedData.data
                                        ) {
                                            // Get the processed meta from lambda response
                                            const finalMeta =
                                                parsedData?.data?.meta;
                                            console.log(
                                                'processSrvcReqUpdationWorkFlowFrmVA :: final meta',
                                                finalMeta
                                            );

                                            // Get required fields that are hidden
                                            requiredFields =
                                                getHiddenAndRequiredKeys(
                                                    finalMeta
                                                );

                                            // Calculate missing required fields
                                            missingRequiredFields =
                                                getMissingRequiredFields(
                                                    requiredFields,
                                                    singleBatchEntry?.form_data
                                                );

                                            console.log(
                                                'processSrvcReqUpdationWorkFlowFrmVA :: missing required fields',
                                                missingRequiredFields
                                            );

                                            // Return batch entry with missing fields array
                                            return {
                                                ...singleBatchEntry,
                                                missing_fields:
                                                    missingRequiredFields,
                                            };
                                        }
                                    }

                                    console.log(
                                        'processSrvcReqUpdationWorkFlowFrmVA :: lambda response not successful for batch entry'
                                    );

                                    // Return original batch entry with empty missing fields if lambda fails
                                    return {
                                        ...singleBatchEntry,
                                        missing_fields: [],
                                    };
                                } catch (lambdaError) {
                                    console.error(
                                        'processSrvcReqUpdationWorkFlowFrmVA :: lambda error for batch entry:',
                                        lambdaError
                                    );
                                    // Return original batch entry with empty missing fields if lambda fails
                                    return {
                                        ...singleBatchEntry,
                                        missing_fields: [],
                                    };
                                }
                            })
                        );

                        // Update query with processed batch data
                        query.batch_data = processedBatchData;
                        console.log(
                            'processSrvcReqUpdationWorkFlowFrmVA :: dynamic custom fields processing completed'
                        );
                    } catch (error) {
                        console.error(
                            'processSrvcReqUpdationWorkFlowFrmVA :: error processing dynamic custom fields:',
                            error
                        );
                        // Continue with original data if processing fails, but add empty missing_fields array
                        if (
                            query?.batch_Data &&
                            Array.isArray(query.batch_data)
                        ) {
                            query.batch_Data = query.batch_data.map(
                                (singleBatchEntry) => ({
                                    ...singleBatchEntry,
                                    missing_fields: [],
                                })
                            );
                        }
                    }
                } else {
                    console.log(
                        'processSrvcReqUpdationWorkFlowFrmVA :: lambda ARN not found in custom fields config'
                    );
                    // Add empty missing_fields array to each batch entry when lambda ARN is not found
                    if (query?.batch_Data && Array.isArray(query.batch_Data)) {
                        query.batch_Data = query.batch_Data.map(
                            (singleBatchEntry) => ({
                                ...singleBatchEntry,
                                missing_fields: [],
                            })
                        );
                    }
                }
            } else {
                // Add empty missing_fields array to each batch entry when batch_Data exists but is not an array
                if (query?.batch_Data && Array.isArray(query.batch_Data)) {
                    query.batch_Data = query.batch_Data.map(
                        (singleBatchEntry) => ({
                            ...singleBatchEntry,
                            missing_fields: [],
                        })
                    );
                }
            }
        }
        const response =
            await va_ai_chatbot_model.getPossibleSrvcRequestsAndUpdate({
                ...query,
                call_thr_wrkflw: true,
            });
        console.log('processSrvcReqUpdationWorkFlowFrmVA :: query', query);
        console.log(
            'processSrvcReqUpdationWorkFlowFrmVA :: response',
            response
        );
        const dbResp = response?.resp;

        const emailId = query?.user_context?.user_details?.email;
        const userName = query?.user_context?.user_details?.name;
        let sendEmailData = {
            to: emailId,
            subject: getSubjectFrSrvcStatusUpdate(query, dbResp),
            message: srvcStatusUpdateViaVANotificationEmailTemplate(
                query,
                dbResp,
                userName
            ),
            cc: '',
            org_id: this.services_model?.org_id,
            usr_id: this.services_model?.usr_id,
            ip_address: this.services_model?.ip_address,
            user_agent: this.services_model?.user_agent,
        };

        try {
            allQueues.WIFY_SEND_EMAIL.addJob(sendEmailData);
        } catch (error) {
            console.log('processSrvcReqUpdationWorkFlowFrmVA :: error', error);
        }
    }

    async processSrvcUpdateWorkFlow(
        query,
        entry_id,
        isBulkCreateorApiCall = false
    ) {
        console.log('processSrvcUpdateWorkFlow', entry_id, query);
        try {
            await this.getConfigDataFrSrvcType(query);
            console.log('Loaded config data..');
        } catch (error) {
            console.log(error);
            return;
        }

        if (isBulkCreateorApiCall && query?.cust_pincode) {
            await this.updateSrvcReqCityStateByPincode(query, entry_id);
        }

        this.autoMovementStatusBasedOnFields(query, entry_id);
        if (checkQueryFrAuthorityModification(query)) {
            await this.autoMovementStatusBasedOnAuthorities(query, entry_id);
            try {
                await this.processRatingsQueue(query, entry_id);
            } catch (e) {
                console.log('processRatingsQueue failed ', e);
            }
        }

        this.processSpecificFieldsWiseAuthorityNotification(query, entry_id);
        this.processDiscountApprovalNotification(query, entry_id);
        this.processDisApprovalStatusChangeNotification(query, entry_id);
        this.processSendFrBillingNotification(query, entry_id);

        let updatedAddressIfAny = concatAddress(query);
        if (updatedAddressIfAny) {
            try {
                await this.services_model.syncSrvcReqLocWithCache(entry_id);
            } catch (rejectedResp) {
                // console.log(" service_workflow syncSrvcReqLocWithCache rejectedResp ->",rejectedResp.form_data);
                // start a bull job
                // to hit Google Api and then internally it will resync cache
                // console.log("service syncSrvcReqLocWithCache in rejectedResp..",rejectedResp);
                let jobData = {
                    form_data: rejectedResp.form_data,
                    query,
                    new_entry_id: entry_id,
                    services_model_data:
                        this.services_model.getServicesModelData(
                            this.services_model
                        ),
                };
                allQueues.WIFY_SRVC_REQ_LOC_MAPPING.addJob(jobData);
            }
        }
        if (updatedAddressIfAny || updatedAddressIfAny == '') {
            console.log(
                'processSrvcUpdateWorkFlow updatedAddressIfAny',
                entry_id,
                updatedAddressIfAny,
                query
            );
            await this.processUpdateLocGrpIdsInSrvcReqs(entry_id);
            try {
                await this.processUpdateRatedByColumFrRatingsTableQueue(
                    { ...query, locationUpdated: true },
                    entry_id
                );
            } catch (error) {
                console.log(
                    'processUpdateRatedByColumFrRatingsTableQueue failed ',
                    error
                );
            }
        }
        if (checkIfQueryHasPincode(query)) {
            console.log(
                'autoAuthorityAssignedBasedOnLocation query has pincode',
                entry_id
            );

            await this.autoAuthorityAssignedBasedOnLocation(query, entry_id);
        } else {
            console.log(
                'autoAuthorityAssignedBasedOnLocation query does not have pincode',
                entry_id
            );
        }
        //if lambda for srvc req deletion is enabled
        if (query.update_deletion) {
            this.triggerSrvcReqDeletion(query, entry_id);
        }
    }

    async processSrvcReqDeletion(query, entry_id) {
        try {
            console.log('processSrvcDeletionWorkFlow', entry_id, query);
            try {
                await this.getConfigDataFrSrvcType(query);
                console.log('Loaded config data..');
            } catch (error) {
                console.log(error);
                return;
            }
            let {
                enable_lambda_hook_fr_srvc_req_deletion,
                lambda_arn_fr_srvc_req_deletion,
            } = this.config_data;

            const srvcReqDetails = await this.services_model.getSingleEntry(
                { srvc_type_id: query.srvc_type_id },
                entry_id
            );
            if (enable_lambda_hook_fr_srvc_req_deletion) {
                const lambdaARN = lambda_arn_fr_srvc_req_deletion;
                const payload = {
                    srvcReqDetails: JSON.parse(srvcReqDetails.resp).form_data,
                };
                const params = {
                    FunctionName: lambdaARN,
                    InvocationType: 'RequestResponse',
                    LogType: 'Tail',
                    Payload: JSON.stringify({ ...payload }),
                };
                try {
                    let respData = await callLambdaFn(params);
                    let lambdaRespData = JSON.parse(respData.Payload);
                    if (lambdaRespData && lambdaRespData.body) {
                        let { srvc_req_form_data } = lambdaRespData.body;
                        if (srvc_req_form_data) {
                            let queryFrSrvcReq = {
                                srvc_type_id: query.srvc_type_id,
                                ...srvc_req_form_data,
                            };
                            await this.services_model.createOrUpdate(
                                queryFrSrvcReq,
                                entry_id
                            );
                        }
                    } else {
                        console.log(lambdaRespData.message);
                    }
                } catch (error) {
                    console.log(
                        'callLambdaFnForSrvcDeletionTrigger :: error :: ',
                        error
                    );
                }
            } else {
                console.log('Lambda hook for srvc req deletion is disabled');
            }
        } catch (error) {
            console.log('processSrvcReqDeletion failed', error);
        }
    }

    async processUpdateLocGrpIdsInSrvcReqs(entry_id) {
        try {
            console.log('processUpdateLocGrpIdsInSrvcReqs entry_id', entry_id);
            await this.services_model.UpdateLocGrpIdsInSrvcReqs(entry_id);
        } catch (error) {
            console.log('processUpdateLocGrpIdsInSrvcReqs failed', error);
        }
    }

    async getReqDataById(entry_id) {
        try {
            const srvcReqResp =
                await this.services_model.getSrvcReqDataById(entry_id);
            let srvcReqObj = {};
            srvcReqObj['org_id'] = srvcReqResp.org_id;
            srvcReqObj['prvdr_id'] = srvcReqResp.prvdr_id;
            srvcReqObj['srvc_type_id'] = srvcReqResp.srvc_type_id;
            return srvcReqObj;
        } catch (error) {
            console.log('getReqDataById failed', error);
            return;
        }
    }

    async processSpecificFieldsWiseAuthorityNotification(query, entry_id) {
        let org_id = query.org_id;
        let srvc_type_id = query.srvc_type_id;
        let brand_config_data = this.config_data || [];
        let sp_config_data =
            (await this.services_model.getConfigDataFrSrvcprvdr(
                org_id,
                srvc_type_id
            )) || [];

        let all_notification = [];
        let brndNotification =
            brand_config_data?.srvc_type_specific_fields_wise_notification ||
            [];
        if (!Array.isArray(brndNotification)) {
            brndNotification = JSON.parse(brndNotification);
        }
        let spNotification =
            sp_config_data?.srvc_type_specific_fields_wise_notification || [];
        if (!Array.isArray(spNotification)) {
            spNotification = JSON.parse(spNotification);
        }
        all_notification = [...brndNotification, ...spNotification];
        // console.log("all_notification",all_notification)

        try {
            const srvcReqOperationResp =
                await this.services_model.getSingleEntry({}, entry_id);
            if (srvcReqOperationResp.isSuccess()) {
                let srvc_req_resp = JSON.parse(
                    srvcReqOperationResp.resp
                )?.form_data;
                all_notification.forEach((singleNotification) => {
                    let notification_specific_field =
                        singleNotification?.notification_specific_field;
                    // the notification_specific_field can 12_uekrbhkjeb or just uekrbhkjeb
                    Object.keys(query).map(async (singleQueryField) => {
                        if (
                            notification_specific_field == singleQueryField ||
                            notification_specific_field ==
                                `${srvc_type_id}_${singleQueryField}`
                        ) {
                            // the notification is matching the current field that is updated
                            // process notification
                            // loop on all the authorities in notification
                            //  check if the authority is present in the request
                            //      add a job to send email to the authority
                            this.services_model.triggerNotificationToAuthorityFrFieldUpdate(
                                singleNotification,
                                srvc_req_resp,
                                singleQueryField,
                                query,
                                brand_config_data,
                                sp_config_data
                            );
                        }
                    });
                    // the notification_specific_file_field can 12_uekrbhkjeb or just uekrbhkjeb
                    if (query?.lastChangedFileSectionIds) {
                        let lastChangedFileSecIds =
                            query?.lastChangedFileSectionIds || [];
                        if (
                            lastChangedFileSecIds &&
                            lastChangedFileSecIds.length > 0
                        ) {
                            //remove dublicate id from array
                            let filteredLastChangedFileSecIds =
                                lastChangedFileSecIds.filter(
                                    (item, index, self) =>
                                        self.indexOf(item) === index
                                );
                            filteredLastChangedFileSecIds.forEach(
                                (singleChangedFileSecId) => {
                                    if (
                                        notification_specific_field ==
                                            singleChangedFileSecId ||
                                        notification_specific_field ==
                                            `${srvc_type_id}_${singleChangedFileSecId}`
                                    ) {
                                        this.services_model.triggerNotificationToAuthorityFrFieldUpdate(
                                            singleNotification,
                                            srvc_req_resp,
                                            singleChangedFileSecId,
                                            query,
                                            brand_config_data,
                                            sp_config_data
                                        );
                                    }
                                }
                            );
                        }
                    }
                });
            } else {
                console.log(
                    'getSingleEntry function failed for processSpecificFieldsWiseAuthorityNotification'
                );
            }
        } catch (error) {
            console.log(
                'processSpecificFieldsWiseAuthorityNotification failed',
                error
            );
        }
    }

    async processDiscountApprovalNotification(query, entry_id) {
        try {
            //if brand side click on button send_for_discount_approval then
            let send_for_discount_approval = query?.send_for_discount_approval;
            if (send_for_discount_approval) {
                //Check if billing discounting feature is enable.
                let config_data = this.config_data;
                if (config_data?.srvc_type_enable_billing_discounting) {
                    this.services_model.checkMatchingRulesAndSendEmailToApprover(
                        query,
                        entry_id,
                        config_data
                    );
                }
            }
            //service provider side click on button send_for_discount_approval then
            let sp_send_for_discount_approval =
                query?.sp_send_for_discount_approval;
            if (sp_send_for_discount_approval) {
                //Check if billing discounting feature is enable.
                let sp_config_data =
                    (await this.services_model.getConfigDataFrSrvcprvdr(
                        query.org_id,
                        query.srvc_type_id
                    )) || [];
                if (sp_config_data?.srvc_type_enable_billing_discounting) {
                    this.services_model.checkMatchingRulesAndSendEmailToApprover(
                        query,
                        entry_id,
                        sp_config_data,
                        true
                    );
                }
            }
        } catch (error) {
            console.log('processDiscountApprovalNotification failed', error);
        }
    }

    async processDisApprovalStatusChangeNotification(query, entry_id) {
        try {
            /* BRAND SIDE */
            //1. if any change on discount_approval_status then
            //2. Check if billing discounting feature is enable.
            let discount_approval_status = query?.discount_approval_status;
            if (discount_approval_status) {
                if (this.config_data?.srvc_type_enable_billing_discounting) {
                    this.services_model.ifAnyChangeInDisApprovalStatusSendEmailToApproverAndRequester(
                        query,
                        entry_id,
                        this.config_data
                    );
                }
            }
            /* SERVICE PROVIDER SIDE */
            //1. if any change on discount_approval_status then
            //2. get config_data for srvc prvdr
            //3. Check if billing discounting feature is enable.
            let sp_discount_approval_status =
                query?.sp_discount_approval_status;
            if (sp_discount_approval_status) {
                let sp_config_data =
                    (await this.services_model.getConfigDataFrSrvcprvdr(
                        query.org_id,
                        query.srvc_type_id
                    )) || [];
                if (sp_config_data?.srvc_type_enable_billing_discounting) {
                    this.services_model.ifAnyChangeInDisApprovalStatusSendEmailToApproverAndRequester(
                        query,
                        entry_id,
                        sp_config_data,
                        true
                    );
                }
            }
        } catch (error) {
            console.log(
                'processDisApprovalStatusChangeNotification failed',
                error
            );
        }
    }

    async processSendFrBillingNotification(query, entry_id) {
        try {
            /* BRAND SIDE */
            //1. if click on send_for_billing then
            //2. Check if billing feature is enable ?.
            let send_for_billing = query?.send_for_billing;
            if (send_for_billing) {
                if (this.config_data?.srvc_type_enable_billing) {
                    this.services_model.sendForBillingNotification(
                        query,
                        entry_id,
                        this.config_data
                    );
                }
            }
            /* SERVICE PROVIDER SIDE */
            //1. if click on sp_send_for_billing then
            //2. get config_data for srvc prvdr
            //3. Check if billing feature is enable ?.
            let sp_send_for_billing = query?.sp_send_for_billing;
            if (sp_send_for_billing) {
                let sp_config_data =
                    (await this.services_model.getConfigDataFrSrvcprvdr(
                        query.org_id,
                        query.srvc_type_id
                    )) || [];
                if (sp_config_data?.srvc_type_enable_billing) {
                    this.services_model.sendForBillingNotification(
                        query,
                        entry_id,
                        sp_config_data,
                        true
                    );
                }
            }
        } catch (error) {
            console.log('processSendFrBillingNotification failed', error);
        }
    }

    async autoMovementStatusBasedOnFields(query, entry_id) {
        const getInputAndFilesFields = () => {
            //inputCustomFields is an array to store all the keys of input custom filed keys except filesfields
            let inputCustomFields = [];

            //filesFields is an object becoz we can know the files_type of the keys
            //object key holds files,mic or camera keys
            //object value holds type of files fields like "attachments","mic_files", or "camera_files"
            let filesFields = {};

            let cust_component_type_and_key = {
                Files: 'attachments',
                WIFY_MIC: 'mic_files',
                WIFY_CAMERA: 'camera_files',
            };

            //Get the Translated Fields
            //apply a for loop so that we can add input custom field keys in inputCustomFields array which is declared above
            //and add the keys of files,mic or camera in filesFields which is declared above
            if (this.config_data?.srvc_cust_fields_json) {
                var translatedFields = JSON.parse(
                    this.config_data?.srvc_cust_fields_json
                ).translatedFields;

                translatedFields.forEach((singleTranslatedFields) => {
                    if (
                        singleTranslatedFields.cust_component == 'Files' ||
                        singleTranslatedFields.cust_component == 'WIFY_MIC' ||
                        singleTranslatedFields.cust_component == 'WIFY_CAMERA'
                    ) {
                        filesFields[singleTranslatedFields.key] =
                            cust_component_type_and_key[
                                singleTranslatedFields.cust_component
                            ];
                    } else {
                        inputCustomFields.push(singleTranslatedFields.key);
                    }
                });
            }

            return { inputCustomFields, filesFields };
        };

        if (this.config_data?.srvc_statuses) {
            var srvc_statuses = JSON.parse(this.config_data?.srvc_statuses);

            let { inputCustomFields, filesFields } = getInputAndFilesFields();

            //previous SrvcRequest FormData
            let pre_srvc_req_form_data = query?.pre_srvc_req_form_data;

            if (pre_srvc_req_form_data) {
                for (const status_type in srvc_statuses) {
                    if (
                        Object.hasOwnProperty.call(srvc_statuses, status_type)
                    ) {
                        const statuses = srvc_statuses[status_type];
                        let current_srvc_req_form_data =
                            await this.services_model.getSrvcReqFormDataFrAutomation(
                                {},
                                entry_id
                            );

                        statuses.forEach(async (singleStatus) => {
                            if (singleStatus.key != 'open') {
                                let flag = false;
                                let count = 0;
                                let totalFieldsCount = 0;
                                let anyOneDependentCustFieldHasUndefinedValue = false;
                                let fullPrefix = `srvc_auto_movement_fr_${singleStatus.key}_key`;
                                let dependent_custom_field_keys =
                                    this.config_data[fullPrefix];

                                if (dependent_custom_field_keys) {
                                    dependent_custom_field_keys.forEach(
                                        (single_dependent_custom_field_key) => {
                                            totalFieldsCount =
                                                totalFieldsCount + 1;
                                            if (
                                                inputCustomFields.includes(
                                                    single_dependent_custom_field_key
                                                )
                                            ) {
                                                if (
                                                    current_srvc_req_form_data
                                                        ?.form_data[
                                                        single_dependent_custom_field_key
                                                    ]
                                                ) {
                                                    count = count + 1;
                                                    if (
                                                        pre_srvc_req_form_data
                                                            ?.form_data[
                                                            single_dependent_custom_field_key
                                                        ] == undefined
                                                    ) {
                                                        anyOneDependentCustFieldHasUndefinedValue = true;
                                                    }
                                                }
                                            } else {
                                                if (
                                                    Object.keys(
                                                        filesFields
                                                    ).includes(
                                                        single_dependent_custom_field_key
                                                    )
                                                ) {
                                                    let attachment_type =
                                                        filesFields[
                                                            single_dependent_custom_field_key
                                                        ];
                                                    if (
                                                        current_srvc_req_form_data
                                                            ?.form_data[
                                                            attachment_type
                                                        ]?.[
                                                            single_dependent_custom_field_key
                                                        ] != undefined &&
                                                        current_srvc_req_form_data
                                                            ?.form_data[
                                                            attachment_type
                                                        ]?.[
                                                            single_dependent_custom_field_key
                                                        ]?.length != 0
                                                    ) {
                                                        count = count + 1;
                                                        if (
                                                            pre_srvc_req_form_data
                                                                ?.form_data[
                                                                attachment_type
                                                            ]?.[
                                                                single_dependent_custom_field_key
                                                            ] == undefined ||
                                                            (Array.isArray(
                                                                pre_srvc_req_form_data
                                                                    ?.form_data[
                                                                    attachment_type
                                                                ]?.[
                                                                    single_dependent_custom_field_key
                                                                ]
                                                            ) &&
                                                                pre_srvc_req_form_data
                                                                    ?.form_data[
                                                                    attachment_type
                                                                ]?.[
                                                                    single_dependent_custom_field_key
                                                                ]?.length == 0)
                                                        ) {
                                                            anyOneDependentCustFieldHasUndefinedValue = true;
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    );
                                    if (
                                        count == totalFieldsCount &&
                                        anyOneDependentCustFieldHasUndefinedValue
                                    ) {
                                        flag = true;
                                    }

                                    if (flag) {
                                        if (
                                            singleStatus.key !=
                                            pre_srvc_req_form_data?.status
                                        ) {
                                            let newQuery = { ...query };

                                            delete newQuery.update_for_comment;
                                            delete newQuery.pre_srvc_req_form_data;

                                            newQuery['new_status'] =
                                                singleStatus.key;
                                            // newQuery["is_frm_frontend"] = true;

                                            try {
                                                let resp =
                                                    await this.services_model.createOrUpdate(
                                                        newQuery,
                                                        entry_id,
                                                        query.is_customer_access
                                                    );
                                                console.log(
                                                    'Srvc Req Update resp',
                                                    resp
                                                );
                                            } catch (error) {
                                                console.log(error);
                                                return;
                                            }
                                        }
                                    }
                                }
                            }
                        });
                    }
                }
            }
        }
    }

    getOrgID() {
        return users_model.getOrgId(this.services_model.user_context);
    }

    async autoAuthorityAssignedBasedOnLocationFrExistingRequests(
        query,
        entry_id
    ) {
        let form_data = JSON.parse(query || '{}');
        this.services_model.srvc_type_id = form_data?.srvc_type_id;
        this.services_model.srvc_req_id = entry_id;
        let role_id = form_data?.role_id;
        let checkWhetherAssigneeAlreadyPresent = true;
        if (
            form_data.is_apply_to_all_active_req &&
            form_data.is_apply_to_all_active_req == true
        ) {
            checkWhetherAssigneeAlreadyPresent = false;
        }

        try {
            await this.getConfigDataFrSrvcType({});
            console.log('Loaded config data..');
        } catch (error) {
            console.log(error);
            return;
        }
        await this.autoAuthorityAssignedBasedOnLocation(
            form_data,
            entry_id,
            role_id,
            checkWhetherAssigneeAlreadyPresent
        );
    }

    async autoAuthorityAssignedBasedOnLocation(
        query,
        entry_id,
        role_id = undefined,
        checkWhetherAssigneeAlreadyPresent = false
    ) {
        try {
            if (this.config_data == undefined) {
                await this.getConfigDataFrSrvcType(query);
            }
            // console.log('running auto authority assignment based loc logic')
            let authorities_roles = this.config_data?.srvc_authorities;
            const suffixFrAutomationEnabledCheckbox =
                '_auto_assign_based_on_location';
            let authoritiesFrWhichAutomationIsEnabled = [];
            if (authorities_roles && authorities_roles.length > 0) {
                // When auto assign authority is triggered for a specific role, filter authorities_roles to that role.
                if (role_id) {
                    authorities_roles = authorities_roles.filter(
                        (singleAuthRole) => singleAuthRole == role_id
                    );
                }
                //console.log('authorities_roles',authorities_roles)
                authoritiesFrWhichAutomationIsEnabled =
                    authorities_roles.filter((singleRoleId) => {
                        let enableAutoAssignByLocationCheckboxKey = `${singleRoleId}${suffixFrAutomationEnabledCheckbox}`;
                        return this.config_data[
                            enableAutoAssignByLocationCheckboxKey
                        ];
                    });
            }
            let isAutomationEnabledForAtleastOneAuthority =
                authoritiesFrWhichAutomationIsEnabled.length > 0;
            console.log(
                'isAutomationEnabledForAtleastOneAuthority',
                isAutomationEnabledForAtleastOneAuthority
            );
            console.log(
                'Automation is enabled for ',
                authoritiesFrWhichAutomationIsEnabled.length
            );

            let updated_form_data =
                await this.services_model.getSrvcReqFormDataFrAutomation(
                    {},
                    entry_id
                );
            let outputObj = {};
            let orgId = this.getOrgID();
            //If refresh authorities from customer access that time we need to brand org_id
            let is_refresh_authorities_frm_customer_access =
                query?.is_refresh_authorities_frm_customer_access;
            if (is_refresh_authorities_frm_customer_access) {
                orgId = updated_form_data?.brand_org_id;
            }
            let authoritiesNotFound = false;
            let assignedAuthorities = false;
            let notFoundAuthoritieskeys = [];
            let assignedAuthoritiesKeys = [];
            const autoAuthoritiesKeyPrefix = `Auto assigned authorities`;
            const { srvc_type_id } = query;
            const pincode =
                updated_form_data?.form_data?.cust_pincode != null
                    ? String(updated_form_data.form_data.cust_pincode)
                    : updated_form_data?.form_data?.cust_pincode;
            if (
                updated_form_data &&
                isAutomationEnabledForAtleastOneAuthority
            ) {
                for (const singleRoleId of authoritiesFrWhichAutomationIsEnabled) {
                    const authorityIdKeyInSrvcReq = `authority_${singleRoleId}`;
                    const existingUserInAuthority =
                        updated_form_data.form_data[authorityIdKeyInSrvcReq];
                    if (checkWhetherAssigneeAlreadyPresent) {
                        if (
                            !updated_form_data.form_data[
                                authorityIdKeyInSrvcReq
                            ] == '' ||
                            !updated_form_data.form_data[
                                authorityIdKeyInSrvcReq
                            ] == undefined
                        ) {
                            //if authority already assigned on the existing srvc req then skip the below code
                            continue;
                        }
                    }
                    if (pincode) {
                        // pincode exists
                        // gethe user for the singleRoleId as per the location group
                        let userIdBasedOnLocGrp =
                            await this.services_model.getAuthorityIdByLocationGroup(
                                orgId,
                                pincode,
                                singleRoleId
                            );
                        console.log('userIdBasedOnLocGrp', userIdBasedOnLocGrp);
                        if (
                            userIdBasedOnLocGrp &&
                            userIdBasedOnLocGrp.value != existingUserInAuthority
                        ) {
                            // the authority needs to be updated
                            outputObj[authorityIdKeyInSrvcReq] =
                                userIdBasedOnLocGrp.value;
                            console.log(
                                'New authority will be assigned',
                                userIdBasedOnLocGrp.label
                            );
                        } else {
                            console.log(
                                'Same authority already exists for the request'
                            );
                            if (userIdBasedOnLocGrp == null) {
                                authoritiesNotFound = true;
                                outputObj[authorityIdKeyInSrvcReq] = '';
                                notFoundAuthoritieskeys.push(
                                    authorityIdKeyInSrvcReq
                                );
                            }
                            if (
                                userIdBasedOnLocGrp &&
                                userIdBasedOnLocGrp.value ==
                                    existingUserInAuthority
                            ) {
                                assignedAuthorities = true;
                                assignedAuthoritiesKeys.push(
                                    authorityIdKeyInSrvcReq
                                );
                            }
                        }
                    } else if (!pincode && existingUserInAuthority) {
                        outputObj[authorityIdKeyInSrvcReq] = '';
                    } else {
                        // pincode does not exist
                        // outputObj[authorityIdKeyInSrvcReq] = ""
                        console.log('Pincode does not exist');
                    }
                }
            }
            if (Object.keys(outputObj).length > 0) {
                // automation effect needs to be executed here
                // console.log('Automation effect will be happening here')
                let resp = await this.services_model.createOrUpdate(
                    { ...outputObj },
                    entry_id,
                    query.is_customer_access
                );
                console.log('Automation effect resp', resp);
                if (resp.isSuccess()) {
                    //send Auto authorities assigned notification data update on timeline
                    if (pincode && pincode?.trim().length > 0) {
                        const authorityValueIsNotEmpty = Object.values(
                            outputObj
                        ).some((value) => value !== '');
                        if (authorityValueIsNotEmpty) {
                            let title = `${autoAuthoritiesKeyPrefix} - Assigned authorities for this pincode ${pincode}`;
                            await this.services_model.createTimelineforSrvcReq(
                                outputObj,
                                srvc_type_id,
                                entry_id,
                                'UPDATE',
                                title
                            );
                        }
                    } else {
                        let title = `${autoAuthoritiesKeyPrefix} - Removed assigned authorities`;
                        await this.services_model.createTimelineforSrvcReq(
                            outputObj,
                            srvc_type_id,
                            entry_id,
                            'UPDATE',
                            title
                        );
                    }
                }
            }
            if (authoritiesNotFound) {
                let title = `${autoAuthoritiesKeyPrefix} - Authorities not found for this pincode ${pincode}`;
                let authoritiesRoles = await this.getAuthoritiesRoleName(
                    srvc_type_id,
                    entry_id,
                    notFoundAuthoritieskeys,
                    orgId
                );
                let authoritiesNotFoundObj = {};
                authoritiesNotFoundObj['authorities_role'] = authoritiesRoles;
                await this.services_model.createTimelineforSrvcReq(
                    authoritiesNotFoundObj,
                    srvc_type_id,
                    entry_id,
                    'UPDATE',
                    title
                );
            }
            if (assignedAuthorities) {
                let title = `${autoAuthoritiesKeyPrefix} - Authorities already assigned for this pincode ${pincode}`;
                let authoritiesRoles = await this.getAuthoritiesRoleName(
                    srvc_type_id,
                    entry_id,
                    assignedAuthoritiesKeys,
                    orgId
                );
                let assignedAuthoritiesobj = {};
                assignedAuthoritiesobj['authorities_role'] = authoritiesRoles;
                await this.services_model.createTimelineforSrvcReq(
                    assignedAuthoritiesobj,
                    srvc_type_id,
                    entry_id,
                    'UPDATE',
                    title
                );
            }
        } catch (error) {
            console.log('autoAuthorityAssignedBasedOnLocation failed', error);
        }
    }

    async getAuthoritiesRoleName(
        srvc_type_id,
        srvc_req_id,
        authoritiesKeyList,
        orgId = undefined
    ) {
        // console.log("authoritiesKeyList",authoritiesKeyList);
        let authorityRoleList = [];
        this.initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id);
        const role_ids = authoritiesKeyList?.map((singleAuthoritiesRoleId) =>
            parseInt(singleAuthoritiesRoleId?.replace('authority_', ''))
        );
        const respData = await this.services_model.getRoleListByRoleIds(
            role_ids,
            orgId
        );
        if (respData?.isSuccess()) {
            let role_list = JSON.parse(respData.resp);
            if (role_list && role_list.length > 0) {
                role_list.forEach((singleRole) => {
                    authorityRoleList.push(singleRole?.label);
                });
            }
        } else {
            console.log('getAuthoritiesRoleName respData failed');
        }
        return authorityRoleList?.join(', ');
    }

    async autoMovementStatusBasedOnAuthorities(query, entry_id) {
        let config_data = { ...this.config_data };
        let srvc_statuses = JSON.parse(config_data?.srvc_statuses || '{}');

        if (
            config_data == undefined ||
            Object.keys(srvc_statuses).length == 0
        ) {
            console.log('Config data not found');
            return;
        }

        //taking all changed authority keys from query
        const authoritiesChanged = Object.keys(query)
            .filter(
                (key) =>
                    key.startsWith('authority_') &&
                    query[key] !== '' &&
                    query[key] !== null
            )
            .map((key) => key.split('_')[1]);

        //take all keys from config data which are configured to move statuses for authority assignment
        const allKeysFrStatusMovementAuthorities = Object.entries(
            config_data
        ).filter(([key]) =>
            key.startsWith('srvc_authority_based_movement_status_')
        );
        //creating an object with all the authourity movement keys and authority id values
        const authoritiesTriggeringStatusMovement = Object.fromEntries(
            allKeysFrStatusMovementAuthorities
        );

        const statusVsRole = {};

        // transforming object to status keys vs role ids

        for (let key in authoritiesTriggeringStatusMovement) {
            const newKey = key.replace(
                'srvc_authority_based_movement_status_',
                ''
            );
            statusVsRole[newKey] = authoritiesTriggeringStatusMovement[key];
        }

        const role_vs_status = {};

        //getting role_vs_status for authority based movement
        for (let key in statusVsRole) {
            const values = statusVsRole[key];
            for (let value of values) {
                role_vs_status[value] = key;
            }
        }

        let statusUpdateArray = [];

        /* 
            looping through the authorities changed to check which ones move the status and pushing that status into statusUpdateArray
        */

        for (let value of authoritiesChanged) {
            if (value in role_vs_status) {
                statusUpdateArray.push(role_vs_status[value]);
            }
        }

        /*
            After getting all the statuses that will be moved check if they are one or multiple
            if 1 then store that in finalStatusToBeUpdated
            else loop through the array and find the status which is last in the service type flow
        */

        let finalStatusToBeUpdated;

        if (statusUpdateArray.length == 1) {
            finalStatusToBeUpdated = statusUpdateArray[0];
        } else if (statusUpdateArray.length > 1) {
            const allSrvcStatusKeys = Object.values(srvc_statuses).flatMap(
                (arr) => arr.map((item) => item.key)
            );
            let highestIndex = -1;

            statusUpdateArray.forEach((value) => {
                const index = allSrvcStatusKeys.indexOf(value);
                if (index > highestIndex) {
                    highestIndex = index;
                    finalStatusToBeUpdated = value;
                }
            });
        }

        //calling the update function on the basis of the Final status to be updated
        if (finalStatusToBeUpdated) {
            let newQuery = {};

            newQuery['new_status'] = finalStatusToBeUpdated;
            // newQuery["is_frm_frontend"] = true;

            try {
                let resp = await this.services_model.createOrUpdate(
                    newQuery,
                    entry_id,
                    query.is_customer_access
                );
            } catch (error) {
                console.log(error);
                return;
            }
        }
    }

    async processStatusUpdateWorkflows(query, entry_id) {
        try {
            await this.getConfigDataFrSrvcType(query);
            console.log('Loaded config data..');
        } catch (error) {
            console.log(
                'processStatusUpdateWorkflows getConfigDataFrSrvcType error',
                error
            );
            return;
        }
        let {
            srvc_can_cust_rate,
            srvc_feedback_trigger_status_key,
            srvc_feedback_send_by_whatsapp,
            enable_lambda_for_status_update,
            service_status_movement_lambda_arn,
            select_whatsApp_feedback_template,
            select_sms_feedback_template,
            dont_send_srvc_feedback_sms,
            automation_srvc_status_enable,
            automation_srvc_statuses,
            auto_move_subtask_status_enabled,
            srvc_type_statuses_fr_automation,
        } = this.config_data;

        query['service_status_movement_lambda_arn'] =
            service_status_movement_lambda_arn;

        let currentNewStatusKey = query['new_status'];
        let lambdaFrSmsCommunication;
        let lambdaFrWhatsAppCommunication;
        // console.log('srvc_feedback_trigger_status_key',srvc_feedback_trigger_status_key);
        // Customer feedback rules
        if (
            srvc_can_cust_rate &&
            srvc_feedback_trigger_status_key &&
            srvc_feedback_trigger_status_key.includes(currentNewStatusKey)
        ) {
            // Trigger rule has matched
            // Now generate a key and send SMS
            // console.log('Now generate a key and send SMS');
            try {
                const finalLink =
                    await this.services_model.generateFeedbackLink(
                        query,
                        entry_id
                    );
                if (finalLink) {
                    const shortenedLink = await shortenLink(
                        this.services_model.db,
                        query,
                        finalLink
                    );
                    if (shortenedLink) {
                        // time to notify Customer
                        console.log('Link to send', shortenedLink);
                        let display_code = query['display_code'];
                        let to = query['cust_mobile'];
                        let name = query['cust_full_name'];
                        if (!dont_send_srvc_feedback_sms) {
                            if (
                                select_sms_feedback_template &&
                                select_sms_feedback_template != ''
                            ) {
                                let smsFeedbackTemplateDetails =
                                    getSmsFeedbackTemplateDetails(
                                        select_sms_feedback_template
                                    );
                                if (!smsFeedbackTemplateDetails) {
                                    const brand_org_id =
                                        this.config_data.org_id;
                                    const orgConfigData =
                                        await this.getOrgLevelSettingsData(
                                            brand_org_id
                                        );
                                    lambdaFrSmsCommunication =
                                        orgConfigData?.lambda_fr_sms_communication;
                                    if (
                                        orgConfigData?.custom_sms_feedback_templates
                                    ) {
                                        const customSmsFeedbackTemplates =
                                            orgConfigData?.custom_sms_feedback_templates;
                                        smsFeedbackTemplateDetails =
                                            customSmsFeedbackTemplates?.find(
                                                (item) =>
                                                    item.value ==
                                                    select_sms_feedback_template
                                            );
                                    }
                                }
                                if (smsFeedbackTemplateDetails) {
                                    // get custom fields if any
                                    // let custom_fields_in_template = smsFeedbackTemplateDetails.custom_fields_meta;
                                    let custom_fields_data = {};
                                    // custom_fields_in_template?.map(
                                    //     singleCustomField => {
                                    //         custom_fields_data[`%${singleCustomField.key}%`] = this.config_data[`${singleCustomField.key}`];
                                    //     }
                                    // );

                                    // // console.log("custom_fields_data",custom_fields_data)

                                    // let final_template = parseWhatsappTemplateWithValues(select_sms_feedback_template,custom_fields_data);
                                    try {
                                        const form_data =
                                            await this.services_model.getDataFrNotification(
                                                {},
                                                entry_id
                                            );

                                        let staticDataFields = {
                                            '%feedback_link%': shortenedLink,
                                        };
                                        Object?.keys(form_data).map(
                                            (singleFormDataKey) => {
                                                staticDataFields[
                                                    `%${singleFormDataKey}%`
                                                ] =
                                                    form_data[
                                                        singleFormDataKey
                                                    ];
                                            }
                                        );
                                        // console.log('staticDataFields',staticDataFields);
                                        const final_template =
                                            parseSmsFeedbackTemplateWithValues(
                                                select_sms_feedback_template,
                                                staticDataFields
                                            );
                                        // console.log("final_template",final_template);
                                        //send sms feedback to consumer
                                        let sendSMSdata = {
                                            to: form_data.cust_mobile,
                                            message: final_template,
                                            channel: TRANSACTION_CHANNEL,
                                            user_info:
                                                this.services_model.getServicesModelData(
                                                    this.services_model,
                                                    true
                                                ),
                                            lambdaFrSmsCommunication,
                                            notificationTimeline: {
                                                successMessage:
                                                    'Feedback sms sent to customer',
                                                failedMessage:
                                                    'Feedback sms not sent to customer',
                                                entry_id,
                                                final_template: '',
                                            },
                                        };

                                        allQueues.WIFY_SEND_SMS.addJob(
                                            sendSMSdata
                                        );
                                        // console.log('consumer_notification_sms -> ',final_template);
                                    } catch (error) {
                                        //swallow
                                        console.log(
                                            'services_workflow :: processStatusUpdateWorkflows :: SMS Feedback notification failed',
                                            error
                                        );
                                    }
                                } else {
                                    console.log(
                                        'services_workflow :: processStatusUpdateWorkflows :: SMS Feedback template not found'
                                    );
                                }
                            } else {
                                sendFeedbackSMS(
                                    this.services_model.db,
                                    query,
                                    to,
                                    name,
                                    display_code,
                                    shortenedLink,
                                    this.services_model.getServicesModelData(
                                        this.services_model,
                                        true
                                    ),
                                    {
                                        successMessage:
                                            'Feedback sms sent to customer',
                                        failedMessage:
                                            'Feedback sms not sent to customer',
                                        entry_id,
                                        final_template: '',
                                    }
                                );
                            }
                        }

                        if (srvc_feedback_send_by_whatsapp) {
                            if (
                                select_whatsApp_feedback_template &&
                                select_whatsApp_feedback_template != ''
                            ) {
                                let whatsAppFeedbackTemplateDetails =
                                    getWhatsappFeedbackTemplateDetails(
                                        select_whatsApp_feedback_template
                                    );
                                if (!whatsAppFeedbackTemplateDetails) {
                                    const brand_org_id =
                                        this.config_data.org_id;
                                    const org_config_data =
                                        await this.getOrgLevelSettingsData(
                                            brand_org_id
                                        );
                                    lambdaFrWhatsAppCommunication =
                                        org_config_data?.lambda_fr_whatsapp_communication;
                                    if (
                                        org_config_data?.custom_whatsapp_feedback_templates
                                    ) {
                                        const customwhatsAppFeedbackTemplates =
                                            org_config_data?.custom_whatsapp_feedback_templates;
                                        whatsAppFeedbackTemplateDetails =
                                            customwhatsAppFeedbackTemplates?.find(
                                                (item) =>
                                                    item.value ==
                                                    select_whatsApp_feedback_template
                                            );
                                    }
                                }
                                if (whatsAppFeedbackTemplateDetails) {
                                    // get custom fields if any
                                    // let custom_fields_in_template = whatsAppFeedbackTemplateDetails.custom_fields_meta;
                                    let custom_fields_data = {};
                                    // custom_fields_in_template?.map(
                                    //     singleCustomField => {
                                    //         custom_fields_data[`%${singleCustomField.key}%`] = this.config_data[`${singleCustomField.key}`];
                                    //     }
                                    // );

                                    // // console.log("custom_fields_data",custom_fields_data)

                                    // let final_template = parseWhatsappTemplateWithValues(select_whatsApp_feedback_template,custom_fields_data);
                                    try {
                                        const form_data =
                                            await this.services_model.getDataFrNotification(
                                                {},
                                                entry_id
                                            );

                                        let static_data_fields = {
                                            '%feedback_link%': shortenedLink,
                                        };
                                        Object?.keys(form_data).map(
                                            (singleFormDataKey) => {
                                                static_data_fields[
                                                    `%${singleFormDataKey}%`
                                                ] =
                                                    form_data[
                                                        singleFormDataKey
                                                    ];
                                            }
                                        );
                                        // console.log('static_data_fields',static_data_fields);
                                        let final_template =
                                            parseWhatsappFeedbackTemplateWithValues(
                                                select_whatsApp_feedback_template,
                                                static_data_fields
                                            );
                                        // console.log("final_template",final_template);
                                        let final_variables_fr_api_call =
                                            createDataFrWhatsappAPICall(
                                                static_data_fields,
                                                custom_fields_data,
                                                whatsAppFeedbackTemplateDetails
                                            );
                                        let template_type_key =
                                            whatsAppFeedbackTemplateDetails?.template_name;
                                        //send whatsapp feedback to consumer
                                        let sendwhatsappFeedbackdata = {
                                            to: form_data.cust_mobile,
                                            message: final_template,
                                            api_call_variables:
                                                final_variables_fr_api_call,
                                            url: whatsAppFeedbackTemplateDetails?.url,
                                            token: whatsAppFeedbackTemplateDetails?.token,
                                            channel: TRANSACTION_CHANNEL,
                                            user_info:
                                                this.services_model.getServicesModelData(
                                                    this.services_model,
                                                    true
                                                ),
                                            template_type_key:
                                                template_type_key,
                                            lambdaFrWhatsAppCommunication,
                                        };

                                        allQueues.WIFY_SEND_WHATSAPP_MSG.addJob(
                                            sendwhatsappFeedbackdata
                                        );

                                        //Notification data update on timeline
                                        await this.notificationDataUpdateOnTimeline(
                                            entry_id,
                                            true,
                                            '',
                                            'Feedback whatsapp sent to customer'
                                        );
                                    } catch (error) {
                                        //swallow
                                        console.log(
                                            'Whatsapp Feedback notification failed',
                                            error
                                        );
                                    }
                                } else {
                                    console.log(
                                        'Whatsapp Feedback template not found'
                                    );
                                }
                            } else {
                                sendFeedbackWhatsappMsg(
                                    this.services_model.db,
                                    query,
                                    to,
                                    name,
                                    display_code,
                                    shortenedLink,
                                    this.services_model.getServicesModelData(
                                        this.services_model,
                                        true
                                    )
                                );
                                await this.notificationDataUpdateOnTimeline(
                                    entry_id,
                                    true,
                                    '',
                                    'Feedback whatsapp sent to customer'
                                );
                            }
                        }
                    } else {
                        console.log('Shortened link not generated');
                    }
                } else {
                    console.log('Feedback link not generated');
                }
            } catch (error) {
                console.log('Error in feedback link generation', error);
            }
        }
        //Send sms notification to consumer
        if (currentNewStatusKey) {
            //status call
            await this.processSMSNotificationFrStatusUpdateWorkflow(
                entry_id,
                currentNewStatusKey,
                query
            );
            const closureResp = await this.processSrvcReqClosure({
                entry_id,
                currentNewStatusKey,
            });

            await this.processWhatsappNotificationFrStatusUpdateWorkflow(
                entry_id,
                currentNewStatusKey,
                closureResp
            );
            if (
                automation_srvc_status_enable &&
                automation_srvc_statuses?.includes(currentNewStatusKey)
            ) {
                try {
                    //process srvc req status updation workflow
                    await this.subtaskStatusUpdationWorkFlow(
                        query,
                        entry_id,
                        this.config_data,
                        currentNewStatusKey,
                        'auto_sbtsk_status_movement_based_on_srvc_update_rules',
                        'sbtsk_type_status'
                    );
                } catch (error) {
                    console.log(
                        'Error in subtaskStatusUpdationWorkFlow',
                        error
                    );
                }
            }
            if (
                auto_move_subtask_status_enabled &&
                srvc_type_statuses_fr_automation?.includes(currentNewStatusKey)
            ) {
                try {
                    //process srvc req status updation workflow
                    await this.subtaskStatusUpdationWorkFlow(
                        query,
                        entry_id,
                        this.config_data,
                        currentNewStatusKey,
                        'auto_move_sbtsk_rules',
                        'sbtsk_status'
                    );
                } catch (error) {
                    console.log(
                        'Error in subtaskStatusUpdationWorkFlow',
                        error
                    );
                }
            }
        } else {
            // When a request is newly created
            // currentNewStatusKey will be empty as it is not a status update
            // but we will still need to trigger notifications
            // for whatever the initial status of the newly created request is
            // so we get the newly created entry back from db
            // and use the status in it for our notification trigger
            try {
                const operationResp = await this.services_model.getSingleEntry(
                    query,
                    entry_id
                );
                if (operationResp.isSuccess()) {
                    let form_data = JSON.parse(operationResp.resp).form_data;
                    let newStatusFrmDb = form_data.status.key;
                    await this.processSMSNotificationFrStatusUpdateWorkflow(
                        entry_id,
                        newStatusFrmDb,
                        query
                    );
                    await this.processWhatsappNotificationFrStatusUpdateWorkflow(
                        entry_id,
                        newStatusFrmDb
                    );
                } else {
                    console.log(
                        'Send sms/whatsapp notification for consumer failed'
                    );
                }
            } catch (error) {
                console.log(
                    'Send sms/whatsapp notification for consumer failed',
                    error
                );
            }
        }

        // calling lambda for the org which have enabled status update lambda
        if (enable_lambda_for_status_update && currentNewStatusKey) {
            try {
                // extrating tms_order_id & adding to query to call lambda
                const operationResp = await this.services_model.getSingleEntry(
                    query,
                    entry_id
                );
                if (operationResp.isSuccess()) {
                    let form_data = JSON.parse(operationResp.resp).form_data;
                    query.pre_srvc_req_form_data.form_data.display_titles = [
                        form_data?.title,
                    ];
                    query.display_titles = [form_data?.title];
                    this.services_model.callLambdaFnForStatusUpdateTrigger(
                        query
                    );
                } else {
                    console.log('Failed to get single entry details by id ');
                }
            } catch (error) {
                console.log(
                    'Failed to get single entry details by id :: error :: ',
                    error
                );
            }
        }

        //Send email for consumer
        // if email is enabled
        // let email_prefix = `cnsmr_ntfctn_fr_status_${currentNewStatusKey}_email_enabled`;
        // let is_cnsmr_ntfctn_email_enabled = this.config_data[email_prefix];
        // if(is_cnsmr_ntfctn_email_enabled){
        // }

        //process service type notify status update workflow (for email)
        let { srvc_type_id, new_status } = query;
        let dummyQuery = {};
        dummyQuery['srvc_type_id'] = srvc_type_id;
        dummyQuery['status_key'] = currentNewStatusKey;
        dummyQuery['srvc_type_id'] = query.srvc_type_id;
        await this.srvcStatusNotifyWorkFlow(
            dummyQuery,
            entry_id,
            this.config_data
        );
    }

    async processSMSNotificationFrStatusUpdateWorkflow(
        entry_id,
        currentNewStatusKey,
        query
    ) {
        if (currentNewStatusKey == undefined) {
            return;
        }

        console.log(
            'processSMSNotificationFrStatusUpdateWorkflow -->',
            entry_id
        );
        let sms_prefix = `cnsmr_ntfctn_fr_status_${currentNewStatusKey}_sms`;

        let lambdaFrSmsCommunication;
        // Check if notification is enabled for new status using this.config_data
        let is_cnsmr_ntfctn_sms_enabled =
            this.config_data[`${sms_prefix}_enabled`];
        if (is_cnsmr_ntfctn_sms_enabled) {
            let sms_status_type = this.config_data[`${sms_prefix}_status_type`];

            if (sms_status_type && sms_status_type != '') {
                let sms_template = this.config_data[`${sms_prefix}_template`];

                if (sms_template && sms_template != '') {
                    // get selected template
                    let sms_template_details = getSMSTemplateDetails(
                        sms_status_type,
                        sms_template
                    );
                    if (!sms_template_details) {
                        const brand_org_id = this.config_data.org_id;
                        const org_config_data =
                            await this.getOrgLevelSettingsData(brand_org_id);
                        lambdaFrSmsCommunication =
                            org_config_data?.lambda_fr_sms_communication;
                        if (org_config_data.custom_sms_templates) {
                            const customStatusWiseTemplates =
                                org_config_data.custom_sms_templates?.find(
                                    (item) => item.value == sms_status_type
                                )?.templates;
                            sms_template_details =
                                customStatusWiseTemplates?.find(
                                    (item) => item.value == sms_template
                                );
                        }
                        // console.log("org_config_data",org_config_data);
                    }
                    if (sms_template_details) {
                        // get custom fields if any
                        let custom_fields_in_template =
                            sms_template_details.custom_fields_meta;
                        let custom_fields_data = {};
                        custom_fields_in_template?.map((singleCustomField) => {
                            custom_fields_data[`%${singleCustomField.key}%`] =
                                this.config_data[
                                    `${sms_prefix}_${singleCustomField.key}`
                                ];
                        });
                        let final_template = parseSMSTemplateWithValues(
                            sms_template,
                            custom_fields_data
                        );
                        try {
                            const form_data =
                                await this.services_model.getDataFrNotification(
                                    {},
                                    entry_id
                                );

                            let static_data_fields = {};
                            Object?.keys(form_data).map((singleFormDataKey) => {
                                static_data_fields[`%${singleFormDataKey}%`] =
                                    form_data[singleFormDataKey];
                            });

                            if (
                                sms_template_details.value.includes(
                                    'masked_number_pin'
                                ) &&
                                sms_template_details.value.includes(
                                    'technician_masked_number'
                                )
                            ) {
                                if (query.is_sbtsk_reassignment) {
                                    // skipping this sms call because on reassignment it is getting call
                                    // and sms getting triggered twice
                                    console.log(
                                        'calling this method from sbtsk reassingment'
                                    );
                                    return;
                                }
                                let maskedNumberExists = false;
                                try {
                                    if (form_data?.sbtsk_id > 0) {
                                        let queryFrValidateMaskedPin = {
                                            sbtsk_db_id: form_data?.sbtsk_id,
                                        };
                                        const masked_number_data =
                                            await this.services_model.validateMaskedNumberAndPin(
                                                queryFrValidateMaskedPin,
                                                entry_id
                                            );
                                        console.log(
                                            'masked_number_data',
                                            masked_number_data
                                        );
                                        if (masked_number_data) {
                                            Object?.keys(
                                                masked_number_data
                                            ).map((singleFormDataKey) => {
                                                static_data_fields[
                                                    `%${singleFormDataKey}%`
                                                ] =
                                                    masked_number_data[
                                                        singleFormDataKey
                                                    ];
                                            });
                                            maskedNumberExists = true;
                                        }
                                    }
                                } catch (error) {
                                    console.log(
                                        'validateMaskedNumberAndPin error',
                                        error
                                    );
                                }
                                if (!maskedNumberExists) {
                                    console.log(
                                        ' Consumer Number/Technician masked Number/Subtask not found.',
                                        form_data
                                    );
                                    await this.notificationDataUpdateOnTimeline(
                                        entry_id,
                                        true,
                                        ' Consumer Number/Technician masked Number/Subtask not found.',
                                        'Failed to send SMS'
                                    );
                                    return;
                                }
                            }
                            // console.log('static_data_fields1',static_data_fields);
                            final_template = parseSMSTemplateWithValues(
                                final_template,
                                static_data_fields
                            );

                            //check if final_template wich technician_name and scheduled_date exists
                            let technicianNameOrScheduleDateIsUndefined =
                                final_template.includes('%technician_name%') ||
                                final_template.includes('%scheduled_date%') ||
                                final_template.includes('%assignee_number%');

                            if (!technicianNameOrScheduleDateIsUndefined) {
                                //send sms to consumer
                                let sendSMSdata = {
                                    to: form_data.cust_mobile,
                                    message: final_template,
                                    channel: TRANSACTION_CHANNEL,
                                    user_info:
                                        this.services_model.getServicesModelData(
                                            this.services_model,
                                            true
                                        ),
                                    lambdaFrSmsCommunication,
                                    notificationTimeline: {
                                        successMessage: 'SMS sent to customer',
                                        failedMessage:
                                            'SMS not sent to customer',
                                        entry_id,
                                        final_template,
                                    },
                                };

                                allQueues.WIFY_SEND_SMS.addJob(sendSMSdata);
                                // console.log('consumer_notification_sms -> ',final_template);
                            } else {
                                //Notification data update on timeline
                                await this.notificationDataUpdateOnTimeline(
                                    entry_id,
                                    true,
                                    'Customer not notified as no subtask was created.',
                                    'SMS sending failed to customer'
                                );
                            }
                        } catch (error) {
                            //swallow
                            console.log('SMS notification failed', error);
                        }
                    }
                }
            }
        }
    }

    /**
     * Processes a service request closure by invoking a Lambda hook if enabled.
     *
     * @param {Object} params - Parameters for processing service request closure.
     * @param {string} params.entry_id - The ID of the service request entry.
     * @param {string} params.currentNewStatusKey - The new status key to check if closure is triggered.
     */
    async processSrvcReqClosure({ entry_id, currentNewStatusKey }) {
        if (currentNewStatusKey != 'closed') {
            return;
        }
        try {
            let {
                enable_lambda_hook_fr_srvc_req_closure,
                lambda_arn_fr_srvc_req_closure,
            } = this.config_data;

            const srvcReqDetails = await this.services_model.getSingleEntry(
                { srvc_type_id: this.srvc_type_id },
                entry_id
            );

            const srvcReqClosedSubtasksDetails =
                await this.services_model.getSrvcReqClosedSubtasksEntrys(
                    {
                        srvc_type_id: this.srvc_type_id,
                        org_id: this.config_data.org_id, //brand org_id
                    },
                    entry_id
                );

            if (!enable_lambda_hook_fr_srvc_req_closure) {
                return;
            }
            const lambdaARN = lambda_arn_fr_srvc_req_closure;
            const payload = {
                srvcReqDetails: JSON.parse(srvcReqDetails.resp).form_data,
                srvc_config_data: this.config_data,
                srvcReqClosedSubtasksDetails: JSON.parse(
                    srvcReqClosedSubtasksDetails.resp
                ),
            };
            const params = {
                FunctionName: lambdaARN,
                InvocationType: 'RequestResponse',
                LogType: 'Tail',
                Payload: JSON.stringify({ ...payload }),
            };

            let respData = await callLambdaFn(params);
            let lambdaRespData = JSON.parse(respData.Payload);

            if (lambdaRespData && lambdaRespData.data) {
                let {
                    createWarrantyCert,
                    hasClosedSubtsksFrWarrantyCert,
                    generatePDFResp,
                    modifiedSrvcReqFormData,
                } = lambdaRespData.data;
                if (createWarrantyCert && generatePDFResp?.s3Key) {
                    let queryFrSrvcReq = {
                        srvc_type_id: this.srvc_type_id,
                        ...modifiedSrvcReqFormData,
                    };
                    await this.services_model.createOrUpdate(
                        queryFrSrvcReq,
                        entry_id
                    );
                }
                return {
                    createWarrantyCert,
                    hasClosedSubtsksFrWarrantyCert,
                    s3Key: generatePDFResp?.s3Key,
                    wrntCertUrl: generatePDFResp?.pdfUrl,
                };
            } else {
                console.log(lambdaRespData.message);
            }
        } catch (error) {
            console.log('processSrvcReqClosure failed', error);
        }
    }

    async processWhatsappNotificationFrStatusUpdateWorkflow(
        entry_id,
        currentNewStatusKey,
        closureResp
    ) {
        let {
            enable_lambda_hook_fr_srvc_req_closure,
            lambda_arn_fr_srvc_req_closure,
        } = this.config_data;
        if (currentNewStatusKey == undefined) {
            return;
        }
        if (
            currentNewStatusKey == 'closed' &&
            enable_lambda_hook_fr_srvc_req_closure &&
            lambda_arn_fr_srvc_req_closure &&
            closureResp &&
            (!closureResp?.createWarrantyCert ||
                !closureResp?.hasClosedSubtsksFrWarrantyCert)
        ) {
            return;
        }
        let whatsapp_prefix = `cnsmr_ntfctn_fr_status_${currentNewStatusKey}_whatsapp_message`;
        let lambdaFrWhatsAppCommunication;
        // Check if notification is enabled for new status using this.config_data
        let is_cnsmr_ntfctn_sms_enabled =
            this.config_data[`${whatsapp_prefix}_enabled`];
        if (is_cnsmr_ntfctn_sms_enabled) {
            let whatsapp_status_type =
                this.config_data[`${whatsapp_prefix}_status_type`];

            // console.log("whatsapp_status_type",whatsapp_status_type)

            if (whatsapp_status_type && whatsapp_status_type != '') {
                let whatsapp_template =
                    this.config_data[`${whatsapp_prefix}_template`];

                // console.log("whatsapp_template",whatsapp_template)

                if (whatsapp_template && whatsapp_template != '') {
                    // get selected template
                    let whatsapp_template_details = getWhatsappTemplateDetails(
                        whatsapp_status_type,
                        whatsapp_template
                    );
                    if (!whatsapp_template_details) {
                        const brand_org_id = this.config_data.org_id;
                        const org_config_data =
                            await this.getOrgLevelSettingsData(brand_org_id);
                        lambdaFrWhatsAppCommunication =
                            org_config_data?.lambda_fr_whatsapp_communication;
                        if (org_config_data.custom_whatsapp_templates) {
                            const customStatusWiseTemplates =
                                org_config_data.custom_whatsapp_templates?.find(
                                    (item) => item.value == whatsapp_status_type
                                )?.templates;
                            whatsapp_template_details =
                                customStatusWiseTemplates?.find(
                                    (item) => item.value == whatsapp_template
                                );
                        }
                        // console.log("org_config_data",org_config_data);
                    }
                    // console.log("whatsapp_template_details",whatsapp_template_details)

                    if (whatsapp_template_details) {
                        // get custom fields if any
                        let custom_fields_in_template =
                            whatsapp_template_details.custom_fields_meta;
                        let custom_fields_data = {};
                        custom_fields_in_template?.map((singleCustomField) => {
                            custom_fields_data[`%${singleCustomField.key}%`] =
                                this.config_data[
                                    `${whatsapp_prefix}_${singleCustomField.key}`
                                ];
                        });

                        // console.log("custom_fields_data",custom_fields_data)

                        let final_template = parseWhatsappTemplateWithValues(
                            whatsapp_template,
                            custom_fields_data
                        );
                        try {
                            const form_data =
                                await this.services_model.getDataFrNotification(
                                    {},
                                    entry_id
                                );

                            if (closureResp?.wrntCertUrl) {
                                form_data['link'] = closureResp?.wrntCertUrl;
                            }
                            let static_data_fields = {};
                            Object?.keys(form_data).map((singleFormDataKey) => {
                                static_data_fields[`%${singleFormDataKey}%`] =
                                    form_data[singleFormDataKey];
                            });
                            // console.log('static_data_fields',static_data_fields);
                            final_template = parseWhatsappTemplateWithValues(
                                final_template,
                                static_data_fields
                            );

                            //check if final_template wich technician_name and scheduled_date exists
                            let technicianNameOrScheduleDateIsUndefined =
                                final_template.includes('%technician_name%') ||
                                final_template.includes('%scheduled_date%') ||
                                final_template.includes('%assignee_number%');

                            let final_variables_fr_api_call =
                                createDataFrWhatsappAPICall(
                                    static_data_fields,
                                    custom_fields_data,
                                    whatsapp_template_details
                                );

                            if (!technicianNameOrScheduleDateIsUndefined) {
                                let template_type_key =
                                    whatsapp_template_details?.template_name;
                                //send whatsapp to consumer
                                let sendwhatsappdata = {
                                    to: form_data.cust_mobile,
                                    message: final_template,
                                    api_call_variables:
                                        final_variables_fr_api_call,
                                    url: whatsapp_template_details?.url,
                                    token: whatsapp_template_details?.token,
                                    channel: TRANSACTION_CHANNEL,
                                    user_info:
                                        this.services_model.getServicesModelData(
                                            this.services_model,
                                            true
                                        ),
                                    template_type_key: template_type_key,
                                    lambdaFrWhatsAppCommunication,
                                };

                                allQueues.WIFY_SEND_WHATSAPP_MSG.addJob(
                                    sendwhatsappdata
                                );
                                // console.log('consumer_notification_whatsapp -> ',final_template);
                                // console.log("xyz ------->>>>",xyz)

                                //Notification data update on timeline
                                await this.notificationDataUpdateOnTimeline(
                                    entry_id,
                                    true,
                                    final_template,
                                    'Whatsapp notification sent to customer'
                                );
                            } else {
                                //Notification data update on timeline
                                await this.notificationDataUpdateOnTimeline(
                                    entry_id,
                                    true,
                                    'Customer not notified as no subtask was created.',
                                    'Whatsapp notification sending failed to customer'
                                );
                            }
                        } catch (error) {
                            //swallow
                            console.log('Whatsapp notification failed', error);
                        }
                    }
                }
            }
        }
    }

    async updateSrvcReqCityStateByPincode(query, new_entry_id) {
        let cityStateData;
        let queryFrSearcher = {
            fn: 'getPincode',
            query: query.cust_pincode,
        };
        searcher_model = getSearcherModelFrQueue(this.services_model);
        try {
            cityStateData =
                await searcher_model.getSearchResults(queryFrSearcher);
            const { resp } = cityStateData;
            cityStateData = JSON.parse(resp);
        } catch (error) {
            console.log(error);
            return;
        }
        if (cityStateData.length > 0) {
            let newQuery = {
                cust_city: cityStateData[0]?.city,
                cust_state: cityStateData[0]?.state,
                isUpdateCityState: true,
            };
            try {
                let resp = await this.services_model.createOrUpdate(
                    newQuery,
                    new_entry_id,
                    query.is_customer_access
                );
                console.log(
                    'update city state with pincode for bulkCreateorApiCall',
                    resp
                );
            } catch (error) {
                console.log(error);
                return;
            }
        }
    }

    async processSrvcReqCreationWorkFlows(
        query,
        new_entry_id,
        is_customer_access = 0,
        cust_org_id = 0,
        db = undefined,
        isBulkCreateorApiCall = false
    ) {
        try {
            await this.getConfigDataFrSrvcType(query);
            console.log('Loaded config data..');
        } catch (error) {
            console.log(
                'processSrvcReqCreationWorkFlows getConfigDataFrSrvcType error',
                error
            );
            return;
        }

        if (isBulkCreateorApiCall && query?.cust_pincode) {
            await this.updateSrvcReqCityStateByPincode(query, new_entry_id);
        }

        //Assigned service provider done by db

        //auto movement based on authority being added
        if (checkQueryFrAuthorityModification(query)) {
            await this.autoMovementStatusBasedOnAuthorities(
                query,
                new_entry_id
            );
            try {
                await this.processRatingsQueue(query, new_entry_id);
            } catch (e) {
                console.log('processRatingsQueue failed ', e);
            }
        }

        try {
            await this.services_model.syncSrvcReqLocWithCache(new_entry_id);
        } catch (rejectedResp) {
            // start a bull job
            // to hit Google Api and then internally it will resync cache
            // console.log("service syncSrvcReqLocWithCache in rejectedResp..",rejectedResp);
            let jobData = {
                form_data: rejectedResp.form_data,
                query,
                new_entry_id,
                services_model_data: this.services_model.getServicesModelData(
                    this.services_model
                ),
            };
            allQueues.WIFY_SRVC_REQ_LOC_MAPPING.addJob(jobData);
        }
        let updatedAddressIfAny = concatAddress(query);
        if (updatedAddressIfAny) {
            console.log(
                'processSrvcReqCreationWorkFlows updatedAddressIfAny',
                new_entry_id,
                updatedAddressIfAny,
                query
            );
            await this.processUpdateLocGrpIdsInSrvcReqs(new_entry_id);
            try {
                await this.processUpdateRatedByColumFrRatingsTableQueue(
                    { ...query, locationUpdated: true },
                    entry_id
                );
            } catch (e) {
                console.log(
                    'processUpdateRatedByColumFrRatingsTableQueue failed ',
                    e
                );
            }
            await this.autoAuthorityAssignedBasedOnLocation(
                query,
                new_entry_id
            );
            console.log(
                'autoAuthorityAssignedBasedOnLocation update updatedAddressIfAny went well'
            );
        } else {
            console.log(
                'autoAuthorityAssignedBasedOnLocation update updatedAddressIfAny failed',
                new_entry_id,
                query
            );
        }
        await this.processStatusUpdateWorkflows(query, new_entry_id);
    }

    async processRatingsQueue(query, entry_id) {
        console.log('processRatingsQueue Start', entry_id);
        for (const key in query) {
            if (key.includes('authority')) {
                try {
                    const role_id = parseInt(key.split('_')[1]);
                    let form_data = { ...query };
                    form_data['role_id'] = role_id;
                    form_data['selected_user_fr_rating'] = query[key];
                    form_data['srvc_req_id'] = entry_id;
                    form_data['rating_type'] = 'authority';
                    let jobData = { form_data, entry_id };
                    jobData['services_model_data'] =
                        this.services_model.getServicesModelData(
                            this.services_model
                        );
                    allQueues.WIFY_PROCESS_RATINGS_QUEUE.addJob(jobData);
                } catch (e) {
                    console.log(`processRatingsQueue failed for ${key} `, e);
                }
            }
        }
        await this.processUpdateRatedByColumFrRatingsTableQueue(
            query,
            entry_id
        );
        console.log('processRatingsQueue end', entry_id);
    }

    async processUpdateRatedByColumFrRatingsTableQueue(query, entry_id) {
        let form_data = {
            srvc_req_id: entry_id,
            updateOnlyRatedByClm: true,
            ...query,
        };
        let jobData = {
            form_data: form_data,
            services_model_data: this.services_model.getServicesModelData(
                this.services_model
            ),
        };
        try {
            allQueues.WIFY_PROCESS_RATINGS_QUEUE.addJob(jobData);
        } catch (e) {
            console.log('process ratings queue failed ', e);
        }
    }

    async triggerSubtaskDeletionNotificationWorkFlow(data, job_id) {
        console.log(
            '**** process triggerSubtaskDeletionNotificationWorkFlow start **** ',
            job_id
        );
        const entry_id_vs_query_fr_deletion =
            data.entry_id_vs_query_fr_deletion;
        if (entry_id_vs_query_fr_deletion) {
            Object.keys(entry_id_vs_query_fr_deletion).map(
                (single_entry_id) => {
                    const query =
                        entry_id_vs_query_fr_deletion[single_entry_id];
                    let jobData = { ...query, sbtsk_entry_id: single_entry_id };
                    jobData['subtasks_model_data'] = subtasks_model
                        .getFreshInstance(subtasks_model)
                        .getSbtskModelData(this.services_model);
                    allQueues.WIFY_TMS_SUBTASK_DELETION_NOTIFICATION.addJob(
                        jobData
                    );
                }
            );
        }
        console.log(
            '**** process triggerSubtaskDeletionNotificationWorkFlow end **** ',
            job_id
        );
    }

    async processSrvcReqDeletionNotificationWorkFlow(
        query,
        entry_id,
        dbResp,
        db,
        job_id = 0
    ) {
        console.log(
            '**** processSrvcReqNotificationWorkFlow start ****',
            job_id
        );
        try {
            let { srvc_type_id, srvc_req_id } = query;

            //make dummy query
            let dummyQuery = { ...dbResp.data };

            const { entry_id_vs_query_fr_deletion } = dummyQuery;
            // console.log("processSrvcReqNotificationWorkFlow",dummyQuery);
            // console.log("processSrvcReqNotificationWorkFlow entry_id_vs_query_fr_deletion",entry_id_vs_query_fr_deletion);

            if (entry_id_vs_query_fr_deletion) {
                //process process sbtsk deletion notify workflow
                await this.triggerSubtaskDeletionNotificationWorkFlow(
                    dummyQuery,
                    job_id
                );
            }
        } catch (error) {
            console.log('processSrvcReqNotificationWorkFlow failed', error);
        }
    }

    async getConfigDataFrSrvcType(query) {
        const operationResp =
            await this.services_model.getSrvcTypeConfigData(query);
        if (operationResp.isSuccess()) {
            this.config_data = JSON.parse(operationResp.resp)?.config_data;
            if (this.config_data) {
                return;
            }
        }
        console.log('getConfigDataFrSrvcType error', query);
        throw 'Config data not available';
    }

    /**
     * Fetches organization-level settings data for the given brand organization ID.
     * If no brandOrgId is provided, it defaults to the current organization's ID.
     *
     * @param {string} [brandOrgId] - The optional brand organization ID for which to fetch the settings. If not provided, the current organization ID will be used.
     *
     * @returns {Promise<Object>} - A promise that resolves to the parsed organization configuration data (from the response), or throws an error if the data is unavailable.
     *
     * @throws {string} - Throws an error if the operation response is not successful or if the organization settings data is not available.
     *
     * @example
     * const settingsData = await getOrgLevelSettingsData('someBrandOrgId');
     *
     * @description
     * This method creates a fresh instance of the `organisations_model` and uses it to fetch the settings of the specified
     * organization ID. The organization ID used is either the one provided (`brandOrgId`) or the current organization's ID.
     * If the settings retrieval is successful, the response is parsed and returned as the organization configuration data.
     * If the request fails or data is not available, an error is logged and a custom error message is thrown.
     */
    async getOrgLevelSettingsData(brandOrgId) {
        try {
            let organisations_model = require('../organisations_model');
            let orgId = this.getOrgID();
            organisations_model = organisations_model.getFreshInstance(
                this.services_model
            );
            if (brandOrgId) {
                orgId = orgId == brandOrgId ? orgId : brandOrgId;
            }
            const operationResp =
                await organisations_model.getSingleEntry(orgId);
            // console.log("operationResp",operationResp);
            if (operationResp.isSuccess()) {
                let orgConfigData = JSON.parse(operationResp.resp);
                return orgConfigData;
            }
            // console.log('getOrgLevelSettingsData error');
            // throw 'Org_level_setting data not available';
        } catch (error) {
            console.log('getOrgLevelSettingsData :: error ::', error);
            throw 'Org_level_setting data not available';
        }
    }

    async notificationDataUpdateOnTimeline(
        entry_id,
        notification_data,
        final_template,
        title
    ) {
        let notification_query = {};
        notification_query['notification_data'] = notification_data;
        notification_query['comment'] = final_template;
        notification_query['title'] = title;
        await this.services_model.createOrUpdate(notification_query, entry_id);
    }

    async srvcStatusNotifyWorkFlow(dummyQuery, entry_id, config_data) {
        let { status_key, srvc_type_id } = dummyQuery;
        let isSrvcReqAlreadyCreated = status_key ? true : false;
        let srvc_type_status_notify_authorities = `srvc_type_status_${status_key ? status_key : 'open'}_notify_authorties`;
        let notify_authorities_usr_roles =
            config_data[srvc_type_status_notify_authorities];
        if (
            notify_authorities_usr_roles == undefined ||
            !(notify_authorities_usr_roles.length > 0)
        ) {
            return;
        }
        let srvc_req_id = entry_id;
        this.initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id);
        const srvcReqOperationResp = await this.services_model.getSingleEntry(
            {},
            srvc_req_id
        );
        if (!srvcReqOperationResp.isSuccess()) {
            return;
        }
        let pre_srvc_req_form_data = JSON.parse(
            srvcReqOperationResp.resp
        )?.form_data;
        let srvc_req_form_data = pre_srvc_req_form_data?.form_data;

        //get srvc_authorities from srvc_type config_data
        const srvc_type_config_data_resp =
            await this.services_model.getSrvcTypeConfigData({}, 0);
        if (!srvc_type_config_data_resp.isSuccess()) {
            return;
        }
        let srvc_authorities = JSON.parse(srvc_type_config_data_resp.resp)
            ?.config_data?.srvc_authorities;

        //get org_details for notify
        const org_details = await this.services_model.getOrgDetails(
            srvc_req_form_data?.org_id
        );
        if (org_details == 'Failed') {
            return;
        }
        let status_transitions = pre_srvc_req_form_data?.status_transitions;
        const host = process.env.FRONTEND_URL || 'tms.wify.co.in';
        let notify_data = {
            title: pre_srvc_req_form_data.title,
            status_transitions:
                status_transitions[status_transitions.length - 1],
            srvc_type_title: pre_srvc_req_form_data.srvc_type_title,
            current_status: pre_srvc_req_form_data.status,
            org_details,
            targetLink: `https://${host}/services/${srvc_type_id}?query=${pre_srvc_req_form_data.title}`,
        };

        //setting params to user_model with db instances
        setParamsToUserModel(user_model, this, this.services_model.db);
        let emailSuccessOrFailureDataFrTimeline = {};

        for (const single_notify_authorties_usr_roles of notify_authorities_usr_roles) {
            if (
                !srvc_authorities?.includes(single_notify_authorties_usr_roles)
            ) {
                continue;
            }

            let srvc_authority_key = `authority_${single_notify_authorties_usr_roles}`;
            let authorities_user_id = srvc_req_form_data[srvc_authority_key];

            if (authorities_user_id) {
                let authoritiesUserResp =
                    await user_model.getUserDetails(authorities_user_id);
                if (authoritiesUserResp.isSuccess()) {
                    let authoritiesUserDetails = JSON.parse(
                        authoritiesUserResp.resp
                    );
                    let emailQuery = {};

                    notify_data['from_name'] =
                        authoritiesUserDetails?.user_name;

                    let sendEmailData = {
                        to: authoritiesUserDetails?.user_email,
                        subject: this.getSubjectFrSrvcStatusNotifyWorkFlow(
                            isSrvcReqAlreadyCreated,
                            notify_data
                        ),
                        message: srvcStatusUpdateNotificationEmailTemplate(
                            isSrvcReqAlreadyCreated,
                            notify_data
                        ),
                        cc: '',
                        org_id: this.services_model?.org_id,
                        usr_id: this.services_model?.usr_id,
                        ip_address: this.services_model?.ip_address,
                        user_agent: this.services_model?.user_agent,
                    };

                    emailQuery[srvc_authority_key] =
                        authoritiesUserDetails?.form_data?.user_name;

                    try {
                        allQueues.WIFY_SEND_EMAIL.addJob(sendEmailData);
                        emailSuccessOrFailureDataFrTimeline['success'] =
                            emailSuccessOrFailureDataFrTimeline['success']
                                ? {
                                      ...emailSuccessOrFailureDataFrTimeline[
                                          'success'
                                      ],
                                      ...emailQuery,
                                  }
                                : emailQuery;
                    } catch (error) {
                        emailSuccessOrFailureDataFrTimeline['failure'] =
                            emailSuccessOrFailureDataFrTimeline['failure']
                                ? {
                                      ...emailSuccessOrFailureDataFrTimeline[
                                          'failure'
                                      ],
                                      ...emailQuery,
                                  }
                                : emailQuery;
                    }
                }
            }
        }

        for (const singleEmailSuccessOrFailureDataFrTimeline of Object.keys(
            emailSuccessOrFailureDataFrTimeline
        )) {
            if (
                emailSuccessOrFailureDataFrTimeline[
                    singleEmailSuccessOrFailureDataFrTimeline
                ]
            ) {
                // console.log("inside if emailSuccessOrFailureDataFrTimeline", emailSuccessOrFailureDataFrTimeline);
                let srvcTypeId = this.srvc_type_id;
                let srvcReqId = this.srvc_req_id;
                let timelineMsg =
                    singleEmailSuccessOrFailureDataFrTimeline == 'success'
                        ? 'Notified authority via email'
                        : 'Failed to notify authority via email';
                let formData =
                    emailSuccessOrFailureDataFrTimeline[
                        singleEmailSuccessOrFailureDataFrTimeline
                    ];

                this.services_model.createTimelineforSrvcReq(
                    formData,
                    srvcTypeId,
                    srvcReqId,
                    'UPDATE',
                    timelineMsg
                );
            }
        }
    }

    async processUpdateRatedByOnLocationGroupRefresh(entry_id, query) {
        let srvcReqRespData = {};
        try {
            srvcReqRespData = await this.getReqDataById(entry_id);
            console.log('Loaded config data..');
        } catch (error) {
            console.log(error);
            return;
        }
        query['srvc_type_id'] = srvcReqRespData?.srvc_type_id;
        try {
            await this.processUpdateRatedByColumFrRatingsTableQueue(
                { ...query, locationUpdated: true },
                entry_id
            );
        } catch (e) {
            console.log(
                'processUpdateRatedByColumFrRatingsTableQueue failed ',
                e
            );
        }
    }

    async subtaskStatusUpdationWorkFlow(
        dummyQuery,
        entry_id,
        config_data,
        currentNewStatusKey,
        autoSbtskStatusMovementKey,
        subtaskNewStatusKey
    ) {
        if (currentNewStatusKey == undefined || currentNewStatusKey == '') {
            currentNewStatusKey = 'open';
        }
        const operationResp =
            await this.services_model.getSubtaskDetailsForAutoStatus(
                dummyQuery,
                entry_id
            );
        const subtasks_model = getParamsToSubtaskModel(
            this.services_model,
            this.services_model.db
        );

        if (operationResp.isSuccess()) {
            let allSbtskTypesFrAutoStatusData = JSON.parse(operationResp.resp);
            for (const singleSbtsktypeFrAutoStatusData of allSbtskTypesFrAutoStatusData) {
                console.log(
                    `services_workflow :: singleSbtsktypeFrAutoStatusData :: ${singleSbtsktypeFrAutoStatusData.sbtsk_type_id}`
                );
                const sbtskTypeId =
                    singleSbtsktypeFrAutoStatusData.sbtsk_type_id;
                const sbtskConfigData =
                    singleSbtsktypeFrAutoStatusData.sbtsk_config_data;
                const sbtskOrgId = singleSbtsktypeFrAutoStatusData.sbtsk_org_id;
                const sbtskStatuses = JSON.parse(
                    sbtskConfigData.sbtsk_statuses
                );

                // Create key mapping using a normal for loop
                const sbtskStatusesKeyMapping = {};

                for (const statusCategory in sbtskStatuses) {
                    if (sbtskStatuses.hasOwnProperty(statusCategory)) {
                        const statuses = sbtskStatuses[statusCategory];
                        for (let i = 0; i < statuses.length; i++) {
                            sbtskStatusesKeyMapping[statuses[i].key] =
                                statuses[i].title;
                        }
                    }
                }

                const autoSbtskStatusKey =
                    sbtskTypeId + '_' + currentNewStatusKey;

                const autoSubtaskStatusConfigData =
                    config_data?.[autoSbtskStatusMovementKey]?.[
                        autoSbtskStatusKey
                    ];

                const subtaskNewStatus =
                    autoSubtaskStatusConfigData?.[subtaskNewStatusKey];

                if (autoSubtaskStatusConfigData && subtaskNewStatus) {
                    for (const singleSbtskData of singleSbtsktypeFrAutoStatusData.sbtsks_details) {
                        console.log(
                            `services_workflow :: singleSbtsktypeFrAutoStatusData :: Subtask ID: ${singleSbtskData.sbtsk_id}`
                        );
                        console.log(
                            `services_workflow :: singleSbtsktypeFrAutoStatusData :: Service Request ID: ${singleSbtskData.srvc_req_id}`
                        );
                        console.log(
                            `services_workflow :: singleSbtsktypeFrAutoStatusData :: Service Type ID: ${singleSbtskData.srvc_type_id}`
                        );
                        console.log(
                            `services_workflow :: singleSbtsktypeFrAutoStatusData :: Current Status: ${singleSbtskData.current_status}`
                        );
                        const sbtskId = singleSbtskData.sbtsk_id;
                        const srvcReqId = singleSbtskData.srvc_req_id;
                        const srvcTypeId = singleSbtskData.srvc_type_id;
                        const subtaskCurrentStatus =
                            singleSbtskData.current_status;
                        //*** Mandatory status validation start ****
                        const statusDependencyMap =
                            sbtskConfigData?.mandatory_status;
                        const requiredPreviousStatus =
                            statusDependencyMap?.[subtaskNewStatus];
                        const subtaskStatusUpdateTypeData =
                            singleSbtskData?.status_update_type_data;
                        const hasRequiredStatusBeenUpdated =
                            Array.isArray(requiredPreviousStatus) &&
                            requiredPreviousStatus.every(
                                (status) =>
                                    subtaskStatusUpdateTypeData?.[status]
                            );

                        if (
                            requiredPreviousStatus?.length > 0 &&
                            !hasRequiredStatusBeenUpdated
                        ) {
                            let srvcReqTimelineTitle = `Task update - Failed to move subtask from ${sbtskStatusesKeyMapping?.[subtaskCurrentStatus]} to ${sbtskStatusesKeyMapping?.[subtaskNewStatus]}`;
                            let subtaskTimelineTitle = `Failed to move from ${sbtskStatusesKeyMapping?.[subtaskCurrentStatus]} to ${sbtskStatusesKeyMapping?.[subtaskNewStatus]}`;
                            await this.services_model.createTimelineforSrvcReq(
                                {
                                    ...dummyQuery,
                                    comment: 'Previous status not updated',
                                },
                                srvcTypeId,
                                entry_id,
                                'UPDATE',
                                srvcReqTimelineTitle
                            );
                            await subtasks_model.createTimelineforSubtask(
                                {
                                    ...dummyQuery,
                                    comment: 'Previous status not updated',
                                },
                                sbtskTypeId,
                                sbtskId,
                                'UPDATE',
                                subtaskTimelineTitle
                            );
                            continue;
                        }
                        //*** Mandatory status validation End ****

                        //*** Status wise custom fields required validation start ****
                        const sbtskStatusFieldKey = `sbtsk_status_${subtaskNewStatus}_fields`;
                        const sbtskStatusFields = JSON.parse(
                            sbtskConfigData?.[sbtskStatusFieldKey] || '{}'
                        );
                        const isAttachmentsFieldMandatoryKey = `make_attachments_field_mandatory_fr_${subtaskNewStatus}`;
                        const isAttachmentsFieldMandatory =
                            sbtskConfigData?.[isAttachmentsFieldMandatoryKey];

                        let isRequiredFieldsMissing = false;
                        let keyMapping = {
                            WIFY_CAMERA: 'camera_files',
                            WIFY_MIC: 'mic_files',
                            Files: 'attachments',
                        };
                        if (sbtskStatusFields.translatedFields) {
                            for (const singleField of sbtskStatusFields?.translatedFields) {
                                if (singleField.required) {
                                    let exists = false;

                                    if (
                                        singleField.cust_component &&
                                        keyMapping[singleField.cust_component]
                                    ) {
                                        // Check in the corresponding nested object
                                        const nestedKey =
                                            keyMapping[
                                                singleField.cust_component
                                            ];
                                        exists =
                                            dummyQuery[
                                                nestedKey
                                            ]?.hasOwnProperty(
                                                singleField.key
                                            ) &&
                                            Array.isArray(
                                                dummyQuery[nestedKey][
                                                    singleField.key
                                                ]
                                            ) &&
                                            dummyQuery[nestedKey][
                                                singleField.key
                                            ].length > 0;
                                    } else {
                                        // Check directly in dummyQuery
                                        exists = dummyQuery.hasOwnProperty(
                                            singleField.key
                                        );
                                    }

                                    if (!exists) {
                                        console.log(
                                            `services_workflow :: singleSbtsktypeFrAutoStatusData :: Missing or empty required key: ${singleField.key} (${singleField.label}). Exiting loop.`
                                        );
                                        isRequiredFieldsMissing = true;
                                        break; // Exit the loop immediately
                                    }
                                }
                            }
                        }

                        if (
                            isAttachmentsFieldMandatory &&
                            !isRequiredFieldsMissing
                        ) {
                            const hasAttachments =
                                dummyQuery['attachments']?.hasOwnProperty(
                                    'general'
                                ) &&
                                Array.isArray(
                                    dummyQuery['attachments']['general']
                                ) &&
                                dummyQuery['attachments']['general'].length > 0;
                            isRequiredFieldsMissing = !hasAttachments;
                        }

                        if (
                            sbtskStatusFields.translatedFields &&
                            isRequiredFieldsMissing
                        ) {
                            let srvcReqTimelineTitle = `Task update - Failed to move subtask from ${sbtskStatusesKeyMapping?.[subtaskCurrentStatus]} to ${sbtskStatusesKeyMapping?.[subtaskNewStatus]}`;
                            let subtaskTimelineTitle = `Failed to move from ${sbtskStatusesKeyMapping?.[subtaskCurrentStatus]} to ${sbtskStatusesKeyMapping?.[subtaskNewStatus]}`;
                            await this.services_model.createTimelineforSrvcReq(
                                {
                                    ...dummyQuery,
                                    comment: 'Mandatory fields found',
                                },
                                srvcTypeId,
                                entry_id,
                                'UPDATE',
                                srvcReqTimelineTitle
                            );
                            try {
                                await subtasks_model.createTimelineforSubtask(
                                    {
                                        ...dummyQuery,
                                        comment: 'Mandatory fields found',
                                    },
                                    sbtskTypeId,
                                    sbtskId,
                                    'UPDATE',
                                    subtaskTimelineTitle
                                );
                            } catch (error) {
                                console.log(
                                    'Error in createTimelineforSubtask',
                                    error
                                );
                            }
                            continue;
                        }

                        //*** Status wise custom fields required validation End ****
                        //*** lambda validation start ***
                        const lambdaValidationOperation =
                            await subtasks_model.callLambdaFnForStatusValidation(
                                {
                                    ...dummyQuery,
                                    update_type_id: subtaskNewStatus,
                                    srvcReqId: entry_id,
                                    sbtskId: sbtskId,
                                    sbtsk_type_id: sbtskTypeId,
                                    srvc_req_id: entry_id,
                                }
                            );

                        if (
                            lambdaValidationOperation &&
                            (!lambdaValidationOperation.isSuccess() ||
                                !lambdaValidationOperation.success)
                        ) {
                            let srvcReqTimelineTitle = `Task update - Failed to move subtask from ${sbtskStatusesKeyMapping?.[subtaskCurrentStatus]} to ${sbtskStatusesKeyMapping?.[subtaskNewStatus]}`;
                            let subtaskTimelineTitle = `Failed to move from ${sbtskStatusesKeyMapping?.[subtaskCurrentStatus]} to ${sbtskStatusesKeyMapping?.[subtaskNewStatus]}`;
                            await this.services_model.createTimelineforSrvcReq(
                                {
                                    ...dummyQuery,
                                    comment: 'Validation Failed',
                                },
                                srvcTypeId,
                                entry_id,
                                'UPDATE',
                                srvcReqTimelineTitle
                            );

                            await subtasks_model.createTimelineforSubtask(
                                {
                                    ...dummyQuery,
                                    comment: 'Validation Failed',
                                },
                                sbtskTypeId,
                                sbtskId,
                                'UPDATE',
                                subtaskTimelineTitle
                            );
                            continue;
                        }
                        //*** lambda validation end ***

                        //*** Subtask status updation start ***
                        let statusUpdateQuery = {};
                        statusUpdateQuery['srvc_req_id'] = srvcReqId;
                        statusUpdateQuery['sbtsk_org_id'] = sbtskOrgId;
                        statusUpdateQuery['srvc_type_id'] = srvcTypeId;
                        statusUpdateQuery['sbtsk_type_id'] = sbtskTypeId;
                        statusUpdateQuery['remarks'] = 'No remarks';
                        statusUpdateQuery['auto_move_status'] = true;

                        await this.moveSubtaskStatus(
                            subtasks_model,
                            statusUpdateQuery,
                            srvcReqId,
                            srvcTypeId,
                            subtaskNewStatus,
                            sbtskId,
                            entry_id
                        );
                        console.log(
                            'services_workflow :: singleSbtsktypeFrAutoStatusData :: end :: entry_id',
                            entry_id
                        );
                        //*** Subtask status updation End ***
                    }
                }
            }
        } else {
            console.log(
                'services_workflow :: singleSbtsktypeFrAutoStatusData :: getSingleEntry failed',
                entry_id
            );
            return;
        }
    }

    async moveSubtaskStatus(
        subtasks_model,
        query,
        srvc_req_id,
        srvc_type_id,
        subtaskNewStatus,
        sbtskId,
        entry_id,
        job_id = 0
    ) {
        try {
            let status_update_query = { ...query };
            status_update_query['update_type_id'] = subtaskNewStatus;

            console.log(
                'services_workflow :: moveSubtaskStatus :: job_id',
                job_id,
                status_update_query,
                srvc_req_id,
                sbtskId
            );
            await subtasks_model.createOrUpdate(
                status_update_query,
                sbtskId,
                subtaskNewStatus
            );
        } catch (error) {
            console.log(
                'Error in services_workflow :: moveSubtaskStatus',
                error
            );
        }
    }
    getSubjectFrSrvcStatusNotifyWorkFlow(isSrvcReqAlreadyCreated, notify_data) {
        let title = notify_data.title;
        let statusTransition = notify_data.status_transitions;
        if (isSrvcReqAlreadyCreated) {
            return `${title} moved to ${statusTransition?.details?.title} by ${statusTransition.u_by ? statusTransition.u_by : statusTransition.c_by}`;
        }
        return `New ${notify_data.srvc_type_title} created - ${title} by ${statusTransition.c_by}`;
    }

    initServiceModelTypeAndReqId(srvc_type_id, srvc_req_id) {
        // Specific for service types
        this.srvc_type_id = srvc_type_id;
        this.srvc_req_id = srvc_req_id;
    }

    getFreshInstance(model) {
        const clonedInstance = new services_workflow(model.services_model);
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports.default = services_workflow;
