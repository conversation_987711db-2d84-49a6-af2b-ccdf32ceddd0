let event = {
  changedValues: {},
  allValues: { attachments: {}, mic_files: {}, camera_files: {} },
  meta: [
    {
      key: "9f6232d7-c95e-44a3-812d-a8b4bc4a3266",
      required: false,
      label: "Customer Type",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "9edf1c41-c5e3-41e3-a03c-338bc17c287f",
      required: false,
      label: "Enquiry Source Type",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "2b336578-3dfb-4964-99a2-bd720e9a6ec8",
      required: false,
      label: "Enquiry Source Sub-Type",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "a4f0c947-8e70-490b-8b44-2f90f4ab3e26",
      required: false,
      label: "Campaign Description",
    },
    {
      key: "d578b9e7-d2ce-49b3-b762-dd39d97a1763",
      required: false,
      label: "Identifier",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "bfdc8617-b163-4b1e-8a95-5c87298cf988",
      required: false,
      label: "APC",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "f42d97e8-d0f5-4a0e-ba9a-f3174beb00a0",
      required: false,
      label: "HEX",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "7ad76c6f-ad2e-4a60-a76b-e4f59ec8760c",
      required: false,
      label: "Event Description",
    },
    {
      key: "746f08c2-7f65-4c83-a591-e39881702f47",
      required: false,
      label: "Exhibition Description",
    },
    {
      key: "5575aed8-79b1-4543-b7ea-2b10462040d1",
      required: false,
      label: "Enquiry for",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "8897b832-e633-4e8c-bfde-b40fd2243a39",
      required: false,
      label: "Product",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "b090b036-3431-4636-a4c2-dbda0a27a276",
      required: false,
      label: "Furniture Fitting Category",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "e21fef9b-e3bd-4a67-bf84-795e173c62d7",
      required: false,
      label: "Door Hardware Category",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "464b13e4-27e3-40c2-bc71-bf41b12d3044",
      required: false,
      label: "Appliances Category",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "23635787-3086-4a1e-a182-e7f2052a682b",
      required: false,
      label: "Service type",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "fd6fba2a-ee60-4130-b652-adc66a23a359",
      required: false,
      label: "Other Enquiry Type",
      widget: "select",
      options: [Array],
      widgetProps: [Object],
    },
    {
      key: "aa4fb495-9dd1-4f5e-9777-33586304ac0d",
      required: false,
      label: "Expected Value (INR)",
      widget: "number",
    },
    {
      key: "a15da723-87dd-4b4d-a756-38e71676f96a",
      required: false,
      label: "Next Call Date",
      widget: "date-picker",
      widgetProps: [Object],
    },
    {
      key: "a96a35fe-f07d-46bf-ba31-d30778c8d0b2",
      required: false,
      label: "Qualified Remarks",
    },
    {
      key: "5390bda7-9bd4-42da-b297-78bbcbeb6e58",
      required: false,
      label: "Design Remarks",
    },
    {
      key: "b630863e-41cc-4f2d-aeb5-e233752b4ab4",
      colSpan: 2,
      required: false,
      label: "Design Ready Remarks",
    },
    {
      key: "40a441e2-e7d2-4101-bdd1-43008864eb42",
      required: false,
      label: "Quote Value (INR)",
      widget: "number",
    },
    {
      key: "40bf5696-6254-4d66-a252-00e692e966b7",
      required: false,
      label: "Proposal Remarks",
    },
    {
      key: "1325d6ab-2b6d-483d-8b79-5593d3e4a5d2",
      required: false,
      label: "Expected Closure Date",
      widget: "date-picker",
      widgetProps: [Object],
    },
    {
      key: "ddbbba38-1d1e-417c-8825-d73785ef9e25",
      required: false,
      label: "Pending Remarks",
    },
    {
      key: "cc8f5465-9d27-4f31-b7d8-2783ecf91c3c",
      required: false,
      label: "Last Follow up Date",
      widget: "date-picker",
      widgetProps: [Object],
    },
    {
      key: "9963b471-e396-49b2-88f0-1ff94071c2cb",
      required: false,
      label: "Closure Remarks",
    },
    {
      key: "b0f4c305-4450-4fde-a82e-3df132a2e9b1",
      required: false,
      label: "Confirm Sale Value (INR)",
      widget: "number",
    },
    {
      key: "1b1d6364-3691-49f7-9efc-1dfbff7a5c1a",
      required: false,
      label: "Last Contacted Date",
      widget: "date-picker",
      widgetProps: [Object],
    },
    {
      key: "b89858b1-6933-46f2-b9b0-921937bf98ab",
      required: false,
      label: "Lost Remarks",
    },
    {
      key: "a986e40a-95c9-49f9-b2df-ffe6e3af7310",
      required: false,
      label: "Rejection Remarks",
    },
    {
      key: "17c866e9-fa11-4397-9062-74a69c49774b",
      required: false,
      label: "Verified Sale Value",
      widget: "number",
    },
    {
      key: "dbe0c482-3e20-4114-b79d-e4d4040739ef",
      required: false,
      label: "Verified Remarks",
    },
    {
      key: "971bef0c-07f9-4bf2-8259-c1ca4dcb5eef",
      required: false,
      label: "Sketches",
      cust_component: "Files",
      cust_component_value: "",
    },
    {
      key: "0353e8ad-e1da-46f6-934a-f19b10a56502",
      required: false,
      label: "Design",
      cust_component: "Files",
      cust_component_value: "",
    },
    {
      key: "b655aba9-d38e-4af4-860a-e4b54574ae2c",
      required: false,
      label: "Quote",
      cust_component: "Files",
      cust_component_value: "",
    },
    {
      key: "4cd0d63d-abeb-4214-8eb0-0c3e2c16d934",
      required: false,
      label: "Dealer Acknowledgement",
      cust_component: "Files",
      cust_component_value: "",
    },
    {
      key: "28fa368f-cdae-4a16-9d4d-bd73eb387489",
      required: false,
      label: "BOQ amount for FF",
      widget: "number",
    },
    {
      key: "4bf41e31-adc1-4bf4-a25b-5d44d06f0ce7",
      required: false,
      label: "BOQ amount for Appliances",
      widget: "number",
    },
    {
      key: "5d94b511-c42d-4237-87ad-fd949a525e48",
      required: false,
      label: "BOQ amount for Lights",
      widget: "number",
    },
    {
      key: "2ed9bd78-3697-4207-a2d4-769514260e0c",
      required: false,
      label: "BOQ amount for DH",
      widget: "number",
    },
  ],
  is_editmode: true,
  request_data: {
    host_d: "tms.wify.co.in",
    org_id: 327,
    usr_id: "a986e3d0-a5b3-43e3-b42b-8b8797ad19c4",
    entry_id: 3833206,
    cust_city: "Chandrapur",
    mic_files: {},
    cust_email: "<EMAIL>",
    cust_state: "MAHARASHTRA",
    ip_address: "**************:55252",
    user_agent:
      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    attachments: {},
    cust_line_2: "Chandrapur",
    cust_mobile: "9011289839",
    camera_files: {},
    cust_pincode: "442401",
    srvc_type_id: "802",
    authority_602: "5b790c9d-021b-4204-922b-ff8af1836094",
    authority_604: "a1f2e61e-3a54-4a28-8479-ef4a1aefe409",
    cust_full_name: "Tushar gawande",
    request_priority: "Urgent",
    is_customer_access: "0",
    request_description:
      "Customer wants to connect with the salesperson for the project",
    srvc_type_his_db_id: "8577",
    lastChangedFileSectionIds: [],
    "23635787-3086-4a1e-a182-e7f2052a682b": ["Technical Consultation"],
    "2b336578-3dfb-4964-99a2-bd720e9a6ec8":
      "e96d8df3-68f5-47e3-876a-578a3a2d5a9d",
    "5575aed8-79b1-4543-b7ea-2b10462040d1": ["Service"],
    "9edf1c41-c5e3-41e3-a03c-338bc17c287f":
      "b15ef4d3-9a03-4f71-8c2e-16a3cee21630",
    "9f6232d7-c95e-44a3-812d-a8b4bc4a3266":
      "1f2442a8-8594-46e0-8098-07155fc90d17",
  },
};

const axios = require("axios");
const oauth = require("oauth-1.0a");
const crypto = require("crypto");
//testbycode
let allEnquirySourceSubType = [];

const modifyFieldInMeta = (meta, fieldKey, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (singleField.key == fieldKey) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const modifyFieldsInMeta = (meta, fieldKeys, newKeyValueObj) => {
  meta.map((singleField, index) => {
    if (fieldKeys.includes(singleField.key)) {
      singleField = {
        ...singleField,
        ...newKeyValueObj,
      };
      meta[index] = singleField;
    }
  });
};

const getObjByKeyFrmArray = (array = [], key, value) => {
  return array.filter((singleObj) => singleObj[key] == value)?.[0];
};

function hideEverythingOnTheFormExcept(meta, exceptionLabels = []) {
  meta.map((singleField, index) => {
    singleField = {
      ...singleField,
      hide: !exceptionLabels.includes(singleField.label),
    };
    meta[index] = singleField;
  });
}

function selectedLabelIs(
  meta,
  allValues,
  labelToKeyMap,
  selectLabel,
  selectedOptionLabel
) {
  const optionsForSelect = getObjByKeyFrmArray(
    meta,
    "key",
    labelToKeyMap[selectLabel]
  )?.options;
  const selectedOptionValue = getObjByKeyFrmArray(
    optionsForSelect,
    "label",
    selectedOptionLabel
  )?.value;
  const selectedValueForSelect = allValues[labelToKeyMap[selectLabel]];

  console.log("selectedValueForSelect -", selectedValueForSelect);

  if (Array.isArray(selectedValueForSelect)) {
    return selectedValueForSelect.includes(selectedOptionValue);
  } else {
    return allValues[labelToKeyMap[selectLabel]] == selectedOptionValue;
  }
}

const getArrayByKeyFrmArray = (array = [], key, filterArray = []) => {
  return array.filter((singleObj) => filterArray.includes(singleObj[key]));
};

function updateManipulatedFieldValues({
  manipulatedFieldValues = {},
  labelsToCheck = [],
  meta,
  allValues,
  labelToKeyMap,
  selectLabel,
}) {
  const isLabelsPresent = labelsToCheck.filter((_label) =>
    selectedLabelIs(meta, allValues, labelToKeyMap, selectLabel, _label)
  );
  if (
    labelsToCheck.length === 0 ||
    !labelsToCheck.includes(isLabelsPresent[0])
  ) {
    manipulatedFieldValues[labelToKeyMap[selectLabel]] = "";
  }
}

const handler = async (event) => {
  let responseStatus = false;
  let responseMessage = "Unknown error";
  let manipulatedFieldValues = {};

  let {
    meta,
    allValues,
    changedValues,
    request_data,
    currentMeta,
    is_editmode,
  } = event;

  console.log("event", event);

  allEnquirySourceSubType = getObjByKeyFrmArray(
    meta,
    "label",
    "Enquiry Source Sub-Type"
  ).options;

  const labelToKeyMap = {};

  meta.map((singleField) => {
    labelToKeyMap[singleField.label] = singleField.key;
  });

  console.log("event ", event);
  console.log("event request_data", event.request_data);

  // check if the API based data is already there
  if (is_editmode) {
    modifyFieldsInMeta(
      meta,
      [
        labelToKeyMap["Campaign Description"],
        labelToKeyMap["Identifier"],
        labelToKeyMap["APC"],
        labelToKeyMap["HEX"],
        labelToKeyMap["Event Description"],
        labelToKeyMap["Exhibition Description"],
        labelToKeyMap["Product"],
        labelToKeyMap["Furniture Fitting Category"],
        labelToKeyMap["Door Hardware Category"],
        labelToKeyMap["Appliances Category"],
        labelToKeyMap["Service type"],
        labelToKeyMap["Other Enquiry Type"],
        labelToKeyMap["Where did you hear about this showroom from?"],
      ],
      {
        hide: true,
      }
    );
  } else {
    hideEverythingOnTheFormExcept(meta, [
      "Customer Type",
      "Enquiry Source Type",
      "Enquiry for",
    ]);
  }

  modifyFieldsInMeta(
    meta,
    [
      labelToKeyMap["Customer Type"],
      labelToKeyMap["Enquiry Source Type"],
      labelToKeyMap["Enquiry Source Sub-Type"],
      labelToKeyMap["Enquiry for"],
    ],
    {
      required: true,
    }
  );

  modifyFieldInMeta(meta, labelToKeyMap["Event Description"], {
    hide: true,
    required: false,
  });

  modifyFieldInMeta(meta, labelToKeyMap["Exhibition Description"], {
    hide: true,
    required: false,
  });

  // initialising all values in edit mode
  if (is_editmode) {
    allValues = {
      ...request_data,
      ...allValues,
    };
  }

  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "Marketing Digital - Organic"
    )
  ) {
    const selectOptions = ["Website", "Mobile App", "Hettich E Shop", "Others"];
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: selectOptions,
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    const filteredOptions = getArrayByKeyFrmArray(
      allEnquirySourceSubType,
      "label",
      selectOptions
    );
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: false,
      required: true,
      options: filteredOptions,
    });
    // modifyFieldInMeta(meta, labelToKeyMap['Campaign Description'], {
    //     hide: false,
    //     required: true
    // })
  }

  // if source type is marketing digital paid
  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "Marketing Digital - Paid"
    )
  ) {
    const selectOptions = [
      "Facebook",
      "Instagram",
      "Twitter",
      "LinkedIn",
      "Google",
      "Youtube",
      "Others",
    ];
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: selectOptions,
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    const filteredOptions = getArrayByKeyFrmArray(
      allEnquirySourceSubType,
      "label",
      selectOptions
    );
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: false,
      required: true,
      options: filteredOptions,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Campaign Description"], {
      hide: false,
      required: true,
    });
  }

  // if source type is Customer Service
  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "Customer Service"
    )
  ) {
    const selectOptions = ["Phone", "Email"];
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: selectOptions,
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    const updatedOptions = getArrayByKeyFrmArray(
      allEnquirySourceSubType,
      "label",
      selectOptions
    );
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: false,
      required: true,
      options: updatedOptions,
    });
    // modifyFieldInMeta(meta, labelToKeyMap['Campaign Description'], {
    //     hide: false,
    //     required: true
    // })
  }

  // Identifier (If Marketing Type = Others or Customer Service enquiry subtype = Phone)
  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "Customer Service"
    ) &&
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Sub-Type",
      "Phone"
    )
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Identifier"], {
      hide: false,
      required: true,
    });
  }

  // Select (If Lead Source = APC)
  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "APC"
    )
  ) {
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: [],
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });

    modifyFieldInMeta(meta, labelToKeyMap["APC"], {
      hide: false,
      required: true,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: true,
      required: false,
    });
    modifyFieldInMeta(
      meta,
      labelToKeyMap["Where did you hear about this showroom from?"],
      {
        hide: false,
        required: true,
      }
    );
  }

  // Select (If Lead Source = HEX)
  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "HEX"
    )
  ) {
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: [],
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    modifyFieldInMeta(meta, labelToKeyMap["HEX"], {
      hide: false,
      required: true,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: true,
      required: false,
    });
  }

  // Event / Exhibition
  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "Event"
    )
  ) {
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: [],
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: true,
      required: false,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Event Description"], {
      hide: false,
      required: true,
    });
  }

  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "Exhibition"
    )
  ) {
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: [],
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: true,
      required: false,
    });
    modifyFieldInMeta(meta, labelToKeyMap["Exhibition Description"], {
      hide: false,
      required: true,
    });
  }

  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "Referral"
    )
  ) {
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: [],
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: true,
      required: false,
    });
  }

  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "E-Shop"
    )
  ) {
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: [],
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: true,
      required: false,
    });
  }

  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Enquiry Source Type",
      "WhatsApp"
    )
  ) {
    updateManipulatedFieldValues({
      manipulatedFieldValues: manipulatedFieldValues,
      labelsToCheck: [],
      allValues: allValues,
      labelToKeyMap: labelToKeyMap,
      meta: meta,
      selectLabel: "Enquiry Source Sub-Type",
    });
    modifyFieldInMeta(meta, labelToKeyMap["Enquiry Source Sub-Type"], {
      hide: true,
      required: false,
    });
  }

  // Enquiry for product
  if (
    selectedLabelIs(meta, allValues, labelToKeyMap, "Enquiry for", "Product")
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Product"], {
      hide: false,
      required: true,
    });
  }

  // Product furniture fitting
  if (
    selectedLabelIs(
      meta,
      allValues,
      labelToKeyMap,
      "Product",
      "Furniture Fittings"
    )
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Furniture Fitting Category"], {
      hide: false,
      required: true,
    });
  }
  // Product Door Hardware
  if (
    selectedLabelIs(meta, allValues, labelToKeyMap, "Product", "Door Hardware")
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Door Hardware Category"], {
      hide: false,
      required: true,
    });
  }

  // Product Appliances
  if (
    selectedLabelIs(meta, allValues, labelToKeyMap, "Product", "Appliances")
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Appliances Category"], {
      hide: false,
      required: true,
    });
  }

  // Enquiry for service
  if (
    selectedLabelIs(meta, allValues, labelToKeyMap, "Enquiry for", "Service")
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Service type"], {
      hide: false,
      required: true,
    });
  }

  // Enquiry for service
  if (
    selectedLabelIs(meta, allValues, labelToKeyMap, "Enquiry for", "Others")
  ) {
    modifyFieldInMeta(meta, labelToKeyMap["Other Enquiry Type"], {
      hide: false,
      required: true,
    });
  }

  responseStatus = true;
  responseMessage = "success";
  const response = {
    status: responseStatus,
    message: responseMessage,
    data: { meta, allValues, changedValues, manipulatedFieldValues },
  };
  return response;
};

exports.handler = handler;
