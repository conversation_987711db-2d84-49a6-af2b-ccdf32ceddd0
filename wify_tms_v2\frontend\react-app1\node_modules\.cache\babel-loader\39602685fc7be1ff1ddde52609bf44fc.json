{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Github\\\\wify_tms_v2\\\\frontend\\\\react-app1\\\\src\\\\routes\\\\users\\\\helper.js\";\nimport { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';\nimport ConfigHelpers from '../../util/ConfigHelpers';\nimport React from 'react';\nimport { Button } from 'antd';\nimport { addressFill, getAddressBasedOnLatAndLng, getAddressFieldKeys, getConcatenatedAddressFrmForm } from '../../util/CustomerHelpers';\nimport LocationSearchInput from '../../components/LocationSearchInput';\nimport RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';\nimport MapComponent from '../../components/wify-utils/MapComponent/index ';\nimport { handleClearSelect } from '../../util/helpers';\nexport const getUsersActiveInfoMeta = (userData, setShowAlertMessage, frBulkUpdate = false) => {\n  var _userData$form_data;\n  const meta = {\n    columns: 4,\n    formItemLayout: null,\n    fields: [{\n      key: 'is_active',\n      label: 'Active',\n      widget: 'switch',\n      initialValue: (userData === null || userData === void 0 ? void 0 : (_userData$form_data = userData.form_data) === null || _userData$form_data === void 0 ? void 0 : _userData$form_data.is_active) || true,\n      onChange: value => {\n        if (!frBulkUpdate) {\n          setShowAlertMessage(value);\n        }\n      }\n    }]\n  };\n  return meta;\n};\nexport const getUsersLastSeenInfoMeta = () => {\n  const meta = {\n    columns: 4,\n    formItemLayout: null,\n    fields: [{\n      key: 'last_seen',\n      label: 'Last Seen'\n    }]\n  };\n  return meta;\n};\nexport const getPrimaryUsrRole = (formRef, userData, primaryRole) => {\n  var _formRef$current, _userData$form_data2;\n  let primaryRoleList = [];\n  let selectedRoleList = (formRef === null || formRef === void 0 ? void 0 : (_formRef$current = formRef.current) === null || _formRef$current === void 0 ? void 0 : _formRef$current.getFieldValue('user_role')) || (userData === null || userData === void 0 ? void 0 : (_userData$form_data2 = userData.form_data) === null || _userData$form_data2 === void 0 ? void 0 : _userData$form_data2.user_role);\n  if (selectedRoleList && (userData === null || userData === void 0 ? void 0 : userData.roleList)) {\n    primaryRoleList = userData.roleList.filter(role => selectedRoleList.includes(role.value) || role.value == primaryRole);\n  }\n  return primaryRoleList;\n};\nexport const getUsersInfoMeta = (userConfigData, userData, frBulkUpdate = false, frExportModal = false, refreshUI = undefined, selectedSrvcTypesOrVerticals, isServiceProvider = false, formRef, orgSettingsData, editMode) => {\n  var _userData$form_data3, _userData$form_data3$, _userData$form_data4, _userData$form_data4$, _formRef$current3, _userData$form_data6, _userData$form_data6$, _userData$verticals_l, _userData$srvc_type_l;\n  // console.log(\"BulkAssign userData\",userData);\n  const countryCode = orgSettingsData === null || orgSettingsData === void 0 ? void 0 : orgSettingsData.country_code;\n  const mobileDigit = orgSettingsData === null || orgSettingsData === void 0 ? void 0 : orgSettingsData.mobile_digit;\n  let assgndOrgIdsFieldMeta = [];\n  if (ConfigHelpers.isServiceProvider()) {\n    assgndOrgIdsFieldMeta.push({\n      key: 'assgnd_org_ids',\n      label: 'Assign Customer Access (If empty then a user has access to all CUSTOMERS)',\n      widget: 'select',\n      widgetProps: {\n        mode: 'multiple',\n        optionFilterProp: 'children',\n        onChange: value => {\n          // console.log('value - ',value);\n        }\n      },\n      options: userData === null || userData === void 0 ? void 0 : userData.possbile_orgs_list,\n      placeholder: 'Please select customer access'\n    });\n  }\n  let usrCustomFieldsMetaJson = [];\n  if (userConfigData) {\n    usrCustomFieldsMetaJson = decodeFieldsMetaFrmJson(JSON.stringify(userConfigData === null || userConfigData === void 0 ? void 0 : userConfigData.value), 1);\n  }\n  let selectedSrvcTypeOrVerticalsArray = isServiceProvider ? userData === null || userData === void 0 ? void 0 : (_userData$form_data3 = userData.form_data) === null || _userData$form_data3 === void 0 ? void 0 : (_userData$form_data3$ = _userData$form_data3.form_data) === null || _userData$form_data3$ === void 0 ? void 0 : _userData$form_data3$.user_vertical : userData === null || userData === void 0 ? void 0 : (_userData$form_data4 = userData.form_data) === null || _userData$form_data4 === void 0 ? void 0 : (_userData$form_data4$ = _userData$form_data4.form_data) === null || _userData$form_data4$ === void 0 ? void 0 : _userData$form_data4$.srvc_types_assigned_to_user;\n  const srvcTypeFieldBasedFieldSelectors = [];\n  if (selectedSrvcTypesOrVerticals || selectedSrvcTypeOrVerticalsArray) {\n    let srvcTypeOrVerticalList = [];\n    if (isServiceProvider) {\n      srvcTypeOrVerticalList = userData === null || userData === void 0 ? void 0 : userData.verticals_list;\n    } else {\n      srvcTypeOrVerticalList = userData === null || userData === void 0 ? void 0 : userData.srvc_type_list;\n    }\n    let selectedSrvcTypesOrVerticalsFrmForm = selectedSrvcTypesOrVerticals ? selectedSrvcTypesOrVerticals || [] : selectedSrvcTypeOrVerticalsArray || [];\n    if ((selectedSrvcTypesOrVerticalsFrmForm === null || selectedSrvcTypesOrVerticalsFrmForm === void 0 ? void 0 : selectedSrvcTypesOrVerticalsFrmForm.length) > 0) {\n      // eslint-disable-next-line no-unused-expressions\n      selectedSrvcTypesOrVerticalsFrmForm === null || selectedSrvcTypesOrVerticalsFrmForm === void 0 ? void 0 : selectedSrvcTypesOrVerticalsFrmForm.map(singleSelectedSrvcTypeOrVertical => {\n        var _userData$form_data5, _userData$form_data5$;\n        const formData = retrieveSelectedFormData(srvcTypeOrVerticalList, singleSelectedSrvcTypeOrVertical);\n        const srvcTypeCategoryFieldOptions = getSrvcCategoryFieldOptions(formData.form_data, isServiceProvider);\n        let selectorKey;\n        if (isServiceProvider) {\n          selectorKey = `${singleSelectedSrvcTypeOrVertical}_skill_for_vertical`;\n        } else {\n          selectorKey = `${singleSelectedSrvcTypeOrVertical}_skill_for_srvc_type`;\n        }\n        srvcTypeCategoryFieldOptions && srvcTypeFieldBasedFieldSelectors.push({\n          key: selectorKey,\n          label: `Select skills for ${formData === null || formData === void 0 ? void 0 : formData.label} ${isServiceProvider ? 'vertical' : 'service type'}`,\n          widget: 'select',\n          widgetProps: {\n            mode: 'multiple',\n            allowClear: true,\n            onChange: () => {\n              if (refreshUI) {\n                refreshUI();\n              }\n            }\n          },\n          options: srvcTypeCategoryFieldOptions\n        });\n        const getSelectedSkills = () => {\n          var _formRef$current2;\n          let selected_skills = formRef === null || formRef === void 0 ? void 0 : (_formRef$current2 = formRef.current) === null || _formRef$current2 === void 0 ? void 0 : _formRef$current2.getFieldValue(selectorKey);\n          return selected_skills;\n        };\n        let skillLevelPrefix = isServiceProvider ? 'vertical_level' : 'srvc_type_level';\n        const skillLevelOptions = [{\n          value: `${skillLevelPrefix}_1`,\n          label: '1(Beginner)'\n        }, {\n          value: `${skillLevelPrefix}_2`,\n          label: '2'\n        }, {\n          value: `${skillLevelPrefix}_3`,\n          label: '3'\n        }, {\n          value: `${skillLevelPrefix}_4`,\n          label: '4'\n        }, {\n          value: `${skillLevelPrefix}_5`,\n          label: '5(Expert)'\n        }];\n        let getSelectedSkillsFrForm = getSelectedSkills() ? getSelectedSkills() : userData === null || userData === void 0 ? void 0 : (_userData$form_data5 = userData.form_data) === null || _userData$form_data5 === void 0 ? void 0 : (_userData$form_data5$ = _userData$form_data5.form_data) === null || _userData$form_data5$ === void 0 ? void 0 : _userData$form_data5$[selectorKey];\n        if (getSelectedSkillsFrForm && srvcTypeCategoryFieldOptions) {\n          // eslint-disable-next-line no-unused-expressions\n          getSelectedSkillsFrForm === null || getSelectedSkillsFrForm === void 0 ? void 0 : getSelectedSkillsFrForm.map(singleSelectedSkills => {\n            const selectedSkillObject = singleSelectedSkills.value ? srvcTypeCategoryFieldOptions === null || srvcTypeCategoryFieldOptions === void 0 ? void 0 : srvcTypeCategoryFieldOptions.find(option => option.value === singleSelectedSkills.value) : srvcTypeCategoryFieldOptions === null || srvcTypeCategoryFieldOptions === void 0 ? void 0 : srvcTypeCategoryFieldOptions.find(option => option.value === singleSelectedSkills);\n            srvcTypeFieldBasedFieldSelectors.push({\n              key: `selected_key_fr_${selectedSkillObject === null || selectedSkillObject === void 0 ? void 0 : selectedSkillObject.label}_of_${formData === null || formData === void 0 ? void 0 : formData.label}`,\n              label: `Select level for ${selectedSkillObject === null || selectedSkillObject === void 0 ? void 0 : selectedSkillObject.label} of ${formData === null || formData === void 0 ? void 0 : formData.label} ${isServiceProvider ? 'vertical' : 'service type'}`,\n              widget: 'select',\n              widgetProps: {\n                mode: '',\n                allowClear: true,\n                onChange: value => {\n                  handleClearSelect(value, formRef, `selected_key_fr_${selectedSkillObject === null || selectedSkillObject === void 0 ? void 0 : selectedSkillObject.label}_of_${formData === null || formData === void 0 ? void 0 : formData.label}`);\n                }\n              },\n              options: skillLevelOptions\n            });\n          });\n        }\n      });\n    }\n  }\n  const primaryRole = (formRef === null || formRef === void 0 ? void 0 : (_formRef$current3 = formRef.current) === null || _formRef$current3 === void 0 ? void 0 : _formRef$current3.getFieldValue('user_primary_role')) || (userData === null || userData === void 0 ? void 0 : (_userData$form_data6 = userData.form_data) === null || _userData$form_data6 === void 0 ? void 0 : (_userData$form_data6$ = _userData$form_data6.form_data) === null || _userData$form_data6$ === void 0 ? void 0 : _userData$form_data6$.user_primary_role);\n  const primaryRoleOptions = getPrimaryUsrRole(formRef, userData, primaryRole);\n  const primaryRoleHasOptionsMoreThanOne = (primaryRoleOptions === null || primaryRoleOptions === void 0 ? void 0 : primaryRoleOptions.length) > 1;\n  let primaryRoleMeta = editMode && primaryRoleHasOptionsMoreThanOne ? [{\n    key: 'user_primary_role',\n    label: 'Select primary role',\n    widget: 'select',\n    required: !frBulkUpdate,\n    placeholder: 'Please select primary role',\n    options: primaryRoleOptions,\n    onChange: () => {\n      if (refreshUI) {\n        refreshUI();\n      }\n    }\n  }] : [];\n  const meta = {\n    columns: 4,\n    formItemLayout: null,\n    fields: [{\n      key: 'user_name',\n      label: 'Full Name',\n      placeholder: 'Eg: john',\n      required: !frBulkUpdate,\n      rules: [{\n        max: 100,\n        message: 'Name must be max 100 characters.'\n      }]\n    }, {\n      key: 'user_code',\n      label: 'Code',\n      placeholder: 'Eg: WMUM10295',\n      rules: [{\n        max: 20,\n        message: 'User code must be max 20 characters.'\n      }]\n    }, {\n      key: 'user_designation',\n      label: 'Designation',\n      placeholder: 'Eg: Technician Incharges',\n      rules: [{\n        max: 50,\n        message: 'Designation must be max 50 characters.'\n      }]\n    }, {\n      key: 'user_role',\n      label: 'Select specific role',\n      widget: 'select',\n      required: !frBulkUpdate,\n      widgetProps: {\n        mode: 'multiple',\n        optionFilterProp: 'children',\n        onChange: value => {\n          if (refreshUI) {\n            refreshUI();\n          }\n          if (!value.includes(primaryRole)) {\n            formRef.current.setFieldsValue({\n              user_primary_role: undefined\n            });\n            if ((value === null || value === void 0 ? void 0 : value.length) == 1) {\n              formRef.current.setFieldsValue({\n                user_primary_role: value[0]\n              });\n            }\n          }\n        }\n      },\n      placeholder: 'Please select specific role',\n      options: userData === null || userData === void 0 ? void 0 : userData.roleList\n    }, ...primaryRoleMeta, {\n      key: 'user_loc_group',\n      label: 'Select Location Group',\n      widget: 'select',\n      widgetProps: {\n        mode: 'multiple',\n        optionFilterProp: 'children',\n        onChange: value => {\n          // console.log('value - ',value);\n        }\n      },\n      options: userData === null || userData === void 0 ? void 0 : userData.locGrpList,\n      placeholder: 'Please select location group'\n    }, ...assgndOrgIdsFieldMeta, {\n      key: 'user_reporting_to',\n      label: 'Select Reporting to',\n      widget: 'select',\n      options: userData === null || userData === void 0 ? void 0 : userData.userList,\n      // to be loaded from API\n      widgetProps: {\n        // defaultValue:'None',\n        showSearch: true,\n        optionFilterProp: 'children',\n        onChange: value => {\n          // console.log('value - ',value);\n        },\n        allowClear: true\n      },\n      placeholder: 'Please select reporting to'\n    }, {\n      key: 'user_mobile',\n      label: `Mobile Number (${countryCode})`,\n      placeholder: 'Eg: 9876543210',\n      required: !frBulkUpdate,\n      rules: [{\n        pattern: new RegExp('^[0-9]*$'),\n        message: 'Please enter your right mobile number!'\n      }, {\n        min: mobileDigit,\n        message: `Mobile no must be ${mobileDigit} characters.`\n      }, {\n        max: mobileDigit,\n        message: `Mobile no must be ${mobileDigit} characters.`\n      }]\n    }, {\n      key: 'user_email',\n      label: 'Email',\n      placeholder: 'Eg: <EMAIL>',\n      required: !frBulkUpdate,\n      rules: [{\n        type: 'email'\n      }]\n    }, ...((userData === null || userData === void 0 ? void 0 : (_userData$verticals_l = userData.verticals_list) === null || _userData$verticals_l === void 0 ? void 0 : _userData$verticals_l.length) > 0 || frExportModal && ConfigHelpers.isServiceProvider() ? [{\n      key: 'user_vertical',\n      label: 'Verticals',\n      widget: 'select',\n      widgetProps: {\n        mode: 'multiple',\n        optionFilterProp: 'children',\n        onChange: val => {\n          if (refreshUI) {\n            refreshUI();\n          }\n        }\n      },\n      options: userData === null || userData === void 0 ? void 0 : userData.verticals_list\n    }] : []), ...(ConfigHelpers.isServiceProvider() ? srvcTypeFieldBasedFieldSelectors : []), ...((userData === null || userData === void 0 ? void 0 : (_userData$srvc_type_l = userData.srvc_type_list) === null || _userData$srvc_type_l === void 0 ? void 0 : _userData$srvc_type_l.length) > 0 || frExportModal || frBulkUpdate ? [{\n      key: 'srvc_types_assigned_to_user',\n      label: 'Service Types',\n      widget: 'select',\n      widgetProps: {\n        mode: 'multiple',\n        optionFilterProp: 'children',\n        onChange: val => {\n          if (refreshUI) {\n            refreshUI();\n          }\n        }\n      },\n      options: userData === null || userData === void 0 ? void 0 : userData.srvc_type_list\n    }] : []), ...(!ConfigHelpers.isServiceProvider() ? srvcTypeFieldBasedFieldSelectors : []), ...usrCustomFieldsMetaJson]\n  };\n  return meta;\n};\nexport const getMetaFrBulkUpdate = (configData, usersData, orgSettingsData = undefined) => {\n  const setShowAlertMessage = value => {\n    this.setState({\n      showAlertMessage: true,\n      isActiveToggleValue: value\n    });\n  };\n  let userConfigData = [];\n  if (configData) {\n    userConfigData = configData[0];\n  }\n  const meta = {\n    columns: 1,\n    formItemLayout: null,\n    fields: [{\n      key: 'user_id',\n      label: 'User ID',\n      required: true\n    }, ...getUsersInfoMeta(userConfigData, usersData, true, false, undefined, undefined, false, undefined, orgSettingsData).fields, {\n      key: 'password',\n      label: 'Password',\n      placeholder: 'Enter your password',\n      widget: 'password',\n      required: false\n    }, {\n      key: 'user_confirm_pass',\n      label: 'Confirm Password',\n      widget: 'password',\n      // hasFeedback:true,\n      placeholder: 'Enter your confirm password',\n      required: false,\n      rules: [({\n        getFieldValue\n      }) => ({\n        validator(_, value) {\n          if (!value || getFieldValue('password') === value) {\n            return Promise.resolve();\n          }\n          return Promise.reject(new Error('The password and confirm password do not match !'));\n        }\n      })]\n    }, ...getUsersActiveInfoMeta(usersData).fields]\n  };\n  return meta;\n};\nexport const getAddressObj = selectedPlace => {\n  const {\n    name,\n    address_components,\n    geometry\n  } = selectedPlace;\n  let selectedPayload = {\n    location: {}\n  };\n  selectedPayload.building = name;\n  const typeToKeyMapping = {\n    country: 'country',\n    administrative_area_level_1: 'state',\n    locality: 'city',\n    sublocality_level_2: 'street',\n    postal_code: 'postalCode',\n    neighborhood: 'building'\n  };\n  for (const component of address_components) {\n    const type = component.types[0];\n    const key = typeToKeyMapping[type];\n    if (key) {\n      selectedPayload[key] = component.long_name;\n    }\n  }\n  if (geometry && geometry.location) {\n    const {\n      lat,\n      lng\n    } = geometry.location;\n    const latLng = typeof lat === 'function' ? {\n      lat: lat(),\n      lng: lng()\n    } : {\n      lat,\n      lng\n    };\n    selectedPayload.location.latlng = latLng;\n  }\n  return selectedPayload;\n};\nexport const getConcatenatedAddressFormData = (prefix = 'cust_', formData, formRef) => {\n  if (!formRef.current) {\n    let addressFieldKeys = getAddressFieldKeys(prefix);\n    return addressFieldKeys.filter(key => formData === null || formData === void 0 ? void 0 : formData[key]).toString();\n  }\n};\nexport const getMapAddressToUsersFieldsMeta = ({\n  form_data,\n  formRef,\n  forceUpdateFn,\n  showPickOnMapModel,\n  togglePickOnMapModel,\n  is_pincode_mandatory,\n  orgSettingsData\n}) => {\n  var _formRef$current4, _formRef$current5, _formRef$current9, _formRef$current0;\n  let filledAddress = getConcatenatedAddressFrmForm('cust_', formRef) || getConcatenatedAddressFormData('cust_', form_data, formRef);\n  let showClearFieldsButton = filledAddress && filledAddress != '';\n  const latitude = (formRef === null || formRef === void 0 ? void 0 : (_formRef$current4 = formRef.current) === null || _formRef$current4 === void 0 ? void 0 : _formRef$current4.getFieldValue('location_latitude')) || (form_data === null || form_data === void 0 ? void 0 : form_data.location_latitude);\n  const longitude = (formRef === null || formRef === void 0 ? void 0 : (_formRef$current5 = formRef.current) === null || _formRef$current5 === void 0 ? void 0 : _formRef$current5.getFieldValue('location_Longitude')) || (form_data === null || form_data === void 0 ? void 0 : form_data.location_Longitude);\n  const onLatLngValueChange = async () => {\n    var _formRef$current6, _formRef$current7;\n    const latitude = formRef === null || formRef === void 0 ? void 0 : (_formRef$current6 = formRef.current) === null || _formRef$current6 === void 0 ? void 0 : _formRef$current6.getFieldValue('location_latitude');\n    const longitude = formRef === null || formRef === void 0 ? void 0 : (_formRef$current7 = formRef.current) === null || _formRef$current7 === void 0 ? void 0 : _formRef$current7.getFieldValue('location_Longitude');\n    if (latitude && longitude) {\n      var _data$results;\n      const data = await getAddressBasedOnLatAndLng(latitude, longitude);\n      addressFill(getAddressObj((_data$results = data.results) === null || _data$results === void 0 ? void 0 : _data$results[0]), formRef);\n    }\n  };\n  let clearGoogleAddressSearch = new Date().getTime();\n  const clearAddress = formRef => {\n    var _formRef$current8;\n    const keyEmptyValue = {};\n    getAddressFieldKeys('cust_').forEach(singleKey => {\n      keyEmptyValue[singleKey] = '';\n    });\n    clearGoogleAddressSearch = new Date().getTime();\n    let result = formRef === null || formRef === void 0 ? void 0 : (_formRef$current8 = formRef.current) === null || _formRef$current8 === void 0 ? void 0 : _formRef$current8.setFieldsValue(keyEmptyValue);\n    forceUpdateFn();\n  };\n  const meta = {\n    // colSpan:4,\n    formItemLayout: null,\n    fields: [{\n      key: 'mark_location_on_map',\n      render: () => {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 25\n          }\n        }, /*#__PURE__*/React.createElement(LocationSearchInput, {\n          placeholder: \"Address\",\n          useCountryAndID: true,\n          onChange: address => {\n            addressFill(address, formRef);\n            forceUpdateFn();\n          },\n          orgSettingsData: orgSettingsData,\n          triggerClear: clearGoogleAddressSearch,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 29\n          }\n        }), /*#__PURE__*/React.createElement(Button, {\n          onClick: () => {\n            togglePickOnMapModel();\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 29\n          }\n        }, \"Pick on Map\"), showPickOnMapModel && /*#__PURE__*/React.createElement(MapComponent, {\n          showPickOnMapModel: showPickOnMapModel,\n          defaultLocation: {\n            lat: latitude || 22.5437692,\n            lng: longitude || 79.1230844\n          },\n          onChange: address => {\n            addressFill(address, formRef);\n            forceUpdateFn();\n            togglePickOnMapModel();\n          },\n          togglePickOnMapModel: () => togglePickOnMapModel(),\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 33\n          }\n        }));\n      }\n    }, {\n      key: 'clear_fields',\n      colSpan: 4,\n      label: 'Clear fields',\n      render() {\n        return /*#__PURE__*/React.createElement(React.Fragment, null, showClearFieldsButton && /*#__PURE__*/React.createElement(Button, {\n          type: \"link\",\n          onClick: () => {\n            clearAddress(formRef);\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 33\n          }\n        }, \"Reset Address\"));\n      }\n    }, {\n      key: 'cust_line_0',\n      colSpan: 4,\n      label: 'Flat no',\n      onChange: () => {\n        forceUpdateFn();\n      },\n      rules: [{\n        max: 50\n      }]\n    }, {\n      key: 'cust_line_1',\n      colSpan: 4,\n      label: 'Building/Apartment name',\n      onChange: () => {\n        forceUpdateFn();\n      },\n      rules: [{\n        max: 200\n      }]\n    }, {\n      key: 'cust_line_2',\n      label: 'Line 1',\n      colSpan: 4,\n      disabled: true,\n      rules: [{\n        max: 1000\n      }]\n    }, {\n      key: 'cust_line_3',\n      label: 'Line 2',\n      colSpan: 4,\n      disabled: true,\n      rules: [{\n        max: 200\n      }]\n    }, {\n      key: 'cust_pincode',\n      label: 'Pincode',\n      colSpan: 2,\n      required: is_pincode_mandatory,\n      // widget: RemoteSourceSelect,\n      disabled: true,\n      widgetProps: {\n        mode: 'single',\n        url: '/searcher',\n        // placeholder:\"Start typing..\",\n        params: {\n          fn: 'getPincode'\n        },\n        widgetProps: {\n          mode: 'single',\n          labelInValue: false,\n          showSearch: true,\n          style: {\n            width: '100%'\n          }\n        }\n      }\n    }, {\n      key: 'cust_city',\n      label: 'City',\n      colSpan: 2,\n      // widget: RemoteSourceSelect,\n      disabled: true,\n      widgetProps: {\n        mode: 'single',\n        url: '/searcher',\n        // placeholder:\"Start typing..\",\n        params: {\n          fn: 'getCities'\n        },\n        widgetProps: {\n          mode: 'single',\n          labelInValue: false,\n          showSearch: true,\n          style: {\n            width: '100%'\n          }\n        }\n      }\n    }, {\n      key: 'cust_state',\n      label: 'State',\n      colSpan: 4,\n      // widget: RemoteSourceSelect,\n      disabled: true,\n      widgetProps: {\n        mode: 'single',\n        url: '/searcher',\n        // placeholder:\"Start typing..\",\n        params: {\n          fn: 'getState'\n        },\n        widgetProps: {\n          mode: 'single',\n          labelInValue: false,\n          showSearch: true,\n          style: {\n            width: '100%'\n          }\n        }\n      }\n    }, {\n      key: 'location_latitude',\n      label: 'Latitude',\n      colSpan: 4,\n      required: formRef === null || formRef === void 0 ? void 0 : (_formRef$current9 = formRef.current) === null || _formRef$current9 === void 0 ? void 0 : _formRef$current9.getFieldValue('location_Longitude'),\n      placeholder: 'Eg 37.7749',\n      onChange: e => {\n        onLatLngValueChange();\n        forceUpdateFn();\n      }\n    }, {\n      key: 'location_Longitude',\n      label: 'Longitude',\n      colSpan: 4,\n      required: formRef === null || formRef === void 0 ? void 0 : (_formRef$current0 = formRef.current) === null || _formRef$current0 === void 0 ? void 0 : _formRef$current0.getFieldValue('location_latitude'),\n      placeholder: 'Eg -122.4194',\n      onChange: e => {\n        onLatLngValueChange();\n        forceUpdateFn();\n      }\n    }, ...(latitude && longitude ? [{\n      key: 'view_location',\n      render() {\n        let url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;\n        return /*#__PURE__*/React.createElement(\"a\", {\n          href: url,\n          target: \"_blank\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 806,\n            columnNumber: 35\n          }\n        }, /*#__PURE__*/React.createElement(\"i\", {\n          class: \"icon icon-location\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 807,\n            columnNumber: 39\n          }\n        }, \" \"), \"View on Google Map\");\n      }\n    }] : [])]\n  };\n  return meta;\n};\nexport const getCapacityFieldsMeta = (getForm, verticals_list, capacityConfigs, handleVerticalChange, fieldSetDetails, keys) => {\n  const {\n    label,\n    value\n  } = fieldSetDetails;\n  const meta = {\n    // colSpan:4,\n    formItemLayout: null,\n    fields: [{\n      key: keys.vertical_key,\n      label: `${label} Vertical`,\n      widget: 'select',\n      widgetProps: {\n        mode: 'single',\n        showSearch: true,\n        optionFilterProp: 'children',\n        allowClear: true,\n        style: {\n          width: '100%'\n        }\n      },\n      onChange: value => {\n        // If field is cleared, set it to empty string directly\n        if (value === undefined) {\n          getForm().setFieldsValue({\n            [keys.vertical_key]: ''\n          });\n        }\n\n        // Handle vertical change and clear dependent fields\n        handleVerticalChange(value, keys);\n        getForm().setFieldsValue({\n          [keys.srvc_hub_key]: '',\n          [keys.skill_key_1]: '',\n          [keys.skill_key_2]: '',\n          [keys.skill_key_3]: ''\n        });\n      },\n      options: verticals_list\n    }, {\n      key: keys.srvc_hub_key,\n      label: `${label} Service Hub`,\n      widget: 'select',\n      widgetProps: {\n        mode: 'single',\n        showSearch: true,\n        optionFilterProp: 'children',\n        allowClear: true,\n        style: {\n          width: '100%'\n        }\n      },\n      onChange: value => {\n        // If field is cleared, set it to empty string directly\n        if (value === undefined) {\n          getForm().setFieldsValue({\n            [keys.srvc_hub_key]: ''\n          });\n        }\n      },\n      options: capacityConfigs.srvc_hub_list\n    }, {\n      key: keys.skill_key_1,\n      label: `${label} Skill 1`,\n      widget: 'select',\n      widgetProps: {\n        mode: 'single',\n        showSearch: true,\n        optionFilterProp: 'children',\n        allowClear: true,\n        style: {\n          width: '100%'\n        }\n      },\n      onChange: value => {\n        // If field is cleared, set it to empty string directly\n        if (value === undefined) {\n          getForm().setFieldsValue({\n            [keys.skill_key_1]: ''\n          });\n        }\n      },\n      options: capacityConfigs.skills,\n      tooltip: 'Will get jobs for this first',\n      // antd rule to make sure that this skill is not selected in 2 & 3\n      rules: [{\n        validator: (_, value) => {\n          let skill2 = getForm().getFieldValue(keys.skill_key_2);\n          let skill3 = getForm().getFieldValue(keys.skill_key_3);\n          if (value && (value == skill2 || value == skill3)) {\n            return Promise.reject(new Error('Skill already selected'));\n          }\n          return Promise.resolve();\n        }\n      }]\n    }, {\n      key: keys.skill_key_2,\n      label: `${label} Skill 2`,\n      widget: 'select',\n      widgetProps: {\n        mode: 'single',\n        showSearch: true,\n        optionFilterProp: 'children',\n        allowClear: true,\n        style: {\n          width: '100%'\n        }\n      },\n      onChange: value => {\n        // If field is cleared, set it to empty string directly\n        if (value === undefined) {\n          getForm().setFieldsValue({\n            [keys.skill_key_2]: ''\n          });\n        }\n      },\n      options: capacityConfigs.skills,\n      tooltip: 'Will get jobs for this if no job for skill 1',\n      // antd rule to make sure that this skill is not selected in 1 & 3\n      rules: [{\n        validator: (_, value) => {\n          let skill1 = getForm().getFieldValue(keys.skill_key_1);\n          let skill3 = getForm().getFieldValue(keys.skill_key_3);\n          if (value && (value == skill1 || value == skill3)) {\n            return Promise.reject(new Error('Skill already selected, you can keep this blank not mandatory!'));\n          }\n          return Promise.resolve();\n        }\n      }]\n    }, {\n      key: keys.skill_key_3,\n      label: `${label} Skill 3`,\n      widget: 'select',\n      widgetProps: {\n        mode: 'single',\n        showSearch: true,\n        optionFilterProp: 'children',\n        allowClear: true,\n        style: {\n          width: '100%'\n        }\n      },\n      onChange: value => {\n        // If field is cleared, set it to empty string directly\n        if (value === undefined) {\n          getForm().setFieldsValue({\n            [keys.skill_key_3]: ''\n          });\n        }\n      },\n      options: capacityConfigs.skills,\n      tooltip: 'Will get jobs for this if no job for skill 2',\n      // antd rule to make sure that this skill is not selected in 1 & 2\n      rules: [{\n        validator: (_, value) => {\n          let skill1 = getForm().getFieldValue(keys.skill_key_1);\n          let skill2 = getForm().getFieldValue(keys.skill_key_2);\n          if (value && (value == skill1 || value == skill2)) {\n            return Promise.reject(new Error('Skill already selected, you can keep this blank not mandatory!'));\n          }\n          return Promise.resolve();\n        }\n      }]\n    }]\n  };\n  return meta;\n};\nexport const retrieveSelectedFormData = (srvcTypeOrVerticalList, singleSelectedSrvcTypeOrVertical) => {\n  const formData = srvcTypeOrVerticalList.filter(eachData => {\n    return eachData.value == singleSelectedSrvcTypeOrVertical;\n  });\n  return formData[0];\n};\nexport const getSrvcCategoryFieldOptions = (formData, isServiceProvider) => {\n  var _srvcCategoryFieldOpt;\n  let categoryFieldKey;\n  if (isServiceProvider) {\n    categoryFieldKey = 'srvc_prvdr_category_field';\n  } else {\n    categoryFieldKey = 'srvc_category_field';\n  }\n  const originalFields = decodeFieldsMetaFrmJson(isServiceProvider ? formData === null || formData === void 0 ? void 0 : formData.sp_cust_fields_json : formData === null || formData === void 0 ? void 0 : formData.srvc_cust_fields_json);\n  const categoryFieldValue = formData === null || formData === void 0 ? void 0 : formData[categoryFieldKey];\n  const srvcCategoryFieldOptions = originalFields.filter(originalField => {\n    return originalField.key == categoryFieldValue;\n  });\n  return (_srvcCategoryFieldOpt = srvcCategoryFieldOptions[0]) === null || _srvcCategoryFieldOpt === void 0 ? void 0 : _srvcCategoryFieldOpt.options;\n};\nexport const formatDate = dateString => {\n  try {\n    const date = new Date(dateString);\n    const month = date.toLocaleString('default', {\n      month: 'short'\n    });\n    const day = date.getDate();\n    // Replace spaces with hyphens\n    const formattedDate = `${day} ${month}`;\n    return formattedDate;\n  } catch (error) {\n    //swallow\n  }\n  return dateString;\n};\nexport const removeAndAppendSixDigitIntegers = inputString => {\n  try {\n    const regex = /\\b\\d{6}, /; // Matches consecutive 6-digit numbers followed by a comma and a space\n    const matches = inputString.match(regex);\n    if (!matches) {\n      return inputString; // No matches found, return the input as is\n    }\n    // Remove the matched 6-digit integers from the input string\n    const stringWithoutIntegers = inputString.replace(regex, '').trim();\n    // Remove any double commas, remove the comma following the 6-digit numbers, and then append the matched 6-digit integers at the end, separated by a single comma\n    const cleanedString = stringWithoutIntegers.replace(/,+/g, ',').trim();\n    const appendedString = cleanedString.replace(/,\\s*$/, '') + ', ' + matches.join(', ');\n    return appendedString.slice(0, -2);\n  } catch (error) {\n    //swallow\n  }\n  return inputString;\n};", "map": {"version": 3, "names": ["decodeFieldsMetaFrmJson", "ConfigHelpers", "React", "<PERSON><PERSON>", "addressFill", "getAddressBasedOnLatAndLng", "getAddressFieldKeys", "getConcatenatedAddressFrmForm", "LocationSearchInput", "RemoteSourceSelect", "MapComponent", "handleClearSelect", "getUsersActiveInfoMeta", "userData", "setShowAlertMessage", "frBulkUpdate", "_userData$form_data", "meta", "columns", "formItemLayout", "fields", "key", "label", "widget", "initialValue", "form_data", "is_active", "onChange", "value", "getUsersLastSeenInfoMeta", "getPrimaryUsrRole", "formRef", "primaryRole", "_formRef$current", "_userData$form_data2", "primaryRoleList", "selectedRoleList", "current", "getFieldValue", "user_role", "roleList", "filter", "role", "includes", "getUsersInfoMeta", "userConfigData", "frExportModal", "refreshUI", "undefined", "selectedSrvcTypesOrVerticals", "isServiceProvider", "orgSettingsData", "editMode", "_userData$form_data3", "_userData$form_data3$", "_userData$form_data4", "_userData$form_data4$", "_formRef$current3", "_userData$form_data6", "_userData$form_data6$", "_userData$verticals_l", "_userData$srvc_type_l", "countryCode", "country_code", "mobileDigit", "mobile_digit", "assgndOrgIdsFieldMeta", "push", "widgetProps", "mode", "optionFilterProp", "options", "possbile_orgs_list", "placeholder", "usrCustomFieldsMeta<PERSON>son", "JSON", "stringify", "selectedSrvcTypeOrVerticalsArray", "user_vertical", "srvc_types_assigned_to_user", "srvcTypeFieldBasedFieldSelectors", "srvcTypeOrVerticalList", "verticals_list", "srvc_type_list", "selectedSrvcTypesOrVerticalsFrmForm", "length", "map", "singleSelectedSrvcTypeOrVertical", "_userData$form_data5", "_userData$form_data5$", "formData", "retrieveSelectedFormData", "srvcTypeCategoryFieldOptions", "getSrvcCategoryFieldOptions", "selector<PERSON><PERSON>", "allowClear", "getSelectedSkills", "_formRef$current2", "selected_skills", "skillLevelPrefix", "skillLevelOptions", "getSelectedSkillsFrForm", "singleSelectedSkills", "selectedSkillObject", "find", "option", "user_primary_role", "primaryRoleOptions", "primaryRoleHasOptionsMoreThanOne", "primaryRoleMeta", "required", "rules", "max", "message", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "locGrpList", "userList", "showSearch", "pattern", "RegExp", "min", "type", "val", "getMetaFrBulkUpdate", "configData", "usersData", "setState", "showAlertMessage", "isActiveToggleValue", "validator", "_", "Promise", "resolve", "reject", "Error", "getAddressObj", "<PERSON><PERSON><PERSON>", "name", "address_components", "geometry", "selectedPayload", "location", "building", "typeToKeyMapping", "country", "administrative_area_level_1", "locality", "sublocality_level_2", "postal_code", "neighborhood", "component", "types", "long_name", "lat", "lng", "latLng", "latlng", "getConcatenatedAddressFormData", "prefix", "addressFieldKeys", "toString", "getMapAddressToUsersFieldsMeta", "forceUpdateFn", "showPickOnMapModel", "togglePickOnMapModel", "is_pincode_mandatory", "_formRef$current4", "_formRef$current5", "_formRef$current9", "_formRef$current0", "<PERSON><PERSON><PERSON><PERSON>", "showClear<PERSON>ieldsButton", "latitude", "location_latitude", "longitude", "location_Longitude", "onLatLngValueChange", "_formRef$current6", "_formRef$current7", "_data$results", "data", "results", "clearGoogleAddressSearch", "Date", "getTime", "<PERSON><PERSON><PERSON><PERSON>", "_formRef$current8", "keyEmptyValue", "for<PERSON>ach", "singleKey", "result", "render", "createElement", "__self", "__source", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "useCountryAndID", "address", "triggerClear", "onClick", "defaultLocation", "colSpan", "Fragment", "disabled", "url", "params", "fn", "labelInValue", "style", "width", "e", "href", "target", "class", "getCapacityFieldsMeta", "getForm", "capacityConfigs", "handleVerticalChange", "fieldSetDetails", "keys", "vertical_key", "srvc_hub_key", "skill_key_1", "skill_key_2", "skill_key_3", "srvc_hub_list", "skills", "tooltip", "skill2", "skill3", "skill1", "eachData", "_srvcCategoryFieldOpt", "categoryFieldKey", "original<PERSON>ields", "sp_cust_fields_json", "srvc_cust_fields_json", "categoryFieldValue", "srvcCategoryFieldOptions", "original<PERSON>ield", "formatDate", "dateString", "date", "month", "toLocaleString", "day", "getDate", "formattedDate", "error", "removeAndAppendSixDigitIntegers", "inputString", "regex", "matches", "match", "stringWithoutIntegers", "replace", "trim", "cleanedString", "appendedString", "join", "slice"], "sources": ["C:/Users/<USER>/Desktop/Github/wify_tms_v2/frontend/react-app1/src/routes/users/helper.js"], "sourcesContent": ["import { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';\r\nimport ConfigHelpers from '../../util/ConfigHelpers';\r\nimport React from 'react';\r\nimport { Button } from 'antd';\r\nimport {\r\n    addressFill,\r\n    getAddressBasedOnLatAndLng,\r\n    getAddressFieldKeys,\r\n    getConcatenatedAddressFrmForm,\r\n} from '../../util/CustomerHelpers';\r\nimport LocationSearchInput from '../../components/LocationSearchInput';\r\nimport RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';\r\nimport MapComponent from '../../components/wify-utils/MapComponent/index ';\r\nimport { handleClearSelect } from '../../util/helpers';\r\n\r\nexport const getUsersActiveInfoMeta = (\r\n    userData,\r\n    setShowAlertMessage,\r\n    frBulkUpdate = false\r\n) => {\r\n    const meta = {\r\n        columns: 4,\r\n        formItemLayout: null,\r\n        fields: [\r\n            {\r\n                key: 'is_active',\r\n                label: 'Active',\r\n                widget: 'switch',\r\n                initialValue: userData?.form_data?.is_active || true,\r\n                onChange: (value) => {\r\n                    if (!frBulkUpdate) {\r\n                        setShowAlertMessage(value);\r\n                    }\r\n                },\r\n            },\r\n        ],\r\n    };\r\n    return meta;\r\n};\r\nexport const getUsersLastSeenInfoMeta = () => {\r\n    const meta = {\r\n        columns: 4,\r\n        formItemLayout: null,\r\n        fields: [\r\n            {\r\n                key: 'last_seen',\r\n                label: 'Last Seen',\r\n            },\r\n        ],\r\n    };\r\n    return meta;\r\n};\r\n\r\nexport const getPrimaryUsrRole = (formRef, userData, primaryRole) => {\r\n    let primaryRoleList = [];\r\n    let selectedRoleList =\r\n        formRef?.current?.getFieldValue('user_role') ||\r\n        userData?.form_data?.user_role;\r\n    if (selectedRoleList && userData?.roleList) {\r\n        primaryRoleList = userData.roleList.filter(\r\n            (role) =>\r\n                selectedRoleList.includes(role.value) ||\r\n                role.value == primaryRole\r\n        );\r\n    }\r\n    return primaryRoleList;\r\n};\r\n\r\nexport const getUsersInfoMeta = (\r\n    userConfigData,\r\n    userData,\r\n    frBulkUpdate = false,\r\n    frExportModal = false,\r\n    refreshUI = undefined,\r\n    selectedSrvcTypesOrVerticals,\r\n    isServiceProvider = false,\r\n    formRef,\r\n    orgSettingsData,\r\n    editMode\r\n) => {\r\n    // console.log(\"BulkAssign userData\",userData);\r\n    const countryCode = orgSettingsData?.country_code;\r\n    const mobileDigit = orgSettingsData?.mobile_digit;\r\n\r\n    let assgndOrgIdsFieldMeta = [];\r\n    if (ConfigHelpers.isServiceProvider()) {\r\n        assgndOrgIdsFieldMeta.push({\r\n            key: 'assgnd_org_ids',\r\n            label: 'Assign Customer Access (If empty then a user has access to all CUSTOMERS)',\r\n            widget: 'select',\r\n            widgetProps: {\r\n                mode: 'multiple',\r\n                optionFilterProp: 'children',\r\n                onChange: (value) => {\r\n                    // console.log('value - ',value);\r\n                },\r\n            },\r\n            options: userData?.possbile_orgs_list,\r\n            placeholder: 'Please select customer access',\r\n        });\r\n    }\r\n\r\n    let usrCustomFieldsMetaJson = [];\r\n    if (userConfigData) {\r\n        usrCustomFieldsMetaJson = decodeFieldsMetaFrmJson(\r\n            JSON.stringify(userConfigData?.value),\r\n            1\r\n        );\r\n    }\r\n    let selectedSrvcTypeOrVerticalsArray = isServiceProvider\r\n        ? userData?.form_data?.form_data?.user_vertical\r\n        : userData?.form_data?.form_data?.srvc_types_assigned_to_user;\r\n\r\n    const srvcTypeFieldBasedFieldSelectors = [];\r\n    if (selectedSrvcTypesOrVerticals || selectedSrvcTypeOrVerticalsArray) {\r\n        let srvcTypeOrVerticalList = [];\r\n        if (isServiceProvider) {\r\n            srvcTypeOrVerticalList = userData?.verticals_list;\r\n        } else {\r\n            srvcTypeOrVerticalList = userData?.srvc_type_list;\r\n        }\r\n\r\n        let selectedSrvcTypesOrVerticalsFrmForm = selectedSrvcTypesOrVerticals\r\n            ? selectedSrvcTypesOrVerticals || []\r\n            : selectedSrvcTypeOrVerticalsArray || [];\r\n\r\n        if (selectedSrvcTypesOrVerticalsFrmForm?.length > 0) {\r\n            // eslint-disable-next-line no-unused-expressions\r\n            selectedSrvcTypesOrVerticalsFrmForm?.map(\r\n                (singleSelectedSrvcTypeOrVertical) => {\r\n                    const formData = retrieveSelectedFormData(\r\n                        srvcTypeOrVerticalList,\r\n                        singleSelectedSrvcTypeOrVertical\r\n                    );\r\n                    const srvcTypeCategoryFieldOptions =\r\n                        getSrvcCategoryFieldOptions(\r\n                            formData.form_data,\r\n                            isServiceProvider\r\n                        );\r\n                    let selectorKey;\r\n                    if (isServiceProvider) {\r\n                        selectorKey = `${singleSelectedSrvcTypeOrVertical}_skill_for_vertical`;\r\n                    } else {\r\n                        selectorKey = `${singleSelectedSrvcTypeOrVertical}_skill_for_srvc_type`;\r\n                    }\r\n                    srvcTypeCategoryFieldOptions &&\r\n                        srvcTypeFieldBasedFieldSelectors.push({\r\n                            key: selectorKey,\r\n                            label: `Select skills for ${formData?.label} ${isServiceProvider ? 'vertical' : 'service type'}`,\r\n                            widget: 'select',\r\n                            widgetProps: {\r\n                                mode: 'multiple',\r\n                                allowClear: true,\r\n                                onChange: () => {\r\n                                    if (refreshUI) {\r\n                                        refreshUI();\r\n                                    }\r\n                                },\r\n                            },\r\n                            options: srvcTypeCategoryFieldOptions,\r\n                        });\r\n\r\n                    const getSelectedSkills = () => {\r\n                        let selected_skills =\r\n                            formRef?.current?.getFieldValue(selectorKey);\r\n                        return selected_skills;\r\n                    };\r\n\r\n                    let skillLevelPrefix = isServiceProvider\r\n                        ? 'vertical_level'\r\n                        : 'srvc_type_level';\r\n                    const skillLevelOptions = [\r\n                        {\r\n                            value: `${skillLevelPrefix}_1`,\r\n                            label: '1(Beginner)',\r\n                        },\r\n                        { value: `${skillLevelPrefix}_2`, label: '2' },\r\n                        { value: `${skillLevelPrefix}_3`, label: '3' },\r\n                        { value: `${skillLevelPrefix}_4`, label: '4' },\r\n                        { value: `${skillLevelPrefix}_5`, label: '5(Expert)' },\r\n                    ];\r\n                    let getSelectedSkillsFrForm = getSelectedSkills()\r\n                        ? getSelectedSkills()\r\n                        : userData?.form_data?.form_data?.[selectorKey];\r\n                    if (\r\n                        getSelectedSkillsFrForm &&\r\n                        srvcTypeCategoryFieldOptions\r\n                    ) {\r\n                        // eslint-disable-next-line no-unused-expressions\r\n                        getSelectedSkillsFrForm?.map((singleSelectedSkills) => {\r\n                            const selectedSkillObject =\r\n                                singleSelectedSkills.value\r\n                                    ? srvcTypeCategoryFieldOptions?.find(\r\n                                          (option) =>\r\n                                              option.value ===\r\n                                              singleSelectedSkills.value\r\n                                      )\r\n                                    : srvcTypeCategoryFieldOptions?.find(\r\n                                          (option) =>\r\n                                              option.value ===\r\n                                              singleSelectedSkills\r\n                                      );\r\n                            srvcTypeFieldBasedFieldSelectors.push({\r\n                                key: `selected_key_fr_${selectedSkillObject?.label}_of_${formData?.label}`,\r\n                                label: `Select level for ${selectedSkillObject?.label} of ${formData?.label} ${isServiceProvider ? 'vertical' : 'service type'}`,\r\n                                widget: 'select',\r\n                                widgetProps: {\r\n                                    mode: '',\r\n                                    allowClear: true,\r\n                                    onChange: (value) => {\r\n                                        handleClearSelect(\r\n                                            value,\r\n                                            formRef,\r\n                                            `selected_key_fr_${selectedSkillObject?.label}_of_${formData?.label}`\r\n                                        );\r\n                                    },\r\n                                },\r\n                                options: skillLevelOptions,\r\n                            });\r\n                        });\r\n                    }\r\n                }\r\n            );\r\n        }\r\n    }\r\n    const primaryRole =\r\n        formRef?.current?.getFieldValue('user_primary_role') ||\r\n        userData?.form_data?.form_data?.user_primary_role;\r\n    const primaryRoleOptions = getPrimaryUsrRole(\r\n        formRef,\r\n        userData,\r\n        primaryRole\r\n    );\r\n    const primaryRoleHasOptionsMoreThanOne = primaryRoleOptions?.length > 1;\r\n\r\n    let primaryRoleMeta =\r\n        editMode && primaryRoleHasOptionsMoreThanOne\r\n            ? [\r\n                  {\r\n                      key: 'user_primary_role',\r\n                      label: 'Select primary role',\r\n                      widget: 'select',\r\n                      required: !frBulkUpdate,\r\n                      placeholder: 'Please select primary role',\r\n                      options: primaryRoleOptions,\r\n                      onChange: () => {\r\n                          if (refreshUI) {\r\n                              refreshUI();\r\n                          }\r\n                      },\r\n                  },\r\n              ]\r\n            : [];\r\n\r\n    const meta = {\r\n        columns: 4,\r\n        formItemLayout: null,\r\n        fields: [\r\n            {\r\n                key: 'user_name',\r\n                label: 'Full Name',\r\n                placeholder: 'Eg: john',\r\n                required: !frBulkUpdate,\r\n                rules: [\r\n                    { max: 100, message: 'Name must be max 100 characters.' },\r\n                ],\r\n            },\r\n            {\r\n                key: 'user_code',\r\n                label: 'Code',\r\n                placeholder: 'Eg: WMUM10295',\r\n                rules: [\r\n                    {\r\n                        max: 20,\r\n                        message: 'User code must be max 20 characters.',\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: 'user_designation',\r\n                label: 'Designation',\r\n                placeholder: 'Eg: Technician Incharges',\r\n                rules: [\r\n                    {\r\n                        max: 50,\r\n                        message: 'Designation must be max 50 characters.',\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: 'user_role',\r\n                label: 'Select specific role',\r\n                widget: 'select',\r\n                required: !frBulkUpdate,\r\n                widgetProps: {\r\n                    mode: 'multiple',\r\n                    optionFilterProp: 'children',\r\n                    onChange: (value) => {\r\n                        if (refreshUI) {\r\n                            refreshUI();\r\n                        }\r\n\r\n                        if (!value.includes(primaryRole)) {\r\n                            formRef.current.setFieldsValue({\r\n                                user_primary_role: undefined,\r\n                            });\r\n                            if (value?.length == 1) {\r\n                                formRef.current.setFieldsValue({\r\n                                    user_primary_role: value[0],\r\n                                });\r\n                            }\r\n                        }\r\n                    },\r\n                },\r\n                placeholder: 'Please select specific role',\r\n                options: userData?.roleList,\r\n            },\r\n            ...primaryRoleMeta,\r\n            {\r\n                key: 'user_loc_group',\r\n                label: 'Select Location Group',\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'multiple',\r\n                    optionFilterProp: 'children',\r\n                    onChange: (value) => {\r\n                        // console.log('value - ',value);\r\n                    },\r\n                },\r\n                options: userData?.locGrpList,\r\n                placeholder: 'Please select location group',\r\n            },\r\n            ...assgndOrgIdsFieldMeta,\r\n            {\r\n                key: 'user_reporting_to',\r\n                label: 'Select Reporting to',\r\n                widget: 'select',\r\n                options: userData?.userList, // to be loaded from API\r\n                widgetProps: {\r\n                    // defaultValue:'None',\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                    onChange: (value) => {\r\n                        // console.log('value - ',value);\r\n                    },\r\n                    allowClear: true,\r\n                },\r\n                placeholder: 'Please select reporting to',\r\n            },\r\n            {\r\n                key: 'user_mobile',\r\n                label: `Mobile Number (${countryCode})`,\r\n                placeholder: 'Eg: 9876543210',\r\n                required: !frBulkUpdate,\r\n                rules: [\r\n                    {\r\n                        pattern: new RegExp('^[0-9]*$'),\r\n                        message: 'Please enter your right mobile number!',\r\n                    },\r\n                    {\r\n                        min: mobileDigit,\r\n                        message: `Mobile no must be ${mobileDigit} characters.`,\r\n                    },\r\n                    {\r\n                        max: mobileDigit,\r\n                        message: `Mobile no must be ${mobileDigit} characters.`,\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: 'user_email',\r\n                label: 'Email',\r\n                placeholder: 'Eg: <EMAIL>',\r\n                required: !frBulkUpdate,\r\n                rules: [\r\n                    {\r\n                        type: 'email',\r\n                    },\r\n                ],\r\n            },\r\n            ...(userData?.verticals_list?.length > 0 ||\r\n            (frExportModal && ConfigHelpers.isServiceProvider())\r\n                ? [\r\n                      {\r\n                          key: 'user_vertical',\r\n                          label: 'Verticals',\r\n                          widget: 'select',\r\n                          widgetProps: {\r\n                              mode: 'multiple',\r\n                              optionFilterProp: 'children',\r\n                              onChange: (val) => {\r\n                                  if (refreshUI) {\r\n                                      refreshUI();\r\n                                  }\r\n                              },\r\n                          },\r\n                          options: userData?.verticals_list,\r\n                      },\r\n                  ]\r\n                : []),\r\n            ...(ConfigHelpers.isServiceProvider()\r\n                ? srvcTypeFieldBasedFieldSelectors\r\n                : []),\r\n            ...(userData?.srvc_type_list?.length > 0 ||\r\n            frExportModal ||\r\n            frBulkUpdate\r\n                ? [\r\n                      {\r\n                          key: 'srvc_types_assigned_to_user',\r\n                          label: 'Service Types',\r\n                          widget: 'select',\r\n                          widgetProps: {\r\n                              mode: 'multiple',\r\n                              optionFilterProp: 'children',\r\n                              onChange: (val) => {\r\n                                  if (refreshUI) {\r\n                                      refreshUI();\r\n                                  }\r\n                              },\r\n                          },\r\n                          options: userData?.srvc_type_list,\r\n                      },\r\n                  ]\r\n                : []),\r\n            ...(!ConfigHelpers.isServiceProvider()\r\n                ? srvcTypeFieldBasedFieldSelectors\r\n                : []),\r\n            ...usrCustomFieldsMetaJson,\r\n        ],\r\n    };\r\n    return meta;\r\n};\r\n\r\nexport const getMetaFrBulkUpdate = (\r\n    configData,\r\n    usersData,\r\n    orgSettingsData = undefined\r\n) => {\r\n    const setShowAlertMessage = (value) => {\r\n        this.setState({\r\n            showAlertMessage: true,\r\n            isActiveToggleValue: value,\r\n        });\r\n    };\r\n\r\n    let userConfigData = [];\r\n    if (configData) {\r\n        userConfigData = configData[0];\r\n    }\r\n\r\n    const meta = {\r\n        columns: 1,\r\n        formItemLayout: null,\r\n        fields: [\r\n            {\r\n                key: 'user_id',\r\n                label: 'User ID',\r\n                required: true,\r\n            },\r\n            ...getUsersInfoMeta(\r\n                userConfigData,\r\n                usersData,\r\n                true,\r\n                false,\r\n                undefined,\r\n                undefined,\r\n                false,\r\n                undefined,\r\n                orgSettingsData\r\n            ).fields,\r\n            {\r\n                key: 'password',\r\n                label: 'Password',\r\n                placeholder: 'Enter your password',\r\n                widget: 'password',\r\n                required: false,\r\n            },\r\n            {\r\n                key: 'user_confirm_pass',\r\n                label: 'Confirm Password',\r\n                widget: 'password',\r\n                // hasFeedback:true,\r\n                placeholder: 'Enter your confirm password',\r\n                required: false,\r\n                rules: [\r\n                    ({ getFieldValue }) => ({\r\n                        validator(_, value) {\r\n                            if (!value || getFieldValue('password') === value) {\r\n                                return Promise.resolve();\r\n                            }\r\n                            return Promise.reject(\r\n                                new Error(\r\n                                    'The password and confirm password do not match !'\r\n                                )\r\n                            );\r\n                        },\r\n                    }),\r\n                ],\r\n            },\r\n            ...getUsersActiveInfoMeta(usersData).fields,\r\n        ],\r\n    };\r\n    return meta;\r\n};\r\n\r\nexport const getAddressObj = (selectedPlace) => {\r\n    const { name, address_components, geometry } = selectedPlace;\r\n    let selectedPayload = { location: {} };\r\n    selectedPayload.building = name;\r\n\r\n    const typeToKeyMapping = {\r\n        country: 'country',\r\n        administrative_area_level_1: 'state',\r\n        locality: 'city',\r\n        sublocality_level_2: 'street',\r\n        postal_code: 'postalCode',\r\n        neighborhood: 'building',\r\n    };\r\n\r\n    for (const component of address_components) {\r\n        const type = component.types[0];\r\n        const key = typeToKeyMapping[type];\r\n        if (key) {\r\n            selectedPayload[key] = component.long_name;\r\n        }\r\n    }\r\n\r\n    if (geometry && geometry.location) {\r\n        const { lat, lng } = geometry.location;\r\n        const latLng =\r\n            typeof lat === 'function'\r\n                ? { lat: lat(), lng: lng() }\r\n                : { lat, lng };\r\n        selectedPayload.location.latlng = latLng;\r\n    }\r\n\r\n    return selectedPayload;\r\n};\r\n\r\nexport const getConcatenatedAddressFormData = (\r\n    prefix = 'cust_',\r\n    formData,\r\n    formRef\r\n) => {\r\n    if (!formRef.current) {\r\n        let addressFieldKeys = getAddressFieldKeys(prefix);\r\n        return addressFieldKeys.filter((key) => formData?.[key]).toString();\r\n    }\r\n};\r\n\r\nexport const getMapAddressToUsersFieldsMeta = ({\r\n    form_data,\r\n    formRef,\r\n    forceUpdateFn,\r\n    showPickOnMapModel,\r\n    togglePickOnMapModel,\r\n    is_pincode_mandatory,\r\n    orgSettingsData,\r\n}) => {\r\n    let filledAddress =\r\n        getConcatenatedAddressFrmForm('cust_', formRef) ||\r\n        getConcatenatedAddressFormData('cust_', form_data, formRef);\r\n    let showClearFieldsButton = filledAddress && filledAddress != '';\r\n    const latitude =\r\n        formRef?.current?.getFieldValue('location_latitude') ||\r\n        form_data?.location_latitude;\r\n    const longitude =\r\n        formRef?.current?.getFieldValue('location_Longitude') ||\r\n        form_data?.location_Longitude;\r\n\r\n    const onLatLngValueChange = async () => {\r\n        const latitude = formRef?.current?.getFieldValue('location_latitude');\r\n        const longitude = formRef?.current?.getFieldValue('location_Longitude');\r\n        if (latitude && longitude) {\r\n            const data = await getAddressBasedOnLatAndLng(latitude, longitude);\r\n            addressFill(getAddressObj(data.results?.[0]), formRef);\r\n        }\r\n    };\r\n    let clearGoogleAddressSearch = new Date().getTime();\r\n\r\n    const clearAddress = (formRef) => {\r\n        const keyEmptyValue = {};\r\n        getAddressFieldKeys('cust_').forEach((singleKey) => {\r\n            keyEmptyValue[singleKey] = '';\r\n        });\r\n        clearGoogleAddressSearch = new Date().getTime();\r\n        let result = formRef?.current?.setFieldsValue(keyEmptyValue);\r\n        forceUpdateFn();\r\n    };\r\n    const meta = {\r\n        // colSpan:4,\r\n        formItemLayout: null,\r\n        fields: [\r\n            {\r\n                key: 'mark_location_on_map',\r\n                render: () => {\r\n                    return (\r\n                        <div>\r\n                            <LocationSearchInput\r\n                                placeholder=\"Address\"\r\n                                useCountryAndID={true}\r\n                                onChange={(address) => {\r\n                                    addressFill(address, formRef);\r\n                                    forceUpdateFn();\r\n                                }}\r\n                                orgSettingsData={orgSettingsData}\r\n                                triggerClear={clearGoogleAddressSearch}\r\n                            />\r\n                            <Button\r\n                                onClick={() => {\r\n                                    togglePickOnMapModel();\r\n                                }}\r\n                            >\r\n                                Pick on Map\r\n                            </Button>\r\n                            {showPickOnMapModel && (\r\n                                <MapComponent\r\n                                    showPickOnMapModel={showPickOnMapModel}\r\n                                    defaultLocation={{\r\n                                        lat: latitude || 22.5437692,\r\n                                        lng: longitude || 79.1230844,\r\n                                    }}\r\n                                    onChange={(address) => {\r\n                                        addressFill(address, formRef);\r\n                                        forceUpdateFn();\r\n                                        togglePickOnMapModel();\r\n                                    }}\r\n                                    togglePickOnMapModel={() =>\r\n                                        togglePickOnMapModel()\r\n                                    }\r\n                                />\r\n                            )}\r\n                        </div>\r\n                    );\r\n                },\r\n            },\r\n            {\r\n                key: 'clear_fields',\r\n                colSpan: 4,\r\n                label: 'Clear fields',\r\n                render() {\r\n                    return (\r\n                        <>\r\n                            {showClearFieldsButton && (\r\n                                <Button\r\n                                    type=\"link\"\r\n                                    onClick={() => {\r\n                                        clearAddress(formRef);\r\n                                    }}\r\n                                >\r\n                                    Reset Address\r\n                                </Button>\r\n                            )}\r\n                        </>\r\n                    );\r\n                },\r\n            },\r\n            {\r\n                key: 'cust_line_0',\r\n                colSpan: 4,\r\n                label: 'Flat no',\r\n                onChange: () => {\r\n                    forceUpdateFn();\r\n                },\r\n\r\n                rules: [\r\n                    {\r\n                        max: 50,\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: 'cust_line_1',\r\n                colSpan: 4,\r\n                label: 'Building/Apartment name',\r\n                onChange: () => {\r\n                    forceUpdateFn();\r\n                },\r\n                rules: [\r\n                    {\r\n                        max: 200,\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: 'cust_line_2',\r\n                label: 'Line 1',\r\n                colSpan: 4,\r\n                disabled: true,\r\n                rules: [\r\n                    {\r\n                        max: 1000,\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: 'cust_line_3',\r\n                label: 'Line 2',\r\n                colSpan: 4,\r\n                disabled: true,\r\n                rules: [\r\n                    {\r\n                        max: 200,\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: 'cust_pincode',\r\n                label: 'Pincode',\r\n                colSpan: 2,\r\n                required: is_pincode_mandatory,\r\n                // widget: RemoteSourceSelect,\r\n                disabled: true,\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    url: '/searcher',\r\n                    // placeholder:\"Start typing..\",\r\n                    params: {\r\n                        fn: 'getPincode',\r\n                    },\r\n                    widgetProps: {\r\n                        mode: 'single',\r\n                        labelInValue: false,\r\n                        showSearch: true,\r\n                        style: {\r\n                            width: '100%',\r\n                        },\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: 'cust_city',\r\n                label: 'City',\r\n                colSpan: 2,\r\n                // widget: RemoteSourceSelect,\r\n                disabled: true,\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    url: '/searcher',\r\n                    // placeholder:\"Start typing..\",\r\n                    params: {\r\n                        fn: 'getCities',\r\n                    },\r\n                    widgetProps: {\r\n                        mode: 'single',\r\n                        labelInValue: false,\r\n                        showSearch: true,\r\n                        style: {\r\n                            width: '100%',\r\n                        },\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: 'cust_state',\r\n                label: 'State',\r\n                colSpan: 4,\r\n                // widget: RemoteSourceSelect,\r\n                disabled: true,\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    url: '/searcher',\r\n                    // placeholder:\"Start typing..\",\r\n                    params: {\r\n                        fn: 'getState',\r\n                    },\r\n                    widgetProps: {\r\n                        mode: 'single',\r\n                        labelInValue: false,\r\n                        showSearch: true,\r\n                        style: {\r\n                            width: '100%',\r\n                        },\r\n                    },\r\n                },\r\n            },\r\n            {\r\n                key: 'location_latitude',\r\n                label: 'Latitude',\r\n                colSpan: 4,\r\n                required: formRef?.current?.getFieldValue('location_Longitude'),\r\n                placeholder: 'Eg 37.7749',\r\n                onChange: (e) => {\r\n                    onLatLngValueChange();\r\n                    forceUpdateFn();\r\n                },\r\n            },\r\n            {\r\n                key: 'location_Longitude',\r\n                label: 'Longitude',\r\n                colSpan: 4,\r\n                required: formRef?.current?.getFieldValue('location_latitude'),\r\n                placeholder: 'Eg -122.4194',\r\n                onChange: (e) => {\r\n                    onLatLngValueChange();\r\n                    forceUpdateFn();\r\n                },\r\n            },\r\n            ...(latitude && longitude\r\n                ? [\r\n                      {\r\n                          key: 'view_location',\r\n                          render() {\r\n                              let url = `https://www.google.com/maps/search/?api=1&query=${latitude},${longitude}`;\r\n                              return (\r\n                                  <a href={url} target=\"_blank\">\r\n                                      <i class=\"icon icon-location\"> </i>View on\r\n                                      Google Map\r\n                                  </a>\r\n                              );\r\n                          },\r\n                      },\r\n                  ]\r\n                : []),\r\n        ],\r\n    };\r\n    return meta;\r\n};\r\n\r\nexport const getCapacityFieldsMeta = (\r\n    getForm,\r\n    verticals_list,\r\n    capacityConfigs,\r\n    handleVerticalChange,\r\n    fieldSetDetails,\r\n    keys\r\n) => {\r\n    const { label, value } = fieldSetDetails;\r\n    const meta = {\r\n        // colSpan:4,\r\n        formItemLayout: null,\r\n        fields: [\r\n            {\r\n                key: keys.vertical_key,\r\n                label: `${label} Vertical`,\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                    allowClear: true,\r\n                    style: { width: '100%' },\r\n                },\r\n                onChange: (value) => {\r\n                    // If field is cleared, set it to empty string directly\r\n                    if (value === undefined) {\r\n                        getForm().setFieldsValue({\r\n                            [keys.vertical_key]: '',\r\n                        });\r\n                    }\r\n\r\n                    // Handle vertical change and clear dependent fields\r\n                    handleVerticalChange(value, keys);\r\n                    getForm().setFieldsValue({\r\n                        [keys.srvc_hub_key]: '',\r\n                        [keys.skill_key_1]: '',\r\n                        [keys.skill_key_2]: '',\r\n                        [keys.skill_key_3]: '',\r\n                    });\r\n                },\r\n                options: verticals_list,\r\n            },\r\n            {\r\n                key: keys.srvc_hub_key,\r\n                label: `${label} Service Hub`,\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                    allowClear: true,\r\n                    style: { width: '100%' },\r\n                },\r\n                onChange: (value) => {\r\n                    // If field is cleared, set it to empty string directly\r\n                    if (value === undefined) {\r\n                        getForm().setFieldsValue({\r\n                            [keys.srvc_hub_key]: '',\r\n                        });\r\n                    }\r\n                },\r\n                options: capacityConfigs.srvc_hub_list,\r\n            },\r\n            {\r\n                key: keys.skill_key_1,\r\n                label: `${label} Skill 1`,\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                    allowClear: true,\r\n                    style: { width: '100%' },\r\n                },\r\n                onChange: (value) => {\r\n                    // If field is cleared, set it to empty string directly\r\n                    if (value === undefined) {\r\n                        getForm().setFieldsValue({\r\n                            [keys.skill_key_1]: '',\r\n                        });\r\n                    }\r\n                },\r\n                options: capacityConfigs.skills,\r\n                tooltip: 'Will get jobs for this first',\r\n                // antd rule to make sure that this skill is not selected in 2 & 3\r\n                rules: [\r\n                    {\r\n                        validator: (_, value) => {\r\n                            let skill2 = getForm().getFieldValue(\r\n                                keys.skill_key_2\r\n                            );\r\n                            let skill3 = getForm().getFieldValue(\r\n                                keys.skill_key_3\r\n                            );\r\n                            if (value && (value == skill2 || value == skill3)) {\r\n                                return Promise.reject(\r\n                                    new Error('Skill already selected')\r\n                                );\r\n                            }\r\n                            return Promise.resolve();\r\n                        },\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: keys.skill_key_2,\r\n                label: `${label} Skill 2`,\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                    allowClear: true,\r\n                    style: { width: '100%' },\r\n                },\r\n                onChange: (value) => {\r\n                    // If field is cleared, set it to empty string directly\r\n                    if (value === undefined) {\r\n                        getForm().setFieldsValue({\r\n                            [keys.skill_key_2]: '',\r\n                        });\r\n                    }\r\n                },\r\n                options: capacityConfigs.skills,\r\n                tooltip: 'Will get jobs for this if no job for skill 1',\r\n                // antd rule to make sure that this skill is not selected in 1 & 3\r\n                rules: [\r\n                    {\r\n                        validator: (_, value) => {\r\n                            let skill1 = getForm().getFieldValue(\r\n                                keys.skill_key_1\r\n                            );\r\n                            let skill3 = getForm().getFieldValue(\r\n                                keys.skill_key_3\r\n                            );\r\n                            if (value && (value == skill1 || value == skill3)) {\r\n                                return Promise.reject(\r\n                                    new Error(\r\n                                        'Skill already selected, you can keep this blank not mandatory!'\r\n                                    )\r\n                                );\r\n                            }\r\n                            return Promise.resolve();\r\n                        },\r\n                    },\r\n                ],\r\n            },\r\n            {\r\n                key: keys.skill_key_3,\r\n                label: `${label} Skill 3`,\r\n                widget: 'select',\r\n                widgetProps: {\r\n                    mode: 'single',\r\n                    showSearch: true,\r\n                    optionFilterProp: 'children',\r\n                    allowClear: true,\r\n                    style: { width: '100%' },\r\n                },\r\n                onChange: (value) => {\r\n                    // If field is cleared, set it to empty string directly\r\n                    if (value === undefined) {\r\n                        getForm().setFieldsValue({\r\n                            [keys.skill_key_3]: '',\r\n                        });\r\n                    }\r\n                },\r\n                options: capacityConfigs.skills,\r\n                tooltip: 'Will get jobs for this if no job for skill 2',\r\n                // antd rule to make sure that this skill is not selected in 1 & 2\r\n                rules: [\r\n                    {\r\n                        validator: (_, value) => {\r\n                            let skill1 = getForm().getFieldValue(\r\n                                keys.skill_key_1\r\n                            );\r\n                            let skill2 = getForm().getFieldValue(\r\n                                keys.skill_key_2\r\n                            );\r\n                            if (value && (value == skill1 || value == skill2)) {\r\n                                return Promise.reject(\r\n                                    new Error(\r\n                                        'Skill already selected, you can keep this blank not mandatory!'\r\n                                    )\r\n                                );\r\n                            }\r\n                            return Promise.resolve();\r\n                        },\r\n                    },\r\n                ],\r\n            },\r\n        ],\r\n    };\r\n    return meta;\r\n};\r\n\r\nexport const retrieveSelectedFormData = (\r\n    srvcTypeOrVerticalList,\r\n    singleSelectedSrvcTypeOrVertical\r\n) => {\r\n    const formData = srvcTypeOrVerticalList.filter((eachData) => {\r\n        return eachData.value == singleSelectedSrvcTypeOrVertical;\r\n    });\r\n    return formData[0];\r\n};\r\n\r\nexport const getSrvcCategoryFieldOptions = (formData, isServiceProvider) => {\r\n    let categoryFieldKey;\r\n    if (isServiceProvider) {\r\n        categoryFieldKey = 'srvc_prvdr_category_field';\r\n    } else {\r\n        categoryFieldKey = 'srvc_category_field';\r\n    }\r\n\r\n    const originalFields = decodeFieldsMetaFrmJson(\r\n        isServiceProvider\r\n            ? formData?.sp_cust_fields_json\r\n            : formData?.srvc_cust_fields_json\r\n    );\r\n    const categoryFieldValue = formData?.[categoryFieldKey];\r\n    const srvcCategoryFieldOptions = originalFields.filter((originalField) => {\r\n        return originalField.key == categoryFieldValue;\r\n    });\r\n    return srvcCategoryFieldOptions[0]?.options;\r\n};\r\n\r\nexport const formatDate = (dateString) => {\r\n    try {\r\n        const date = new Date(dateString);\r\n        const month = date.toLocaleString('default', { month: 'short' });\r\n        const day = date.getDate();\r\n        // Replace spaces with hyphens\r\n        const formattedDate = `${day} ${month}`;\r\n        return formattedDate;\r\n    } catch (error) {\r\n        //swallow\r\n    }\r\n    return dateString;\r\n};\r\n\r\nexport const removeAndAppendSixDigitIntegers = (inputString) => {\r\n    try {\r\n        const regex = /\\b\\d{6}, /; // Matches consecutive 6-digit numbers followed by a comma and a space\r\n        const matches = inputString.match(regex);\r\n        if (!matches) {\r\n            return inputString; // No matches found, return the input as is\r\n        }\r\n        // Remove the matched 6-digit integers from the input string\r\n        const stringWithoutIntegers = inputString.replace(regex, '').trim();\r\n        // Remove any double commas, remove the comma following the 6-digit numbers, and then append the matched 6-digit integers at the end, separated by a single comma\r\n        const cleanedString = stringWithoutIntegers.replace(/,+/g, ',').trim();\r\n        const appendedString =\r\n            cleanedString.replace(/,\\s*$/, '') + ', ' + matches.join(', ');\r\n        return appendedString.slice(0, -2);\r\n    } catch (error) {\r\n        //swallow\r\n    }\r\n    return inputString;\r\n};\r\n"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,kDAAkD;AAC1F,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,MAAM;AAC7B,SACIC,WAAW,EACXC,0BAA0B,EAC1BC,mBAAmB,EACnBC,6BAA6B,QAC1B,4BAA4B;AACnC,OAAOC,mBAAmB,MAAM,sCAAsC;AACtE,OAAOC,kBAAkB,MAAM,gDAAgD;AAC/E,OAAOC,YAAY,MAAM,iDAAiD;AAC1E,SAASC,iBAAiB,QAAQ,oBAAoB;AAEtD,OAAO,MAAMC,sBAAsB,GAAGA,CAClCC,QAAQ,EACRC,mBAAmB,EACnBC,YAAY,GAAG,KAAK,KACnB;EAAA,IAAAC,mBAAA;EACD,MAAMC,IAAI,GAAG;IACTC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,CACJ;MACIC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE,QAAQ;MAChBC,YAAY,EAAE,CAAAX,QAAQ,aAARA,QAAQ,wBAAAG,mBAAA,GAARH,QAAQ,CAAEY,SAAS,cAAAT,mBAAA,uBAAnBA,mBAAA,CAAqBU,SAAS,KAAI,IAAI;MACpDC,QAAQ,EAAGC,KAAK,IAAK;QACjB,IAAI,CAACb,YAAY,EAAE;UACfD,mBAAmB,CAACc,KAAK,CAAC;QAC9B;MACJ;IACJ,CAAC;EAET,CAAC;EACD,OAAOX,IAAI;AACf,CAAC;AACD,OAAO,MAAMY,wBAAwB,GAAGA,CAAA,KAAM;EAC1C,MAAMZ,IAAI,GAAG;IACTC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,CACJ;MACIC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE;IACX,CAAC;EAET,CAAC;EACD,OAAOL,IAAI;AACf,CAAC;AAED,OAAO,MAAMa,iBAAiB,GAAGA,CAACC,OAAO,EAAElB,QAAQ,EAAEmB,WAAW,KAAK;EAAA,IAAAC,gBAAA,EAAAC,oBAAA;EACjE,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,gBAAgB,GAChB,CAAAL,OAAO,aAAPA,OAAO,wBAAAE,gBAAA,GAAPF,OAAO,CAAEM,OAAO,cAAAJ,gBAAA,uBAAhBA,gBAAA,CAAkBK,aAAa,CAAC,WAAW,CAAC,MAC5CzB,QAAQ,aAARA,QAAQ,wBAAAqB,oBAAA,GAARrB,QAAQ,CAAEY,SAAS,cAAAS,oBAAA,uBAAnBA,oBAAA,CAAqBK,SAAS;EAClC,IAAIH,gBAAgB,KAAIvB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B,QAAQ,GAAE;IACxCL,eAAe,GAAGtB,QAAQ,CAAC2B,QAAQ,CAACC,MAAM,CACrCC,IAAI,IACDN,gBAAgB,CAACO,QAAQ,CAACD,IAAI,CAACd,KAAK,CAAC,IACrCc,IAAI,CAACd,KAAK,IAAII,WACtB,CAAC;EACL;EACA,OAAOG,eAAe;AAC1B,CAAC;AAED,OAAO,MAAMS,gBAAgB,GAAGA,CAC5BC,cAAc,EACdhC,QAAQ,EACRE,YAAY,GAAG,KAAK,EACpB+B,aAAa,GAAG,KAAK,EACrBC,SAAS,GAAGC,SAAS,EACrBC,4BAA4B,EAC5BC,iBAAiB,GAAG,KAAK,EACzBnB,OAAO,EACPoB,eAAe,EACfC,QAAQ,KACP;EAAA,IAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;EACD;EACA,MAAMC,WAAW,GAAGX,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEY,YAAY;EACjD,MAAMC,WAAW,GAAGb,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAEc,YAAY;EAEjD,IAAIC,qBAAqB,GAAG,EAAE;EAC9B,IAAIjE,aAAa,CAACiD,iBAAiB,CAAC,CAAC,EAAE;IACnCgB,qBAAqB,CAACC,IAAI,CAAC;MACvB9C,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE,2EAA2E;MAClFC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,UAAU;QAChBC,gBAAgB,EAAE,UAAU;QAC5B3C,QAAQ,EAAGC,KAAK,IAAK;UACjB;QAAA;MAER,CAAC;MACD2C,OAAO,EAAE1D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2D,kBAAkB;MACrCC,WAAW,EAAE;IACjB,CAAC,CAAC;EACN;EAEA,IAAIC,uBAAuB,GAAG,EAAE;EAChC,IAAI7B,cAAc,EAAE;IAChB6B,uBAAuB,GAAG1E,uBAAuB,CAC7C2E,IAAI,CAACC,SAAS,CAAC/B,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEjB,KAAK,CAAC,EACrC,CACJ,CAAC;EACL;EACA,IAAIiD,gCAAgC,GAAG3B,iBAAiB,GAClDrC,QAAQ,aAARA,QAAQ,wBAAAwC,oBAAA,GAARxC,QAAQ,CAAEY,SAAS,cAAA4B,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqB5B,SAAS,cAAA6B,qBAAA,uBAA9BA,qBAAA,CAAgCwB,aAAa,GAC7CjE,QAAQ,aAARA,QAAQ,wBAAA0C,oBAAA,GAAR1C,QAAQ,CAAEY,SAAS,cAAA8B,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqB9B,SAAS,cAAA+B,qBAAA,uBAA9BA,qBAAA,CAAgCuB,2BAA2B;EAEjE,MAAMC,gCAAgC,GAAG,EAAE;EAC3C,IAAI/B,4BAA4B,IAAI4B,gCAAgC,EAAE;IAClE,IAAII,sBAAsB,GAAG,EAAE;IAC/B,IAAI/B,iBAAiB,EAAE;MACnB+B,sBAAsB,GAAGpE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqE,cAAc;IACrD,CAAC,MAAM;MACHD,sBAAsB,GAAGpE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsE,cAAc;IACrD;IAEA,IAAIC,mCAAmC,GAAGnC,4BAA4B,GAChEA,4BAA4B,IAAI,EAAE,GAClC4B,gCAAgC,IAAI,EAAE;IAE5C,IAAI,CAAAO,mCAAmC,aAAnCA,mCAAmC,uBAAnCA,mCAAmC,CAAEC,MAAM,IAAG,CAAC,EAAE;MACjD;MACAD,mCAAmC,aAAnCA,mCAAmC,uBAAnCA,mCAAmC,CAAEE,GAAG,CACnCC,gCAAgC,IAAK;QAAA,IAAAC,oBAAA,EAAAC,qBAAA;QAClC,MAAMC,QAAQ,GAAGC,wBAAwB,CACrCV,sBAAsB,EACtBM,gCACJ,CAAC;QACD,MAAMK,4BAA4B,GAC9BC,2BAA2B,CACvBH,QAAQ,CAACjE,SAAS,EAClByB,iBACJ,CAAC;QACL,IAAI4C,WAAW;QACf,IAAI5C,iBAAiB,EAAE;UACnB4C,WAAW,GAAG,GAAGP,gCAAgC,qBAAqB;QAC1E,CAAC,MAAM;UACHO,WAAW,GAAG,GAAGP,gCAAgC,sBAAsB;QAC3E;QACAK,4BAA4B,IACxBZ,gCAAgC,CAACb,IAAI,CAAC;UAClC9C,GAAG,EAAEyE,WAAW;UAChBxE,KAAK,EAAE,qBAAqBoE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpE,KAAK,IAAI4B,iBAAiB,GAAG,UAAU,GAAG,cAAc,EAAE;UAChG3B,MAAM,EAAE,QAAQ;UAChB6C,WAAW,EAAE;YACTC,IAAI,EAAE,UAAU;YAChB0B,UAAU,EAAE,IAAI;YAChBpE,QAAQ,EAAEA,CAAA,KAAM;cACZ,IAAIoB,SAAS,EAAE;gBACXA,SAAS,CAAC,CAAC;cACf;YACJ;UACJ,CAAC;UACDwB,OAAO,EAAEqB;QACb,CAAC,CAAC;QAEN,MAAMI,iBAAiB,GAAGA,CAAA,KAAM;UAAA,IAAAC,iBAAA;UAC5B,IAAIC,eAAe,GACfnE,OAAO,aAAPA,OAAO,wBAAAkE,iBAAA,GAAPlE,OAAO,CAAEM,OAAO,cAAA4D,iBAAA,uBAAhBA,iBAAA,CAAkB3D,aAAa,CAACwD,WAAW,CAAC;UAChD,OAAOI,eAAe;QAC1B,CAAC;QAED,IAAIC,gBAAgB,GAAGjD,iBAAiB,GAClC,gBAAgB,GAChB,iBAAiB;QACvB,MAAMkD,iBAAiB,GAAG,CACtB;UACIxE,KAAK,EAAE,GAAGuE,gBAAgB,IAAI;UAC9B7E,KAAK,EAAE;QACX,CAAC,EACD;UAAEM,KAAK,EAAE,GAAGuE,gBAAgB,IAAI;UAAE7E,KAAK,EAAE;QAAI,CAAC,EAC9C;UAAEM,KAAK,EAAE,GAAGuE,gBAAgB,IAAI;UAAE7E,KAAK,EAAE;QAAI,CAAC,EAC9C;UAAEM,KAAK,EAAE,GAAGuE,gBAAgB,IAAI;UAAE7E,KAAK,EAAE;QAAI,CAAC,EAC9C;UAAEM,KAAK,EAAE,GAAGuE,gBAAgB,IAAI;UAAE7E,KAAK,EAAE;QAAY,CAAC,CACzD;QACD,IAAI+E,uBAAuB,GAAGL,iBAAiB,CAAC,CAAC,GAC3CA,iBAAiB,CAAC,CAAC,GACnBnF,QAAQ,aAARA,QAAQ,wBAAA2E,oBAAA,GAAR3E,QAAQ,CAAEY,SAAS,cAAA+D,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqB/D,SAAS,cAAAgE,qBAAA,uBAA9BA,qBAAA,CAAiCK,WAAW,CAAC;QACnD,IACIO,uBAAuB,IACvBT,4BAA4B,EAC9B;UACE;UACAS,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEf,GAAG,CAAEgB,oBAAoB,IAAK;YACnD,MAAMC,mBAAmB,GACrBD,oBAAoB,CAAC1E,KAAK,GACpBgE,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAEY,IAAI,CAC7BC,MAAM,IACHA,MAAM,CAAC7E,KAAK,KACZ0E,oBAAoB,CAAC1E,KAC7B,CAAC,GACDgE,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAEY,IAAI,CAC7BC,MAAM,IACHA,MAAM,CAAC7E,KAAK,KACZ0E,oBACR,CAAC;YACXtB,gCAAgC,CAACb,IAAI,CAAC;cAClC9C,GAAG,EAAE,mBAAmBkF,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEjF,KAAK,OAAOoE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpE,KAAK,EAAE;cAC1EA,KAAK,EAAE,oBAAoBiF,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEjF,KAAK,OAAOoE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpE,KAAK,IAAI4B,iBAAiB,GAAG,UAAU,GAAG,cAAc,EAAE;cAChI3B,MAAM,EAAE,QAAQ;cAChB6C,WAAW,EAAE;gBACTC,IAAI,EAAE,EAAE;gBACR0B,UAAU,EAAE,IAAI;gBAChBpE,QAAQ,EAAGC,KAAK,IAAK;kBACjBjB,iBAAiB,CACbiB,KAAK,EACLG,OAAO,EACP,mBAAmBwE,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAEjF,KAAK,OAAOoE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEpE,KAAK,EACvE,CAAC;gBACL;cACJ,CAAC;cACDiD,OAAO,EAAE6B;YACb,CAAC,CAAC;UACN,CAAC,CAAC;QACN;MACJ,CACJ,CAAC;IACL;EACJ;EACA,MAAMpE,WAAW,GACb,CAAAD,OAAO,aAAPA,OAAO,wBAAA0B,iBAAA,GAAP1B,OAAO,CAAEM,OAAO,cAAAoB,iBAAA,uBAAhBA,iBAAA,CAAkBnB,aAAa,CAAC,mBAAmB,CAAC,MACpDzB,QAAQ,aAARA,QAAQ,wBAAA6C,oBAAA,GAAR7C,QAAQ,CAAEY,SAAS,cAAAiC,oBAAA,wBAAAC,qBAAA,GAAnBD,oBAAA,CAAqBjC,SAAS,cAAAkC,qBAAA,uBAA9BA,qBAAA,CAAgC+C,iBAAiB;EACrD,MAAMC,kBAAkB,GAAG7E,iBAAiB,CACxCC,OAAO,EACPlB,QAAQ,EACRmB,WACJ,CAAC;EACD,MAAM4E,gCAAgC,GAAG,CAAAD,kBAAkB,aAAlBA,kBAAkB,uBAAlBA,kBAAkB,CAAEtB,MAAM,IAAG,CAAC;EAEvE,IAAIwB,eAAe,GACfzD,QAAQ,IAAIwD,gCAAgC,GACtC,CACI;IACIvF,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,qBAAqB;IAC5BC,MAAM,EAAE,QAAQ;IAChBuF,QAAQ,EAAE,CAAC/F,YAAY;IACvB0D,WAAW,EAAE,4BAA4B;IACzCF,OAAO,EAAEoC,kBAAkB;IAC3BhF,QAAQ,EAAEA,CAAA,KAAM;MACZ,IAAIoB,SAAS,EAAE;QACXA,SAAS,CAAC,CAAC;MACf;IACJ;EACJ,CAAC,CACJ,GACD,EAAE;EAEZ,MAAM9B,IAAI,GAAG;IACTC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,CACJ;MACIC,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,WAAW;MAClBmD,WAAW,EAAE,UAAU;MACvBqC,QAAQ,EAAE,CAAC/F,YAAY;MACvBgG,KAAK,EAAE,CACH;QAAEC,GAAG,EAAE,GAAG;QAAEC,OAAO,EAAE;MAAmC,CAAC;IAEjE,CAAC,EACD;MACI5F,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,MAAM;MACbmD,WAAW,EAAE,eAAe;MAC5BsC,KAAK,EAAE,CACH;QACIC,GAAG,EAAE,EAAE;QACPC,OAAO,EAAE;MACb,CAAC;IAET,CAAC,EACD;MACI5F,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE,aAAa;MACpBmD,WAAW,EAAE,0BAA0B;MACvCsC,KAAK,EAAE,CACH;QACIC,GAAG,EAAE,EAAE;QACPC,OAAO,EAAE;MACb,CAAC;IAET,CAAC,EACD;MACI5F,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,sBAAsB;MAC7BC,MAAM,EAAE,QAAQ;MAChBuF,QAAQ,EAAE,CAAC/F,YAAY;MACvBqD,WAAW,EAAE;QACTC,IAAI,EAAE,UAAU;QAChBC,gBAAgB,EAAE,UAAU;QAC5B3C,QAAQ,EAAGC,KAAK,IAAK;UACjB,IAAImB,SAAS,EAAE;YACXA,SAAS,CAAC,CAAC;UACf;UAEA,IAAI,CAACnB,KAAK,CAACe,QAAQ,CAACX,WAAW,CAAC,EAAE;YAC9BD,OAAO,CAACM,OAAO,CAAC6E,cAAc,CAAC;cAC3BR,iBAAiB,EAAE1D;YACvB,CAAC,CAAC;YACF,IAAI,CAAApB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyD,MAAM,KAAI,CAAC,EAAE;cACpBtD,OAAO,CAACM,OAAO,CAAC6E,cAAc,CAAC;gBAC3BR,iBAAiB,EAAE9E,KAAK,CAAC,CAAC;cAC9B,CAAC,CAAC;YACN;UACJ;QACJ;MACJ,CAAC;MACD6C,WAAW,EAAE,6BAA6B;MAC1CF,OAAO,EAAE1D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE2B;IACvB,CAAC,EACD,GAAGqE,eAAe,EAClB;MACIxF,GAAG,EAAE,gBAAgB;MACrBC,KAAK,EAAE,uBAAuB;MAC9BC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,UAAU;QAChBC,gBAAgB,EAAE,UAAU;QAC5B3C,QAAQ,EAAGC,KAAK,IAAK;UACjB;QAAA;MAER,CAAC;MACD2C,OAAO,EAAE1D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsG,UAAU;MAC7B1C,WAAW,EAAE;IACjB,CAAC,EACD,GAAGP,qBAAqB,EACxB;MACI7C,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,qBAAqB;MAC5BC,MAAM,EAAE,QAAQ;MAChBgD,OAAO,EAAE1D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuG,QAAQ;MAAE;MAC7BhD,WAAW,EAAE;QACT;QACAiD,UAAU,EAAE,IAAI;QAChB/C,gBAAgB,EAAE,UAAU;QAC5B3C,QAAQ,EAAGC,KAAK,IAAK;UACjB;QAAA,CACH;QACDmE,UAAU,EAAE;MAChB,CAAC;MACDtB,WAAW,EAAE;IACjB,CAAC,EACD;MACIpD,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE,kBAAkBwC,WAAW,GAAG;MACvCW,WAAW,EAAE,gBAAgB;MAC7BqC,QAAQ,EAAE,CAAC/F,YAAY;MACvBgG,KAAK,EAAE,CACH;QACIO,OAAO,EAAE,IAAIC,MAAM,CAAC,UAAU,CAAC;QAC/BN,OAAO,EAAE;MACb,CAAC,EACD;QACIO,GAAG,EAAExD,WAAW;QAChBiD,OAAO,EAAE,qBAAqBjD,WAAW;MAC7C,CAAC,EACD;QACIgD,GAAG,EAAEhD,WAAW;QAChBiD,OAAO,EAAE,qBAAqBjD,WAAW;MAC7C,CAAC;IAET,CAAC,EACD;MACI3C,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE,OAAO;MACdmD,WAAW,EAAE,oBAAoB;MACjCqC,QAAQ,EAAE,CAAC/F,YAAY;MACvBgG,KAAK,EAAE,CACH;QACIU,IAAI,EAAE;MACV,CAAC;IAET,CAAC,EACD,IAAI,CAAA5G,QAAQ,aAARA,QAAQ,wBAAA+C,qBAAA,GAAR/C,QAAQ,CAAEqE,cAAc,cAAAtB,qBAAA,uBAAxBA,qBAAA,CAA0ByB,MAAM,IAAG,CAAC,IACvCvC,aAAa,IAAI7C,aAAa,CAACiD,iBAAiB,CAAC,CAAE,GAC9C,CACI;MACI7B,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,WAAW;MAClBC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,UAAU;QAChBC,gBAAgB,EAAE,UAAU;QAC5B3C,QAAQ,EAAG+F,GAAG,IAAK;UACf,IAAI3E,SAAS,EAAE;YACXA,SAAS,CAAC,CAAC;UACf;QACJ;MACJ,CAAC;MACDwB,OAAO,EAAE1D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqE;IACvB,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAIjF,aAAa,CAACiD,iBAAiB,CAAC,CAAC,GAC/B8B,gCAAgC,GAChC,EAAE,CAAC,EACT,IAAI,CAAAnE,QAAQ,aAARA,QAAQ,wBAAAgD,qBAAA,GAARhD,QAAQ,CAAEsE,cAAc,cAAAtB,qBAAA,uBAAxBA,qBAAA,CAA0BwB,MAAM,IAAG,CAAC,IACxCvC,aAAa,IACb/B,YAAY,GACN,CACI;MACIM,GAAG,EAAE,6BAA6B;MAClCC,KAAK,EAAE,eAAe;MACtBC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,UAAU;QAChBC,gBAAgB,EAAE,UAAU;QAC5B3C,QAAQ,EAAG+F,GAAG,IAAK;UACf,IAAI3E,SAAS,EAAE;YACXA,SAAS,CAAC,CAAC;UACf;QACJ;MACJ,CAAC;MACDwB,OAAO,EAAE1D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsE;IACvB,CAAC,CACJ,GACD,EAAE,CAAC,EACT,IAAI,CAAClF,aAAa,CAACiD,iBAAiB,CAAC,CAAC,GAChC8B,gCAAgC,GAChC,EAAE,CAAC,EACT,GAAGN,uBAAuB;EAElC,CAAC;EACD,OAAOzD,IAAI;AACf,CAAC;AAED,OAAO,MAAM0G,mBAAmB,GAAGA,CAC/BC,UAAU,EACVC,SAAS,EACT1E,eAAe,GAAGH,SAAS,KAC1B;EACD,MAAMlC,mBAAmB,GAAIc,KAAK,IAAK;IACnC,IAAI,CAACkG,QAAQ,CAAC;MACVC,gBAAgB,EAAE,IAAI;MACtBC,mBAAmB,EAAEpG;IACzB,CAAC,CAAC;EACN,CAAC;EAED,IAAIiB,cAAc,GAAG,EAAE;EACvB,IAAI+E,UAAU,EAAE;IACZ/E,cAAc,GAAG+E,UAAU,CAAC,CAAC,CAAC;EAClC;EAEA,MAAM3G,IAAI,GAAG;IACTC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,CACJ;MACIC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBwF,QAAQ,EAAE;IACd,CAAC,EACD,GAAGlE,gBAAgB,CACfC,cAAc,EACdgF,SAAS,EACT,IAAI,EACJ,KAAK,EACL7E,SAAS,EACTA,SAAS,EACT,KAAK,EACLA,SAAS,EACTG,eACJ,CAAC,CAAC/B,MAAM,EACR;MACIC,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE,UAAU;MACjBmD,WAAW,EAAE,qBAAqB;MAClClD,MAAM,EAAE,UAAU;MAClBuF,QAAQ,EAAE;IACd,CAAC,EACD;MACIzF,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,kBAAkB;MACzBC,MAAM,EAAE,UAAU;MAClB;MACAkD,WAAW,EAAE,6BAA6B;MAC1CqC,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,CACH,CAAC;QAAEzE;MAAc,CAAC,MAAM;QACpB2F,SAASA,CAACC,CAAC,EAAEtG,KAAK,EAAE;UAChB,IAAI,CAACA,KAAK,IAAIU,aAAa,CAAC,UAAU,CAAC,KAAKV,KAAK,EAAE;YAC/C,OAAOuG,OAAO,CAACC,OAAO,CAAC,CAAC;UAC5B;UACA,OAAOD,OAAO,CAACE,MAAM,CACjB,IAAIC,KAAK,CACL,kDACJ,CACJ,CAAC;QACL;MACJ,CAAC,CAAC;IAEV,CAAC,EACD,GAAG1H,sBAAsB,CAACiH,SAAS,CAAC,CAACzG,MAAM;EAEnD,CAAC;EACD,OAAOH,IAAI;AACf,CAAC;AAED,OAAO,MAAMsH,aAAa,GAAIC,aAAa,IAAK;EAC5C,MAAM;IAAEC,IAAI;IAAEC,kBAAkB;IAAEC;EAAS,CAAC,GAAGH,aAAa;EAC5D,IAAII,eAAe,GAAG;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC;EACtCD,eAAe,CAACE,QAAQ,GAAGL,IAAI;EAE/B,MAAMM,gBAAgB,GAAG;IACrBC,OAAO,EAAE,SAAS;IAClBC,2BAA2B,EAAE,OAAO;IACpCC,QAAQ,EAAE,MAAM;IAChBC,mBAAmB,EAAE,QAAQ;IAC7BC,WAAW,EAAE,YAAY;IACzBC,YAAY,EAAE;EAClB,CAAC;EAED,KAAK,MAAMC,SAAS,IAAIZ,kBAAkB,EAAE;IACxC,MAAMjB,IAAI,GAAG6B,SAAS,CAACC,KAAK,CAAC,CAAC,CAAC;IAC/B,MAAMlI,GAAG,GAAG0H,gBAAgB,CAACtB,IAAI,CAAC;IAClC,IAAIpG,GAAG,EAAE;MACLuH,eAAe,CAACvH,GAAG,CAAC,GAAGiI,SAAS,CAACE,SAAS;IAC9C;EACJ;EAEA,IAAIb,QAAQ,IAAIA,QAAQ,CAACE,QAAQ,EAAE;IAC/B,MAAM;MAAEY,GAAG;MAAEC;IAAI,CAAC,GAAGf,QAAQ,CAACE,QAAQ;IACtC,MAAMc,MAAM,GACR,OAAOF,GAAG,KAAK,UAAU,GACnB;MAAEA,GAAG,EAAEA,GAAG,CAAC,CAAC;MAAEC,GAAG,EAAEA,GAAG,CAAC;IAAE,CAAC,GAC1B;MAAED,GAAG;MAAEC;IAAI,CAAC;IACtBd,eAAe,CAACC,QAAQ,CAACe,MAAM,GAAGD,MAAM;EAC5C;EAEA,OAAOf,eAAe;AAC1B,CAAC;AAED,OAAO,MAAMiB,8BAA8B,GAAGA,CAC1CC,MAAM,GAAG,OAAO,EAChBpE,QAAQ,EACR3D,OAAO,KACN;EACD,IAAI,CAACA,OAAO,CAACM,OAAO,EAAE;IAClB,IAAI0H,gBAAgB,GAAGzJ,mBAAmB,CAACwJ,MAAM,CAAC;IAClD,OAAOC,gBAAgB,CAACtH,MAAM,CAAEpB,GAAG,IAAKqE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGrE,GAAG,CAAC,CAAC,CAAC2I,QAAQ,CAAC,CAAC;EACvE;AACJ,CAAC;AAED,OAAO,MAAMC,8BAA8B,GAAGA,CAAC;EAC3CxI,SAAS;EACTM,OAAO;EACPmI,aAAa;EACbC,kBAAkB;EAClBC,oBAAoB;EACpBC,oBAAoB;EACpBlH;AACJ,CAAC,KAAK;EAAA,IAAAmH,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EACF,IAAIC,aAAa,GACbnK,6BAA6B,CAAC,OAAO,EAAEwB,OAAO,CAAC,IAC/C8H,8BAA8B,CAAC,OAAO,EAAEpI,SAAS,EAAEM,OAAO,CAAC;EAC/D,IAAI4I,qBAAqB,GAAGD,aAAa,IAAIA,aAAa,IAAI,EAAE;EAChE,MAAME,QAAQ,GACV,CAAA7I,OAAO,aAAPA,OAAO,wBAAAuI,iBAAA,GAAPvI,OAAO,CAAEM,OAAO,cAAAiI,iBAAA,uBAAhBA,iBAAA,CAAkBhI,aAAa,CAAC,mBAAmB,CAAC,MACpDb,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEoJ,iBAAiB;EAChC,MAAMC,SAAS,GACX,CAAA/I,OAAO,aAAPA,OAAO,wBAAAwI,iBAAA,GAAPxI,OAAO,CAAEM,OAAO,cAAAkI,iBAAA,uBAAhBA,iBAAA,CAAkBjI,aAAa,CAAC,oBAAoB,CAAC,MACrDb,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEsJ,kBAAkB;EAEjC,MAAMC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IAAA,IAAAC,iBAAA,EAAAC,iBAAA;IACpC,MAAMN,QAAQ,GAAG7I,OAAO,aAAPA,OAAO,wBAAAkJ,iBAAA,GAAPlJ,OAAO,CAAEM,OAAO,cAAA4I,iBAAA,uBAAhBA,iBAAA,CAAkB3I,aAAa,CAAC,mBAAmB,CAAC;IACrE,MAAMwI,SAAS,GAAG/I,OAAO,aAAPA,OAAO,wBAAAmJ,iBAAA,GAAPnJ,OAAO,CAAEM,OAAO,cAAA6I,iBAAA,uBAAhBA,iBAAA,CAAkB5I,aAAa,CAAC,oBAAoB,CAAC;IACvE,IAAIsI,QAAQ,IAAIE,SAAS,EAAE;MAAA,IAAAK,aAAA;MACvB,MAAMC,IAAI,GAAG,MAAM/K,0BAA0B,CAACuK,QAAQ,EAAEE,SAAS,CAAC;MAClE1K,WAAW,CAACmI,aAAa,EAAA4C,aAAA,GAACC,IAAI,CAACC,OAAO,cAAAF,aAAA,uBAAZA,aAAA,CAAe,CAAC,CAAC,CAAC,EAAEpJ,OAAO,CAAC;IAC1D;EACJ,CAAC;EACD,IAAIuJ,wBAAwB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAEnD,MAAMC,YAAY,GAAI1J,OAAO,IAAK;IAAA,IAAA2J,iBAAA;IAC9B,MAAMC,aAAa,GAAG,CAAC,CAAC;IACxBrL,mBAAmB,CAAC,OAAO,CAAC,CAACsL,OAAO,CAAEC,SAAS,IAAK;MAChDF,aAAa,CAACE,SAAS,CAAC,GAAG,EAAE;IACjC,CAAC,CAAC;IACFP,wBAAwB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAC/C,IAAIM,MAAM,GAAG/J,OAAO,aAAPA,OAAO,wBAAA2J,iBAAA,GAAP3J,OAAO,CAAEM,OAAO,cAAAqJ,iBAAA,uBAAhBA,iBAAA,CAAkBxE,cAAc,CAACyE,aAAa,CAAC;IAC5DzB,aAAa,CAAC,CAAC;EACnB,CAAC;EACD,MAAMjJ,IAAI,GAAG;IACT;IACAE,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,CACJ;MACIC,GAAG,EAAE,sBAAsB;MAC3B0K,MAAM,EAAEA,CAAA,KAAM;QACV,oBACI7L,KAAA,CAAA8L,aAAA;UAAAC,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBACIpM,KAAA,CAAA8L,aAAA,CAACxL,mBAAmB;UAChBiE,WAAW,EAAC,SAAS;UACrB8H,eAAe,EAAE,IAAK;UACtB5K,QAAQ,EAAG6K,OAAO,IAAK;YACnBpM,WAAW,CAACoM,OAAO,EAAEzK,OAAO,CAAC;YAC7BmI,aAAa,CAAC,CAAC;UACnB,CAAE;UACF/G,eAAe,EAAEA,eAAgB;UACjCsJ,YAAY,EAAEnB,wBAAyB;UAAAW,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CAC1C,CAAC,eACFpM,KAAA,CAAA8L,aAAA,CAAC7L,MAAM;UACHuM,OAAO,EAAEA,CAAA,KAAM;YACXtC,oBAAoB,CAAC,CAAC;UAC1B,CAAE;UAAA6B,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GACL,aAEO,CAAC,EACRnC,kBAAkB,iBACfjK,KAAA,CAAA8L,aAAA,CAACtL,YAAY;UACTyJ,kBAAkB,EAAEA,kBAAmB;UACvCwC,eAAe,EAAE;YACblD,GAAG,EAAEmB,QAAQ,IAAI,UAAU;YAC3BlB,GAAG,EAAEoB,SAAS,IAAI;UACtB,CAAE;UACFnJ,QAAQ,EAAG6K,OAAO,IAAK;YACnBpM,WAAW,CAACoM,OAAO,EAAEzK,OAAO,CAAC;YAC7BmI,aAAa,CAAC,CAAC;YACfE,oBAAoB,CAAC,CAAC;UAC1B,CAAE;UACFA,oBAAoB,EAAEA,CAAA,KAClBA,oBAAoB,CAAC,CACxB;UAAA6B,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,CACJ,CAEJ,CAAC;MAEd;IACJ,CAAC,EACD;MACIjL,GAAG,EAAE,cAAc;MACnBuL,OAAO,EAAE,CAAC;MACVtL,KAAK,EAAE,cAAc;MACrByK,MAAMA,CAAA,EAAG;QACL,oBACI7L,KAAA,CAAA8L,aAAA,CAAA9L,KAAA,CAAA2M,QAAA,QACKlC,qBAAqB,iBAClBzK,KAAA,CAAA8L,aAAA,CAAC7L,MAAM;UACHsH,IAAI,EAAC,MAAM;UACXiF,OAAO,EAAEA,CAAA,KAAM;YACXjB,YAAY,CAAC1J,OAAO,CAAC;UACzB,CAAE;UAAAkK,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GACL,eAEO,CAEd,CAAC;MAEX;IACJ,CAAC,EACD;MACIjL,GAAG,EAAE,aAAa;MAClBuL,OAAO,EAAE,CAAC;MACVtL,KAAK,EAAE,SAAS;MAChBK,QAAQ,EAAEA,CAAA,KAAM;QACZuI,aAAa,CAAC,CAAC;MACnB,CAAC;MAEDnD,KAAK,EAAE,CACH;QACIC,GAAG,EAAE;MACT,CAAC;IAET,CAAC,EACD;MACI3F,GAAG,EAAE,aAAa;MAClBuL,OAAO,EAAE,CAAC;MACVtL,KAAK,EAAE,yBAAyB;MAChCK,QAAQ,EAAEA,CAAA,KAAM;QACZuI,aAAa,CAAC,CAAC;MACnB,CAAC;MACDnD,KAAK,EAAE,CACH;QACIC,GAAG,EAAE;MACT,CAAC;IAET,CAAC,EACD;MACI3F,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE,QAAQ;MACfsL,OAAO,EAAE,CAAC;MACVE,QAAQ,EAAE,IAAI;MACd/F,KAAK,EAAE,CACH;QACIC,GAAG,EAAE;MACT,CAAC;IAET,CAAC,EACD;MACI3F,GAAG,EAAE,aAAa;MAClBC,KAAK,EAAE,QAAQ;MACfsL,OAAO,EAAE,CAAC;MACVE,QAAQ,EAAE,IAAI;MACd/F,KAAK,EAAE,CACH;QACIC,GAAG,EAAE;MACT,CAAC;IAET,CAAC,EACD;MACI3F,GAAG,EAAE,cAAc;MACnBC,KAAK,EAAE,SAAS;MAChBsL,OAAO,EAAE,CAAC;MACV9F,QAAQ,EAAEuD,oBAAoB;MAC9B;MACAyC,QAAQ,EAAE,IAAI;MACd1I,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACd0I,GAAG,EAAE,WAAW;QAChB;QACAC,MAAM,EAAE;UACJC,EAAE,EAAE;QACR,CAAC;QACD7I,WAAW,EAAE;UACTC,IAAI,EAAE,QAAQ;UACd6I,YAAY,EAAE,KAAK;UACnB7F,UAAU,EAAE,IAAI;UAChB8F,KAAK,EAAE;YACHC,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC,EACD;MACI/L,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,MAAM;MACbsL,OAAO,EAAE,CAAC;MACV;MACAE,QAAQ,EAAE,IAAI;MACd1I,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACd0I,GAAG,EAAE,WAAW;QAChB;QACAC,MAAM,EAAE;UACJC,EAAE,EAAE;QACR,CAAC;QACD7I,WAAW,EAAE;UACTC,IAAI,EAAE,QAAQ;UACd6I,YAAY,EAAE,KAAK;UACnB7F,UAAU,EAAE,IAAI;UAChB8F,KAAK,EAAE;YACHC,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC,EACD;MACI/L,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE,OAAO;MACdsL,OAAO,EAAE,CAAC;MACV;MACAE,QAAQ,EAAE,IAAI;MACd1I,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACd0I,GAAG,EAAE,WAAW;QAChB;QACAC,MAAM,EAAE;UACJC,EAAE,EAAE;QACR,CAAC;QACD7I,WAAW,EAAE;UACTC,IAAI,EAAE,QAAQ;UACd6I,YAAY,EAAE,KAAK;UACnB7F,UAAU,EAAE,IAAI;UAChB8F,KAAK,EAAE;YACHC,KAAK,EAAE;UACX;QACJ;MACJ;IACJ,CAAC,EACD;MACI/L,GAAG,EAAE,mBAAmB;MACxBC,KAAK,EAAE,UAAU;MACjBsL,OAAO,EAAE,CAAC;MACV9F,QAAQ,EAAE/E,OAAO,aAAPA,OAAO,wBAAAyI,iBAAA,GAAPzI,OAAO,CAAEM,OAAO,cAAAmI,iBAAA,uBAAhBA,iBAAA,CAAkBlI,aAAa,CAAC,oBAAoB,CAAC;MAC/DmC,WAAW,EAAE,YAAY;MACzB9C,QAAQ,EAAG0L,CAAC,IAAK;QACbrC,mBAAmB,CAAC,CAAC;QACrBd,aAAa,CAAC,CAAC;MACnB;IACJ,CAAC,EACD;MACI7I,GAAG,EAAE,oBAAoB;MACzBC,KAAK,EAAE,WAAW;MAClBsL,OAAO,EAAE,CAAC;MACV9F,QAAQ,EAAE/E,OAAO,aAAPA,OAAO,wBAAA0I,iBAAA,GAAP1I,OAAO,CAAEM,OAAO,cAAAoI,iBAAA,uBAAhBA,iBAAA,CAAkBnI,aAAa,CAAC,mBAAmB,CAAC;MAC9DmC,WAAW,EAAE,cAAc;MAC3B9C,QAAQ,EAAG0L,CAAC,IAAK;QACbrC,mBAAmB,CAAC,CAAC;QACrBd,aAAa,CAAC,CAAC;MACnB;IACJ,CAAC,EACD,IAAIU,QAAQ,IAAIE,SAAS,GACnB,CACI;MACIzJ,GAAG,EAAE,eAAe;MACpB0K,MAAMA,CAAA,EAAG;QACL,IAAIgB,GAAG,GAAG,mDAAmDnC,QAAQ,IAAIE,SAAS,EAAE;QACpF,oBACI5K,KAAA,CAAA8L,aAAA;UAAGsB,IAAI,EAAEP,GAAI;UAACQ,MAAM,EAAC,QAAQ;UAAAtB,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,gBACzBpM,KAAA,CAAA8L,aAAA;UAAGwB,KAAK,EAAC,oBAAoB;UAAAvB,MAAA;UAAAC,QAAA;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA;QAAA,GAAC,GAAI,CAAC,sBAEpC,CAAC;MAEZ;IACJ,CAAC,CACJ,GACD,EAAE,CAAC;EAEjB,CAAC;EACD,OAAOrL,IAAI;AACf,CAAC;AAED,OAAO,MAAMwM,qBAAqB,GAAGA,CACjCC,OAAO,EACPxI,cAAc,EACdyI,eAAe,EACfC,oBAAoB,EACpBC,eAAe,EACfC,IAAI,KACH;EACD,MAAM;IAAExM,KAAK;IAAEM;EAAM,CAAC,GAAGiM,eAAe;EACxC,MAAM5M,IAAI,GAAG;IACT;IACAE,cAAc,EAAE,IAAI;IACpBC,MAAM,EAAE,CACJ;MACIC,GAAG,EAAEyM,IAAI,CAACC,YAAY;MACtBzM,KAAK,EAAE,GAAGA,KAAK,WAAW;MAC1BC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACdgD,UAAU,EAAE,IAAI;QAChB/C,gBAAgB,EAAE,UAAU;QAC5ByB,UAAU,EAAE,IAAI;QAChBoH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAC3B,CAAC;MACDzL,QAAQ,EAAGC,KAAK,IAAK;QACjB;QACA,IAAIA,KAAK,KAAKoB,SAAS,EAAE;UACrB0K,OAAO,CAAC,CAAC,CAACxG,cAAc,CAAC;YACrB,CAAC4G,IAAI,CAACC,YAAY,GAAG;UACzB,CAAC,CAAC;QACN;;QAEA;QACAH,oBAAoB,CAAChM,KAAK,EAAEkM,IAAI,CAAC;QACjCJ,OAAO,CAAC,CAAC,CAACxG,cAAc,CAAC;UACrB,CAAC4G,IAAI,CAACE,YAAY,GAAG,EAAE;UACvB,CAACF,IAAI,CAACG,WAAW,GAAG,EAAE;UACtB,CAACH,IAAI,CAACI,WAAW,GAAG,EAAE;UACtB,CAACJ,IAAI,CAACK,WAAW,GAAG;QACxB,CAAC,CAAC;MACN,CAAC;MACD5J,OAAO,EAAEW;IACb,CAAC,EACD;MACI7D,GAAG,EAAEyM,IAAI,CAACE,YAAY;MACtB1M,KAAK,EAAE,GAAGA,KAAK,cAAc;MAC7BC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACdgD,UAAU,EAAE,IAAI;QAChB/C,gBAAgB,EAAE,UAAU;QAC5ByB,UAAU,EAAE,IAAI;QAChBoH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAC3B,CAAC;MACDzL,QAAQ,EAAGC,KAAK,IAAK;QACjB;QACA,IAAIA,KAAK,KAAKoB,SAAS,EAAE;UACrB0K,OAAO,CAAC,CAAC,CAACxG,cAAc,CAAC;YACrB,CAAC4G,IAAI,CAACE,YAAY,GAAG;UACzB,CAAC,CAAC;QACN;MACJ,CAAC;MACDzJ,OAAO,EAAEoJ,eAAe,CAACS;IAC7B,CAAC,EACD;MACI/M,GAAG,EAAEyM,IAAI,CAACG,WAAW;MACrB3M,KAAK,EAAE,GAAGA,KAAK,UAAU;MACzBC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACdgD,UAAU,EAAE,IAAI;QAChB/C,gBAAgB,EAAE,UAAU;QAC5ByB,UAAU,EAAE,IAAI;QAChBoH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAC3B,CAAC;MACDzL,QAAQ,EAAGC,KAAK,IAAK;QACjB;QACA,IAAIA,KAAK,KAAKoB,SAAS,EAAE;UACrB0K,OAAO,CAAC,CAAC,CAACxG,cAAc,CAAC;YACrB,CAAC4G,IAAI,CAACG,WAAW,GAAG;UACxB,CAAC,CAAC;QACN;MACJ,CAAC;MACD1J,OAAO,EAAEoJ,eAAe,CAACU,MAAM;MAC/BC,OAAO,EAAE,8BAA8B;MACvC;MACAvH,KAAK,EAAE,CACH;QACIkB,SAAS,EAAEA,CAACC,CAAC,EAAEtG,KAAK,KAAK;UACrB,IAAI2M,MAAM,GAAGb,OAAO,CAAC,CAAC,CAACpL,aAAa,CAChCwL,IAAI,CAACI,WACT,CAAC;UACD,IAAIM,MAAM,GAAGd,OAAO,CAAC,CAAC,CAACpL,aAAa,CAChCwL,IAAI,CAACK,WACT,CAAC;UACD,IAAIvM,KAAK,KAAKA,KAAK,IAAI2M,MAAM,IAAI3M,KAAK,IAAI4M,MAAM,CAAC,EAAE;YAC/C,OAAOrG,OAAO,CAACE,MAAM,CACjB,IAAIC,KAAK,CAAC,wBAAwB,CACtC,CAAC;UACL;UACA,OAAOH,OAAO,CAACC,OAAO,CAAC,CAAC;QAC5B;MACJ,CAAC;IAET,CAAC,EACD;MACI/G,GAAG,EAAEyM,IAAI,CAACI,WAAW;MACrB5M,KAAK,EAAE,GAAGA,KAAK,UAAU;MACzBC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACdgD,UAAU,EAAE,IAAI;QAChB/C,gBAAgB,EAAE,UAAU;QAC5ByB,UAAU,EAAE,IAAI;QAChBoH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAC3B,CAAC;MACDzL,QAAQ,EAAGC,KAAK,IAAK;QACjB;QACA,IAAIA,KAAK,KAAKoB,SAAS,EAAE;UACrB0K,OAAO,CAAC,CAAC,CAACxG,cAAc,CAAC;YACrB,CAAC4G,IAAI,CAACI,WAAW,GAAG;UACxB,CAAC,CAAC;QACN;MACJ,CAAC;MACD3J,OAAO,EAAEoJ,eAAe,CAACU,MAAM;MAC/BC,OAAO,EAAE,8CAA8C;MACvD;MACAvH,KAAK,EAAE,CACH;QACIkB,SAAS,EAAEA,CAACC,CAAC,EAAEtG,KAAK,KAAK;UACrB,IAAI6M,MAAM,GAAGf,OAAO,CAAC,CAAC,CAACpL,aAAa,CAChCwL,IAAI,CAACG,WACT,CAAC;UACD,IAAIO,MAAM,GAAGd,OAAO,CAAC,CAAC,CAACpL,aAAa,CAChCwL,IAAI,CAACK,WACT,CAAC;UACD,IAAIvM,KAAK,KAAKA,KAAK,IAAI6M,MAAM,IAAI7M,KAAK,IAAI4M,MAAM,CAAC,EAAE;YAC/C,OAAOrG,OAAO,CAACE,MAAM,CACjB,IAAIC,KAAK,CACL,gEACJ,CACJ,CAAC;UACL;UACA,OAAOH,OAAO,CAACC,OAAO,CAAC,CAAC;QAC5B;MACJ,CAAC;IAET,CAAC,EACD;MACI/G,GAAG,EAAEyM,IAAI,CAACK,WAAW;MACrB7M,KAAK,EAAE,GAAGA,KAAK,UAAU;MACzBC,MAAM,EAAE,QAAQ;MAChB6C,WAAW,EAAE;QACTC,IAAI,EAAE,QAAQ;QACdgD,UAAU,EAAE,IAAI;QAChB/C,gBAAgB,EAAE,UAAU;QAC5ByB,UAAU,EAAE,IAAI;QAChBoH,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAO;MAC3B,CAAC;MACDzL,QAAQ,EAAGC,KAAK,IAAK;QACjB;QACA,IAAIA,KAAK,KAAKoB,SAAS,EAAE;UACrB0K,OAAO,CAAC,CAAC,CAACxG,cAAc,CAAC;YACrB,CAAC4G,IAAI,CAACK,WAAW,GAAG;UACxB,CAAC,CAAC;QACN;MACJ,CAAC;MACD5J,OAAO,EAAEoJ,eAAe,CAACU,MAAM;MAC/BC,OAAO,EAAE,8CAA8C;MACvD;MACAvH,KAAK,EAAE,CACH;QACIkB,SAAS,EAAEA,CAACC,CAAC,EAAEtG,KAAK,KAAK;UACrB,IAAI6M,MAAM,GAAGf,OAAO,CAAC,CAAC,CAACpL,aAAa,CAChCwL,IAAI,CAACG,WACT,CAAC;UACD,IAAIM,MAAM,GAAGb,OAAO,CAAC,CAAC,CAACpL,aAAa,CAChCwL,IAAI,CAACI,WACT,CAAC;UACD,IAAItM,KAAK,KAAKA,KAAK,IAAI6M,MAAM,IAAI7M,KAAK,IAAI2M,MAAM,CAAC,EAAE;YAC/C,OAAOpG,OAAO,CAACE,MAAM,CACjB,IAAIC,KAAK,CACL,gEACJ,CACJ,CAAC;UACL;UACA,OAAOH,OAAO,CAACC,OAAO,CAAC,CAAC;QAC5B;MACJ,CAAC;IAET,CAAC;EAET,CAAC;EACD,OAAOnH,IAAI;AACf,CAAC;AAED,OAAO,MAAM0E,wBAAwB,GAAGA,CACpCV,sBAAsB,EACtBM,gCAAgC,KAC/B;EACD,MAAMG,QAAQ,GAAGT,sBAAsB,CAACxC,MAAM,CAAEiM,QAAQ,IAAK;IACzD,OAAOA,QAAQ,CAAC9M,KAAK,IAAI2D,gCAAgC;EAC7D,CAAC,CAAC;EACF,OAAOG,QAAQ,CAAC,CAAC,CAAC;AACtB,CAAC;AAED,OAAO,MAAMG,2BAA2B,GAAGA,CAACH,QAAQ,EAAExC,iBAAiB,KAAK;EAAA,IAAAyL,qBAAA;EACxE,IAAIC,gBAAgB;EACpB,IAAI1L,iBAAiB,EAAE;IACnB0L,gBAAgB,GAAG,2BAA2B;EAClD,CAAC,MAAM;IACHA,gBAAgB,GAAG,qBAAqB;EAC5C;EAEA,MAAMC,cAAc,GAAG7O,uBAAuB,CAC1CkD,iBAAiB,GACXwC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoJ,mBAAmB,GAC7BpJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqJ,qBACpB,CAAC;EACD,MAAMC,kBAAkB,GAAGtJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAGkJ,gBAAgB,CAAC;EACvD,MAAMK,wBAAwB,GAAGJ,cAAc,CAACpM,MAAM,CAAEyM,aAAa,IAAK;IACtE,OAAOA,aAAa,CAAC7N,GAAG,IAAI2N,kBAAkB;EAClD,CAAC,CAAC;EACF,QAAAL,qBAAA,GAAOM,wBAAwB,CAAC,CAAC,CAAC,cAAAN,qBAAA,uBAA3BA,qBAAA,CAA6BpK,OAAO;AAC/C,CAAC;AAED,OAAO,MAAM4K,UAAU,GAAIC,UAAU,IAAK;EACtC,IAAI;IACA,MAAMC,IAAI,GAAG,IAAI9D,IAAI,CAAC6D,UAAU,CAAC;IACjC,MAAME,KAAK,GAAGD,IAAI,CAACE,cAAc,CAAC,SAAS,EAAE;MAAED,KAAK,EAAE;IAAQ,CAAC,CAAC;IAChE,MAAME,GAAG,GAAGH,IAAI,CAACI,OAAO,CAAC,CAAC;IAC1B;IACA,MAAMC,aAAa,GAAG,GAAGF,GAAG,IAAIF,KAAK,EAAE;IACvC,OAAOI,aAAa;EACxB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACZ;EAAA;EAEJ,OAAOP,UAAU;AACrB,CAAC;AAED,OAAO,MAAMQ,+BAA+B,GAAIC,WAAW,IAAK;EAC5D,IAAI;IACA,MAAMC,KAAK,GAAG,WAAW,CAAC,CAAC;IAC3B,MAAMC,OAAO,GAAGF,WAAW,CAACG,KAAK,CAACF,KAAK,CAAC;IACxC,IAAI,CAACC,OAAO,EAAE;MACV,OAAOF,WAAW,CAAC,CAAC;IACxB;IACA;IACA,MAAMI,qBAAqB,GAAGJ,WAAW,CAACK,OAAO,CAACJ,KAAK,EAAE,EAAE,CAAC,CAACK,IAAI,CAAC,CAAC;IACnE;IACA,MAAMC,aAAa,GAAGH,qBAAqB,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC;IACtE,MAAME,cAAc,GAChBD,aAAa,CAACF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,IAAI,GAAGH,OAAO,CAACO,IAAI,CAAC,IAAI,CAAC;IAClE,OAAOD,cAAc,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtC,CAAC,CAAC,OAAOZ,KAAK,EAAE;IACZ;EAAA;EAEJ,OAAOE,WAAW;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module"}